#!/usr/bin/env python3
"""
Test License Generator for QuizAIGen
===================================

Generates test license keys for different tiers to test the commercial packages.
"""

import json
import base64
from datetime import datetime, timedelta
from typing import Dict, Any, List
import secrets
import hashlib

class TestLicenseGenerator:
    """Simple license generator for testing purposes."""
    
    def __init__(self):
        """Initialize the test license generator."""
        # Simple secret key for testing (in production, use proper RSA keys)
        self.secret_key = "test-secret-key-for-quizaigen-development"
        
    def generate_license_key(self, 
                           customer_id: str,
                           tier: str,
                           features: List[str],
                           duration_days: int = 365,
                           hardware_binding: bool = False) -> str:
        """
        Generate a test license key.
        
        Args:
            customer_id: Unique customer identifier
            tier: License tier (free, premium, enterprise)
            features: List of features included
            duration_days: License duration in days
            hardware_binding: Whether to bind to hardware
            
        Returns:
            Base64 encoded license key
        """
        # Create license payload
        now = datetime.now()
        expires_at = now + timedelta(days=duration_days)
        
        license_data = {
            "customer_id": customer_id,
            "product": "quizaigen",
            "tier": tier,
            "features": features,
            "issued_at": now.isoformat(),
            "expires_at": expires_at.isoformat(),
            "exp": int(expires_at.timestamp()),  # Unix timestamp for JWT compatibility
            "iat": int(now.timestamp()),
            "version": "1.0",
            "test_license": True  # Mark as test license
        }
        
        if hardware_binding:
            # Add a test hardware fingerprint
            license_data["hardware_fingerprint"] = "test-hardware-fingerprint-123"
        
        # Create signature (simple HMAC for testing)
        license_json = json.dumps(license_data, sort_keys=True)
        signature = hashlib.sha256(
            (license_json + self.secret_key).encode()
        ).hexdigest()
        
        # Combine data and signature
        signed_license = {
            "data": license_data,
            "signature": signature
        }
        
        # Encode as base64
        license_bytes = json.dumps(signed_license).encode()
        license_key = base64.b64encode(license_bytes).decode()
        
        return license_key
    
    def validate_license_key(self, license_key: str) -> Dict[str, Any]:
        """
        Validate a test license key.
        
        Args:
            license_key: Base64 encoded license key
            
        Returns:
            Validation result
        """
        try:
            # Decode license
            license_bytes = base64.b64decode(license_key.encode())
            signed_license = json.loads(license_bytes.decode())
            
            license_data = signed_license["data"]
            signature = signed_license["signature"]
            
            # Verify signature
            license_json = json.dumps(license_data, sort_keys=True)
            expected_signature = hashlib.sha256(
                (license_json + self.secret_key).encode()
            ).hexdigest()
            
            if signature != expected_signature:
                return {"valid": False, "error": "Invalid signature"}
            
            # Check expiration
            exp_timestamp = license_data.get("exp", 0)
            if exp_timestamp and datetime.now().timestamp() > exp_timestamp:
                return {"valid": False, "error": "License expired"}
            
            return {
                "valid": True,
                "customer_id": license_data["customer_id"],
                "tier": license_data["tier"],
                "features": license_data["features"],
                "expires_at": license_data["expires_at"]
            }
            
        except Exception as e:
            return {"valid": False, "error": str(e)}

def generate_test_licenses():
    """Generate test licenses for all tiers."""
    generator = TestLicenseGenerator()
    
    # Define tier configurations
    tier_configs = {
        "free": {
            "features": ["basic_mcq", "basic_boolean", "basic_faq"],
            "duration_days": 365
        },
        "premium": {
            "features": [
                "basic_mcq", "basic_boolean", "basic_faq",
                "fill_blank", "advanced_export", "batch_processing",
                "document_processing", "url_processing"
            ],
            "duration_days": 365
        },
        "enterprise": {
            "features": [
                "basic_mcq", "basic_boolean", "basic_faq",
                "fill_blank", "advanced_export", "batch_processing",
                "document_processing", "url_processing",
                "multilingual", "custom_training", "gpu_acceleration",
                "white_label", "api_access", "all"
            ],
            "duration_days": 365
        }
    }
    
    licenses = {}
    
    print("Generating Test License Keys")
    print("=" * 50)
    
    for tier, config in tier_configs.items():
        customer_id = f"test-customer-{tier}-001"
        
        license_key = generator.generate_license_key(
            customer_id=customer_id,
            tier=tier,
            features=config["features"],
            duration_days=config["duration_days"],
            hardware_binding=False  # Disable for testing
        )
        
        licenses[tier] = {
            "customer_id": customer_id,
            "license_key": license_key,
            "tier": tier,
            "features": config["features"]
        }
        
        print(f"\n{tier.upper()} TIER LICENSE:")
        print(f"Customer ID: {customer_id}")
        print(f"Features: {', '.join(config['features'][:3])}{'...' if len(config['features']) > 3 else ''}")
        print(f"License Key: {license_key[:50]}...")
        
        # Validate the generated license
        validation = generator.validate_license_key(license_key)
        if validation["valid"]:
            print("✅ License validation: PASSED")
        else:
            print(f"❌ License validation: FAILED - {validation['error']}")
    
    # Save licenses to file
    with open("test_licenses.json", "w") as f:
        json.dump(licenses, f, indent=2)
    
    print(f"\n📄 Test licenses saved to: test_licenses.json")
    
    # Create environment file with licenses
    env_content = f"""# Test License Keys for QuizAIGen Development
# Generated on {datetime.now().isoformat()}

# Free Tier License
QUIZAIGEN_FREE_LICENSE_KEY={licenses['free']['license_key']}

# Premium Tier License  
QUIZAIGEN_PREMIUM_LICENSE_KEY={licenses['premium']['license_key']}

# Enterprise Tier License
QUIZAIGEN_ENTERPRISE_LICENSE_KEY={licenses['enterprise']['license_key']}

# Default license for testing (Premium)
QUIZAIGEN_LICENSE_KEY={licenses['premium']['license_key']}
"""
    
    with open("test_licenses.env", "w") as f:
        f.write(env_content)
    
    print(f"📄 Environment file saved to: test_licenses.env")
    
    return licenses

def test_license_integration():
    """Test license integration with QuizAIGen packages."""
    print("\n" + "=" * 50)
    print("Testing License Integration")
    print("=" * 50)
    
    # Load test licenses
    try:
        with open("test_licenses.json", "r") as f:
            licenses = json.load(f)
    except FileNotFoundError:
        print("❌ No test licenses found. Run generate_test_licenses() first.")
        return
    
    # Test each tier
    for tier, license_info in licenses.items():
        print(f"\n🧪 Testing {tier.upper()} license integration...")
        
        license_key = license_info["license_key"]
        
        # Create a simple test script
        test_script = f'''
import os
import sys

# Set license key
os.environ["QUIZAIGEN_LICENSE_KEY"] = "{license_key}"

try:
    import quizaigen
    
    # Initialize with license
    quizaigen.initialize_license("{license_key}")
    
    # Check license status
    license_info = quizaigen.get_license_info()
    print(f"License status: {{license_info.get('status')}}")
    print(f"Current tier: {{quizaigen.get_current_tier().value}}")
    
    # Test basic functionality
    generator = quizaigen.QuestionGenerator()
    test_text = "Python is a programming language."
    questions = generator.generate_mcq(test_text, num_questions=1)
    
    if questions:
        print(f"✅ Generated {{len(questions)}} questions successfully")
        print(f"Sample: {{questions[0].question[:50]}}...")
    else:
        print("❌ No questions generated")
        
except Exception as e:
    print(f"❌ Error: {{e}}")
'''
        
        # Save and run test script
        with open(f"test_{tier}_license.py", "w") as f:
            f.write(test_script)
        
        print(f"   Created test script: test_{tier}_license.py")
    
    print("\n📝 License integration test scripts created.")
    print("Run them individually to test each tier:")
    for tier in licenses.keys():
        print(f"   python test_{tier}_license.py")

def main():
    """Main function."""
    print("QuizAIGen Test License Generator")
    print("=" * 50)
    
    # Generate test licenses
    licenses = generate_test_licenses()
    
    # Test license integration
    test_license_integration()
    
    print("\n🎉 Test license generation completed!")
    print("\nNext steps:")
    print("1. Use the generated license keys to test commercial packages")
    print("2. Set QUIZAIGEN_LICENSE_KEY environment variable")
    print("3. Test tier-specific features")
    print("4. Integrate with your SaaS application")
    
    return licenses

if __name__ == "__main__":
    main()
