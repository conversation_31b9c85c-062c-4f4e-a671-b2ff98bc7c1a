#!/usr/bin/env python3
"""
QuizAIGen Development Environment Setup Script

This script automates the setup of a development environment for QuizAIGen,
including dependency installation, model downloads, and environment configuration.

Usage:
    python scripts/setup_dev_environment.py
    python scripts/setup_dev_environment.py --minimal  # Skip optional dependencies
    python scripts/setup_dev_environment.py --gpu      # Install GPU support
"""

import argparse
import os
import platform
import subprocess
import sys
from pathlib import Path
from typing import List, Optional


class DevEnvironmentSetup:
    """Handles development environment setup for QuizAIGen."""
    
    def __init__(self, minimal: bool = False, gpu: bool = False, verbose: bool = False):
        self.minimal = minimal
        self.gpu = gpu
        self.verbose = verbose
        self.system = platform.system().lower()
        self.python_version = sys.version_info
        
    def log(self, message: str, level: str = "INFO"):
        """Log a message with optional level."""
        if level == "DEBUG" and not self.verbose:
            return
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "DEBUG": "🔍"
        }.get(level, "📝")
        print(f"{prefix} {message}")
        
    def run_command(self, cmd: List[str], check: bool = True, capture: bool = False) -> subprocess.CompletedProcess:
        """Run a command with error handling."""
        self.log(f"Running: {' '.join(cmd)}", "DEBUG")
        
        try:
            result = subprocess.run(
                cmd,
                check=check,
                capture_output=capture,
                text=True
            )
            return result
        except subprocess.CalledProcessError as e:
            self.log(f"Command failed: {' '.join(cmd)}", "ERROR")
            if capture:
                self.log(f"stdout: {e.stdout}", "DEBUG")
                self.log(f"stderr: {e.stderr}", "DEBUG")
            raise
        except FileNotFoundError:
            self.log(f"Command not found: {cmd[0]}", "ERROR")
            raise
            
    def check_python_version(self) -> bool:
        """Check if Python version is compatible."""
        self.log("Checking Python version...")
        
        if self.python_version < (3, 8):
            self.log(f"Python {self.python_version.major}.{self.python_version.minor} is not supported", "ERROR")
            self.log("QuizAIGen requires Python 3.8 or higher", "ERROR")
            return False
            
        self.log(f"Python {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro} is compatible", "SUCCESS")
        return True
        
    def check_system_dependencies(self) -> bool:
        """Check and install system dependencies."""
        self.log("Checking system dependencies...")
        
        if self.system == "linux":
            return self._check_linux_dependencies()
        elif self.system == "darwin":
            return self._check_macos_dependencies()
        elif self.system == "windows":
            return self._check_windows_dependencies()
        else:
            self.log(f"Unsupported system: {self.system}", "WARNING")
            return True
            
    def _check_linux_dependencies(self) -> bool:
        """Check Linux system dependencies."""
        required_packages = [
            "tesseract-ocr",
            "tesseract-ocr-eng",
            "poppler-utils",
            "libmagic1"
        ]
        
        # Check if packages are installed
        missing_packages = []
        for package in required_packages:
            try:
                result = self.run_command(["dpkg", "-l", package], capture=True, check=False)
                if result.returncode != 0:
                    missing_packages.append(package)
            except FileNotFoundError:
                self.log("dpkg not found, assuming non-Debian system", "WARNING")
                break
                
        if missing_packages:
            self.log(f"Missing packages: {', '.join(missing_packages)}", "WARNING")
            self.log("Install with: sudo apt-get install " + " ".join(missing_packages))
            return False
            
        self.log("All system dependencies are installed", "SUCCESS")
        return True
        
    def _check_macos_dependencies(self) -> bool:
        """Check macOS system dependencies."""
        # Check if Homebrew is installed
        try:
            self.run_command(["brew", "--version"], capture=True)
        except FileNotFoundError:
            self.log("Homebrew not found", "WARNING")
            self.log("Install Homebrew: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            return False
            
        # Check required packages
        required_packages = ["tesseract", "poppler", "libmagic"]
        missing_packages = []
        
        for package in required_packages:
            try:
                result = self.run_command(["brew", "list", package], capture=True, check=False)
                if result.returncode != 0:
                    missing_packages.append(package)
            except subprocess.CalledProcessError:
                missing_packages.append(package)
                
        if missing_packages:
            self.log(f"Missing packages: {', '.join(missing_packages)}", "WARNING")
            self.log("Install with: brew install " + " ".join(missing_packages))
            return False
            
        self.log("All system dependencies are installed", "SUCCESS")
        return True
        
    def _check_windows_dependencies(self) -> bool:
        """Check Windows system dependencies."""
        self.log("Windows system dependency check not implemented", "WARNING")
        self.log("Please manually install Tesseract OCR and Poppler")
        self.log("Tesseract: https://github.com/UB-Mannheim/tesseract/wiki")
        self.log("Poppler: https://blog.alivate.com.au/poppler-windows/")
        return True
        
    def upgrade_pip(self) -> bool:
        """Upgrade pip to latest version."""
        self.log("Upgrading pip...")
        
        try:
            self.run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
            self.log("pip upgraded successfully", "SUCCESS")
            return True
        except subprocess.CalledProcessError:
            self.log("Failed to upgrade pip", "ERROR")
            return False
            
    def install_core_dependencies(self) -> bool:
        """Install core Python dependencies."""
        self.log("Installing core dependencies...")
        
        requirements_file = "requirements.txt"
        if not Path(requirements_file).exists():
            self.log(f"{requirements_file} not found", "ERROR")
            return False
            
        try:
            cmd = [sys.executable, "-m", "pip", "install", "-r", requirements_file]
            if self.gpu:
                # Install PyTorch with CUDA support
                self.log("Installing PyTorch with GPU support...")
                self.run_command([
                    sys.executable, "-m", "pip", "install", 
                    "torch", "torchvision", "torchaudio", 
                    "--index-url", "https://download.pytorch.org/whl/cu118"
                ])
            
            self.run_command(cmd)
            self.log("Core dependencies installed successfully", "SUCCESS")
            return True
        except subprocess.CalledProcessError:
            self.log("Failed to install core dependencies", "ERROR")
            return False
            
    def install_dev_dependencies(self) -> bool:
        """Install development dependencies."""
        if self.minimal:
            self.log("Skipping development dependencies (minimal mode)", "WARNING")
            return True
            
        self.log("Installing development dependencies...")
        
        dev_requirements = "requirements-dev.txt"
        if Path(dev_requirements).exists():
            try:
                self.run_command([sys.executable, "-m", "pip", "install", "-r", dev_requirements])
                self.log("Development dependencies installed successfully", "SUCCESS")
                return True
            except subprocess.CalledProcessError:
                self.log("Failed to install development dependencies", "ERROR")
                return False
        else:
            # Install common dev dependencies
            dev_packages = [
                "pytest", "pytest-cov", "black", "isort", "flake8", 
                "mypy", "pre-commit", "twine", "build"
            ]
            
            try:
                self.run_command([sys.executable, "-m", "pip", "install"] + dev_packages)
                self.log("Development dependencies installed successfully", "SUCCESS")
                return True
            except subprocess.CalledProcessError:
                self.log("Failed to install development dependencies", "ERROR")
                return False
                
    def install_package_editable(self) -> bool:
        """Install the package in editable mode."""
        self.log("Installing QuizAIGen in editable mode...")
        
        try:
            self.run_command([sys.executable, "-m", "pip", "install", "-e", "."])
            self.log("QuizAIGen installed in editable mode", "SUCCESS")
            return True
        except subprocess.CalledProcessError:
            self.log("Failed to install QuizAIGen in editable mode", "ERROR")
            return False
            
    def download_models(self) -> bool:
        """Download required language models."""
        if self.minimal:
            self.log("Skipping model downloads (minimal mode)", "WARNING")
            return True
            
        self.log("Downloading language models...")
        
        # Download spaCy models
        spacy_models = ["en_core_web_sm"]
        
        for model in spacy_models:
            try:
                self.log(f"Downloading spaCy model: {model}")
                self.run_command([sys.executable, "-m", "spacy", "download", model])
                self.log(f"Downloaded {model} successfully", "SUCCESS")
            except subprocess.CalledProcessError:
                self.log(f"Failed to download {model}", "WARNING")
                
        # Download NLTK data
        try:
            self.log("Downloading NLTK data...")
            import nltk
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('wordnet', quiet=True)
            self.log("NLTK data downloaded successfully", "SUCCESS")
        except Exception as e:
            self.log(f"Failed to download NLTK data: {e}", "WARNING")
            
        return True
        
    def setup_pre_commit(self) -> bool:
        """Setup pre-commit hooks."""
        if self.minimal:
            self.log("Skipping pre-commit setup (minimal mode)", "WARNING")
            return True
            
        self.log("Setting up pre-commit hooks...")
        
        if not Path(".pre-commit-config.yaml").exists():
            self.log(".pre-commit-config.yaml not found, skipping", "WARNING")
            return True
            
        try:
            self.run_command(["pre-commit", "install"])
            self.log("Pre-commit hooks installed successfully", "SUCCESS")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("Failed to setup pre-commit hooks", "WARNING")
            return True  # Non-critical
            
    def run_tests(self) -> bool:
        """Run a quick test to verify installation."""
        self.log("Running quick verification test...")
        
        try:
            # Test import
            result = self.run_command([
                sys.executable, "-c", 
                "import quizaigen; print(f'QuizAIGen {quizaigen.__version__} imported successfully')"
            ], capture=True)
            
            self.log(result.stdout.strip(), "SUCCESS")
            
            # Run a simple test if pytest is available
            if not self.minimal:
                try:
                    self.run_command(["pytest", "--version"], capture=True)
                    self.log("Running basic tests...")
                    self.run_command(["pytest", "tests/", "-x", "-q", "--tb=short"])
                    self.log("Basic tests passed", "SUCCESS")
                except (subprocess.CalledProcessError, FileNotFoundError):
                    self.log("Tests failed or pytest not available", "WARNING")
                    
            return True
        except subprocess.CalledProcessError:
            self.log("Verification test failed", "ERROR")
            return False
            
    def create_env_file(self) -> bool:
        """Create a sample .env file."""
        self.log("Creating sample .env file...")
        
        env_file = Path(".env")
        if env_file.exists():
            self.log(".env file already exists, skipping", "WARNING")
            return True
            
        env_content = """
# QuizAIGen Environment Configuration

# API Keys (optional)
# OPENAI_API_KEY=your_openai_api_key_here
# HUGGINGFACE_API_TOKEN=your_huggingface_token_here

# Model Configuration
# QUIZAIGEN_MODEL_CACHE_DIR=./models
# QUIZAIGEN_DEFAULT_MODEL=distilbert-base-uncased

# Processing Configuration
# QUIZAIGEN_MAX_WORKERS=4
# QUIZAIGEN_BATCH_SIZE=32

# Logging
# QUIZAIGEN_LOG_LEVEL=INFO
# QUIZAIGEN_LOG_FILE=logs/quizaigen.log

# Development
# QUIZAIGEN_DEBUG=false
# QUIZAIGEN_PROFILE=false
""".strip()
        
        try:
            env_file.write_text(env_content)
            self.log("Sample .env file created", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Failed to create .env file: {e}", "WARNING")
            return True  # Non-critical
            
    def setup_complete(self) -> None:
        """Display setup completion message."""
        self.log("\n" + "=" * 50)
        self.log("🎉 Development environment setup complete!", "SUCCESS")
        self.log("=" * 50)
        
        self.log("\nNext steps:")
        self.log("1. Activate your virtual environment (if using one)")
        self.log("2. Configure your .env file with API keys (if needed)")
        self.log("3. Run tests: pytest tests/")
        self.log("4. Start developing!")
        
        self.log("\nUseful commands:")
        self.log("• Run tests: pytest tests/")
        self.log("• Format code: black quizaigen/ tests/")
        self.log("• Sort imports: isort quizaigen/ tests/")
        self.log("• Type check: mypy quizaigen/")
        self.log("• Build package: python -m build")
        
        if self.gpu:
            self.log("\n🚀 GPU support enabled!")
            self.log("Verify CUDA: python -c \"import torch; print(torch.cuda.is_available())\"")
            
    def run_setup(self) -> bool:
        """Run the complete setup process."""
        self.log("🚀 Starting QuizAIGen development environment setup")
        self.log("=" * 60)
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Checking system dependencies", self.check_system_dependencies),
            ("Upgrading pip", self.upgrade_pip),
            ("Installing core dependencies", self.install_core_dependencies),
            ("Installing development dependencies", self.install_dev_dependencies),
            ("Installing package in editable mode", self.install_package_editable),
            ("Downloading language models", self.download_models),
            ("Setting up pre-commit hooks", self.setup_pre_commit),
            ("Creating environment file", self.create_env_file),
            ("Running verification tests", self.run_tests),
        ]
        
        for step_name, step_func in steps:
            self.log(f"\n📋 {step_name}...")
            try:
                if not step_func():
                    self.log(f"Setup failed at: {step_name}", "ERROR")
                    return False
            except Exception as e:
                self.log(f"Unexpected error in {step_name}: {e}", "ERROR")
                if self.verbose:
                    import traceback
                    traceback.print_exc()
                return False
                
        self.setup_complete()
        return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Setup QuizAIGen development environment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/setup_dev_environment.py              # Full setup
  python scripts/setup_dev_environment.py --minimal   # Minimal setup
  python scripts/setup_dev_environment.py --gpu       # Setup with GPU support
        """
    )
    
    parser.add_argument(
        "--minimal",
        action="store_true",
        help="Skip optional dependencies and models"
    )
    
    parser.add_argument(
        "--gpu",
        action="store_true",
        help="Install GPU support (CUDA)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Change to project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    setup = DevEnvironmentSetup(
        minimal=args.minimal,
        gpu=args.gpu,
        verbose=args.verbose
    )
    
    try:
        success = setup.run_setup()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n❌ Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()