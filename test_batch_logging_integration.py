#!/usr/bin/env python3
"""
Test script for batch processor logging integration
"""

import time
from pathlib import Path
from quizaigen.utils.logger import configure_logging, get_log_files, get_log_statistics
from quizaigen.api.batch_processor import EnhancedBatchProcessor


def test_batch_processor_logging():
    """Test enhanced batch processor with comprehensive logging."""
    print("=== Testing Batch Processor Logging Integration ===")
    
    # Configure logging for testing
    config = configure_logging(
        log_dir="batch_test_logs",
        level="INFO",
        console_logging=True,
        file_logging=True,
        enable_json_logging=False,
        enable_performance_logging=True,
        separate_error_log=True
    )
    
    print(f"✓ Logging configured: {config.log_dir}")
    
    # Initialize batch processor
    batch_config = {
        'max_workers': 2,
        'chunk_size': 2,
        'enable_performance_monitoring': True
    }
    
    processor = EnhancedBatchProcessor(batch_config)
    print("✓ Enhanced batch processor initialized")
    
    # Test data
    test_inputs = [
        {
            'text': 'Python is a high-level programming language known for its simplicity and readability.',
            'source': 'test_input_1'
        },
        {
            'text': 'Machine learning is a subset of artificial intelligence that focuses on algorithms.',
            'source': 'test_input_2'
        },
        {
            'text': 'Data science combines statistics, programming, and domain expertise.',
            'source': 'test_input_3'
        }
    ]
    
    print(f"✓ Test data prepared: {len(test_inputs)} inputs")
    
    # Test successful batch processing
    try:
        print("\n--- Testing Successful Batch Processing ---")
        results = processor.process_batch(
            inputs=test_inputs,
            question_types=['mcq', 'boolean'],
            num_questions_per_type=2
        )
        
        print(f"✓ Batch processing completed successfully")
        print(f"  - Inputs processed: {results['inputs_processed']}")
        print(f"  - Processing time: {results['processing_time']:.2f}s")
        print(f"  - Questions generated: {results['total_questions_generated']}")
        
    except Exception as e:
        print(f"✗ Batch processing failed: {e}")
    
    # Test error handling
    try:
        print("\n--- Testing Error Handling ---")
        # Test with empty inputs to trigger error logging
        processor.process_batch(
            inputs=[],
            question_types=['mcq'],
            num_questions_per_type=1
        )
    except ValueError as e:
        print(f"✓ Error handling logged correctly: {e}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
    
    # Test with invalid input to see error logging
    try:
        print("\n--- Testing Invalid Input Handling ---")
        invalid_inputs = [
            {'invalid_key': 'This should cause processing errors'},
            {'text': None}  # Invalid text
        ]
        
        results = processor.process_batch(
            inputs=invalid_inputs,
            question_types=['mcq'],
            num_questions_per_type=1
        )
        
        print(f"✓ Invalid input handling completed")
        print(f"  - Inputs failed: {results['inputs_failed']}")
        print(f"  - Errors logged: {len(results['errors'])}")
        
    except Exception as e:
        print(f"✓ Exception handling logged: {e}")
    
    print("\n✅ Batch processor logging integration test completed!")
    return True


def test_log_analysis():
    """Analyze the generated logs."""
    print("\n=== Analyzing Generated Logs ===")
    
    # Get log files information
    log_files = get_log_files()
    print(f"✓ Log files found: {len(log_files)} files")
    
    for log_file, info in log_files.items():
        if isinstance(info, dict) and 'size_mb' in info:
            print(f"  - {Path(log_file).name}: {info['size_mb']} MB, {info['lines']} lines")
    
    # Get statistics
    stats = get_log_statistics()
    print(f"✓ Log statistics:")
    print(f"  - Total files: {stats['total_log_files']}")
    print(f"  - Total size: {stats['total_size_mb']} MB")
    print(f"  - Performance logs: {stats['performance_logs']}")
    print(f"  - Recent errors: {len(stats['recent_errors'])}")
    
    return True


def test_performance_logging():
    """Test performance logging specifically."""
    print("\n=== Testing Performance Logging ===")
    
    # Create batch processor for performance testing
    processor = EnhancedBatchProcessor({'max_workers': 1})
    
    # Test with larger dataset for performance metrics
    large_test_inputs = [
        {
            'text': f'This is test input number {i} for performance testing. '
                   f'It contains enough text to generate meaningful questions and test the system performance.',
            'source': f'perf_test_{i}'
        }
        for i in range(5)
    ]
    
    print(f"✓ Performance test data prepared: {len(large_test_inputs)} inputs")
    
    start_time = time.time()
    
    try:
        results = processor.process_batch(
            inputs=large_test_inputs,
            question_types=['mcq', 'boolean', 'faq'],
            num_questions_per_type=3
        )
        
        processing_time = time.time() - start_time
        
        print(f"✓ Performance test completed in {processing_time:.2f}s")
        print(f"  - Questions per second: {results['total_questions_generated'] / processing_time:.2f}")
        print(f"  - Success rate: {results['inputs_processed'] / len(large_test_inputs) * 100:.1f}%")
        
    except Exception as e:
        print(f"✗ Performance test failed: {e}")
    
    return True


def show_log_samples():
    """Show samples from different log files."""
    print("\n=== Log File Samples ===")
    
    log_dir = Path("batch_test_logs")
    if not log_dir.exists():
        print("✗ Log directory not found")
        return
    
    # Show main log sample
    main_log = log_dir / f"quizaigen_{time.strftime('%Y%m%d')}.log"
    if main_log.exists():
        print(f"\n--- Main Log Sample ({main_log.name}) ---")
        with open(main_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[-5:], 1):  # Last 5 lines
                print(f"{i}: {line.strip()}")
    
    # Show performance log sample
    perf_log = log_dir / f"quizaigen_performance_{time.strftime('%Y%m%d')}.log"
    if perf_log.exists():
        print(f"\n--- Performance Log Sample ({perf_log.name}) ---")
        with open(perf_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[-3:], 1):  # Last 3 lines
                print(f"{i}: {line.strip()}")
    
    # Show error log sample
    error_log = log_dir / f"quizaigen_errors_{time.strftime('%Y%m%d')}.log"
    if error_log.exists():
        print(f"\n--- Error Log Sample ({error_log.name}) ---")
        with open(error_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[-3:], 1):  # Last 3 lines
                print(f"{i}: {line.strip()}")


def main():
    """Run all batch processor logging tests."""
    print("🚀 Starting Batch Processor Logging Integration Tests")
    
    success = True
    
    try:
        success &= test_batch_processor_logging()
        success &= test_log_analysis()
        success &= test_performance_logging()
        show_log_samples()
        
        if success:
            print("\n✅ All batch processor logging tests completed successfully!")
            print("📁 Check the 'batch_test_logs' directory for generated log files")
        else:
            print("\n❌ Some tests failed")
            
    except Exception as e:
        print(f"\n💥 Test suite failed with exception: {e}")
        success = False
    
    return success


if __name__ == "__main__":
    main()
