"""
Multilingual Question Templates for QuizAIGen

Provides language-specific templates and patterns for generating questions
in multiple languages with proper grammar and cultural context.
"""

from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

from ..core.language_detector import SupportedLanguage


@dataclass
class QuestionTemplate:
    """Template for generating questions in specific language."""
    pattern: str
    placeholders: List[str]
    question_type: str
    language: str
    difficulty: str = "medium"
    
    def format(self, **kwargs) -> str:
        """Format template with provided values."""
        return self.pattern.format(**kwargs)


class QuestionType(Enum):
    """Types of questions supported."""
    MCQ = "mcq"
    BOOLEAN = "boolean"
    FAQ = "faq"
    FILL_BLANK = "fill_blank"
    SHORT_ANSWER = "short_answer"


class MultilingualTemplates:
    """
    Comprehensive multilingual template system for question generation.
    
    Provides language-specific templates, patterns, and cultural adaptations
    for generating natural-sounding questions in multiple languages.
    """
    
    def __init__(self):
        """Initialize multilingual templates."""
        self.templates = self._load_templates()
        self.question_starters = self._load_question_starters()
        self.answer_patterns = self._load_answer_patterns()
        self.cultural_adaptations = self._load_cultural_adaptations()
    
    def _load_templates(self) -> Dict[str, Dict[str, List[QuestionTemplate]]]:
        """Load question templates for all supported languages."""
        return {
            'en': self._load_english_templates(),
            'es': self._load_spanish_templates(),
            'fr': self._load_french_templates(),
            'de': self._load_german_templates(),
            'it': self._load_italian_templates(),
            'pt': self._load_portuguese_templates()
        }
    
    def _load_english_templates(self) -> Dict[str, List[QuestionTemplate]]:
        """Load English question templates."""
        return {
            QuestionType.MCQ.value: [
                QuestionTemplate("What is {subject}?", ["subject"], "mcq", "en"),
                QuestionTemplate("Which of the following best describes {concept}?", ["concept"], "mcq", "en"),
                QuestionTemplate("According to the text, {statement}?", ["statement"], "mcq", "en"),
                QuestionTemplate("What does {term} refer to?", ["term"], "mcq", "en"),
                QuestionTemplate("How would you define {concept}?", ["concept"], "mcq", "en")
            ],
            QuestionType.BOOLEAN.value: [
                QuestionTemplate("Is {statement} true or false?", ["statement"], "boolean", "en"),
                QuestionTemplate("{statement}. True or False?", ["statement"], "boolean", "en"),
                QuestionTemplate("According to the text, is it true that {statement}?", ["statement"], "boolean", "en"),
                QuestionTemplate("Can we say that {statement}?", ["statement"], "boolean", "en")
            ],
            QuestionType.FAQ.value: [
                QuestionTemplate("What is {subject}?", ["subject"], "faq", "en"),
                QuestionTemplate("How does {process} work?", ["process"], "faq", "en"),
                QuestionTemplate("Why is {concept} important?", ["concept"], "faq", "en"),
                QuestionTemplate("When was {event} established?", ["event"], "faq", "en"),
                QuestionTemplate("Who created {invention}?", ["invention"], "faq", "en"),
                QuestionTemplate("Where is {location} located?", ["location"], "faq", "en")
            ],
            QuestionType.FILL_BLANK.value: [
                QuestionTemplate("{sentence_part1} _____ {sentence_part2}", ["sentence_part1", "sentence_part2"], "fill_blank", "en"),
                QuestionTemplate("Complete the sentence: {partial_sentence} _____", ["partial_sentence"], "fill_blank", "en"),
                QuestionTemplate("Fill in the blank: {text_with_blank}", ["text_with_blank"], "fill_blank", "en")
            ]
        }
    
    def _load_spanish_templates(self) -> Dict[str, List[QuestionTemplate]]:
        """Load Spanish question templates."""
        return {
            QuestionType.MCQ.value: [
                QuestionTemplate("¿Qué es {subject}?", ["subject"], "mcq", "es"),
                QuestionTemplate("¿Cuál de las siguientes opciones describe mejor {concept}?", ["concept"], "mcq", "es"),
                QuestionTemplate("Según el texto, ¿{statement}?", ["statement"], "mcq", "es"),
                QuestionTemplate("¿A qué se refiere {term}?", ["term"], "mcq", "es"),
                QuestionTemplate("¿Cómo definirías {concept}?", ["concept"], "mcq", "es")
            ],
            QuestionType.BOOLEAN.value: [
                QuestionTemplate("¿Es {statement} verdadero o falso?", ["statement"], "boolean", "es"),
                QuestionTemplate("{statement}. ¿Verdadero o Falso?", ["statement"], "boolean", "es"),
                QuestionTemplate("Según el texto, ¿es cierto que {statement}?", ["statement"], "boolean", "es"),
                QuestionTemplate("¿Podemos decir que {statement}?", ["statement"], "boolean", "es")
            ],
            QuestionType.FAQ.value: [
                QuestionTemplate("¿Qué es {subject}?", ["subject"], "faq", "es"),
                QuestionTemplate("¿Cómo funciona {process}?", ["process"], "faq", "es"),
                QuestionTemplate("¿Por qué es importante {concept}?", ["concept"], "faq", "es"),
                QuestionTemplate("¿Cuándo se estableció {event}?", ["event"], "faq", "es"),
                QuestionTemplate("¿Quién creó {invention}?", ["invention"], "faq", "es"),
                QuestionTemplate("¿Dónde está ubicado {location}?", ["location"], "faq", "es")
            ],
            QuestionType.FILL_BLANK.value: [
                QuestionTemplate("{sentence_part1} _____ {sentence_part2}", ["sentence_part1", "sentence_part2"], "fill_blank", "es"),
                QuestionTemplate("Completa la oración: {partial_sentence} _____", ["partial_sentence"], "fill_blank", "es"),
                QuestionTemplate("Rellena el espacio en blanco: {text_with_blank}", ["text_with_blank"], "fill_blank", "es")
            ]
        }
    
    def _load_french_templates(self) -> Dict[str, List[QuestionTemplate]]:
        """Load French question templates."""
        return {
            QuestionType.MCQ.value: [
                QuestionTemplate("Qu'est-ce que {subject}?", ["subject"], "mcq", "fr"),
                QuestionTemplate("Laquelle des options suivantes décrit le mieux {concept}?", ["concept"], "mcq", "fr"),
                QuestionTemplate("Selon le texte, {statement}?", ["statement"], "mcq", "fr"),
                QuestionTemplate("À quoi fait référence {term}?", ["term"], "mcq", "fr"),
                QuestionTemplate("Comment définiriez-vous {concept}?", ["concept"], "mcq", "fr")
            ],
            QuestionType.BOOLEAN.value: [
                QuestionTemplate("Est-ce que {statement} est vrai ou faux?", ["statement"], "boolean", "fr"),
                QuestionTemplate("{statement}. Vrai ou Faux?", ["statement"], "boolean", "fr"),
                QuestionTemplate("Selon le texte, est-il vrai que {statement}?", ["statement"], "boolean", "fr"),
                QuestionTemplate("Peut-on dire que {statement}?", ["statement"], "boolean", "fr")
            ],
            QuestionType.FAQ.value: [
                QuestionTemplate("Qu'est-ce que {subject}?", ["subject"], "faq", "fr"),
                QuestionTemplate("Comment fonctionne {process}?", ["process"], "faq", "fr"),
                QuestionTemplate("Pourquoi {concept} est-il important?", ["concept"], "faq", "fr"),
                QuestionTemplate("Quand {event} a-t-il été établi?", ["event"], "faq", "fr"),
                QuestionTemplate("Qui a créé {invention}?", ["invention"], "faq", "fr"),
                QuestionTemplate("Où se trouve {location}?", ["location"], "faq", "fr")
            ],
            QuestionType.FILL_BLANK.value: [
                QuestionTemplate("{sentence_part1} _____ {sentence_part2}", ["sentence_part1", "sentence_part2"], "fill_blank", "fr"),
                QuestionTemplate("Complétez la phrase: {partial_sentence} _____", ["partial_sentence"], "fill_blank", "fr"),
                QuestionTemplate("Remplissez le blanc: {text_with_blank}", ["text_with_blank"], "fill_blank", "fr")
            ]
        }
    
    def _load_german_templates(self) -> Dict[str, List[QuestionTemplate]]:
        """Load German question templates."""
        return {
            QuestionType.MCQ.value: [
                QuestionTemplate("Was ist {subject}?", ["subject"], "mcq", "de"),
                QuestionTemplate("Welche der folgenden Optionen beschreibt {concept} am besten?", ["concept"], "mcq", "de"),
                QuestionTemplate("Laut dem Text, {statement}?", ["statement"], "mcq", "de"),
                QuestionTemplate("Worauf bezieht sich {term}?", ["term"], "mcq", "de"),
                QuestionTemplate("Wie würden Sie {concept} definieren?", ["concept"], "mcq", "de")
            ],
            QuestionType.BOOLEAN.value: [
                QuestionTemplate("Ist {statement} wahr oder falsch?", ["statement"], "boolean", "de"),
                QuestionTemplate("{statement}. Wahr oder Falsch?", ["statement"], "boolean", "de"),
                QuestionTemplate("Laut dem Text, ist es wahr, dass {statement}?", ["statement"], "boolean", "de"),
                QuestionTemplate("Können wir sagen, dass {statement}?", ["statement"], "boolean", "de")
            ],
            QuestionType.FAQ.value: [
                QuestionTemplate("Was ist {subject}?", ["subject"], "faq", "de"),
                QuestionTemplate("Wie funktioniert {process}?", ["process"], "faq", "de"),
                QuestionTemplate("Warum ist {concept} wichtig?", ["concept"], "faq", "de"),
                QuestionTemplate("Wann wurde {event} gegründet?", ["event"], "faq", "de"),
                QuestionTemplate("Wer hat {invention} erfunden?", ["invention"], "faq", "de"),
                QuestionTemplate("Wo befindet sich {location}?", ["location"], "faq", "de")
            ],
            QuestionType.FILL_BLANK.value: [
                QuestionTemplate("{sentence_part1} _____ {sentence_part2}", ["sentence_part1", "sentence_part2"], "fill_blank", "de"),
                QuestionTemplate("Vervollständigen Sie den Satz: {partial_sentence} _____", ["partial_sentence"], "fill_blank", "de"),
                QuestionTemplate("Füllen Sie die Lücke aus: {text_with_blank}", ["text_with_blank"], "fill_blank", "de")
            ]
        }
    
    def _load_italian_templates(self) -> Dict[str, List[QuestionTemplate]]:
        """Load Italian question templates."""
        return {
            QuestionType.MCQ.value: [
                QuestionTemplate("Cos'è {subject}?", ["subject"], "mcq", "it"),
                QuestionTemplate("Quale delle seguenti opzioni descrive meglio {concept}?", ["concept"], "mcq", "it"),
                QuestionTemplate("Secondo il testo, {statement}?", ["statement"], "mcq", "it"),
                QuestionTemplate("A cosa si riferisce {term}?", ["term"], "mcq", "it"),
                QuestionTemplate("Come definiresti {concept}?", ["concept"], "mcq", "it")
            ],
            QuestionType.BOOLEAN.value: [
                QuestionTemplate("È {statement} vero o falso?", ["statement"], "boolean", "it"),
                QuestionTemplate("{statement}. Vero o Falso?", ["statement"], "boolean", "it"),
                QuestionTemplate("Secondo il testo, è vero che {statement}?", ["statement"], "boolean", "it"),
                QuestionTemplate("Possiamo dire che {statement}?", ["statement"], "boolean", "it")
            ],
            QuestionType.FAQ.value: [
                QuestionTemplate("Cos'è {subject}?", ["subject"], "faq", "it"),
                QuestionTemplate("Come funziona {process}?", ["process"], "faq", "it"),
                QuestionTemplate("Perché {concept} è importante?", ["concept"], "faq", "it"),
                QuestionTemplate("Quando è stato stabilito {event}?", ["event"], "faq", "it"),
                QuestionTemplate("Chi ha creato {invention}?", ["invention"], "faq", "it"),
                QuestionTemplate("Dove si trova {location}?", ["location"], "faq", "it")
            ],
            QuestionType.FILL_BLANK.value: [
                QuestionTemplate("{sentence_part1} _____ {sentence_part2}", ["sentence_part1", "sentence_part2"], "fill_blank", "it"),
                QuestionTemplate("Completa la frase: {partial_sentence} _____", ["partial_sentence"], "fill_blank", "it"),
                QuestionTemplate("Riempi lo spazio vuoto: {text_with_blank}", ["text_with_blank"], "fill_blank", "it")
            ]
        }
    
    def _load_portuguese_templates(self) -> Dict[str, List[QuestionTemplate]]:
        """Load Portuguese question templates."""
        return {
            QuestionType.MCQ.value: [
                QuestionTemplate("O que é {subject}?", ["subject"], "mcq", "pt"),
                QuestionTemplate("Qual das seguintes opções melhor descreve {concept}?", ["concept"], "mcq", "pt"),
                QuestionTemplate("De acordo com o texto, {statement}?", ["statement"], "mcq", "pt"),
                QuestionTemplate("A que se refere {term}?", ["term"], "mcq", "pt"),
                QuestionTemplate("Como você definiria {concept}?", ["concept"], "mcq", "pt")
            ],
            QuestionType.BOOLEAN.value: [
                QuestionTemplate("É {statement} verdadeiro ou falso?", ["statement"], "boolean", "pt"),
                QuestionTemplate("{statement}. Verdadeiro ou Falso?", ["statement"], "boolean", "pt"),
                QuestionTemplate("De acordo com o texto, é verdade que {statement}?", ["statement"], "boolean", "pt"),
                QuestionTemplate("Podemos dizer que {statement}?", ["statement"], "boolean", "pt")
            ],
            QuestionType.FAQ.value: [
                QuestionTemplate("O que é {subject}?", ["subject"], "faq", "pt"),
                QuestionTemplate("Como funciona {process}?", ["process"], "faq", "pt"),
                QuestionTemplate("Por que {concept} é importante?", ["concept"], "faq", "pt"),
                QuestionTemplate("Quando {event} foi estabelecido?", ["event"], "faq", "pt"),
                QuestionTemplate("Quem criou {invention}?", ["invention"], "faq", "pt"),
                QuestionTemplate("Onde fica {location}?", ["location"], "faq", "pt")
            ],
            QuestionType.FILL_BLANK.value: [
                QuestionTemplate("{sentence_part1} _____ {sentence_part2}", ["sentence_part1", "sentence_part2"], "fill_blank", "pt"),
                QuestionTemplate("Complete a frase: {partial_sentence} _____", ["partial_sentence"], "fill_blank", "pt"),
                QuestionTemplate("Preencha o espaço em branco: {text_with_blank}", ["text_with_blank"], "fill_blank", "pt")
            ]
        }
    
    def get_templates(self, language: str, question_type: str) -> List[QuestionTemplate]:
        """Get templates for specific language and question type."""
        if language not in self.templates:
            language = 'en'  # Fallback to English
        
        if question_type not in self.templates[language]:
            return []
        
        return self.templates[language][question_type]
    
    def get_random_template(self, language: str, question_type: str) -> Optional[QuestionTemplate]:
        """Get a random template for specific language and question type."""
        templates = self.get_templates(language, question_type)
        if not templates:
            return None
        
        import random
        return random.choice(templates)
    
    def _load_question_starters(self) -> Dict[str, Dict[str, List[str]]]:
        """Load question starter words for each language."""
        return {
            'en': {
                'what': ['What', 'What is', 'What are', 'What does', 'What did'],
                'how': ['How', 'How does', 'How did', 'How can', 'How would'],
                'why': ['Why', 'Why is', 'Why are', 'Why does', 'Why did'],
                'when': ['When', 'When is', 'When are', 'When does', 'When did'],
                'where': ['Where', 'Where is', 'Where are', 'Where does'],
                'who': ['Who', 'Who is', 'Who are', 'Who does', 'Who did']
            },
            'es': {
                'what': ['Qué', 'Qué es', 'Qué son', 'Qué hace', 'Qué hizo'],
                'how': ['Cómo', 'Cómo es', 'Cómo son', 'Cómo puede', 'Cómo haría'],
                'why': ['Por qué', 'Por qué es', 'Por qué son', 'Por qué hace'],
                'when': ['Cuándo', 'Cuándo es', 'Cuándo son', 'Cuándo hace'],
                'where': ['Dónde', 'Dónde está', 'Dónde están', 'Dónde hace'],
                'who': ['Quién', 'Quién es', 'Quién son', 'Quién hace']
            },
            'fr': {
                'what': ['Qu\'est-ce que', 'Que', 'Quoi', 'Qu\'est-ce qui'],
                'how': ['Comment', 'Comment est', 'Comment sont', 'Comment peut'],
                'why': ['Pourquoi', 'Pourquoi est', 'Pourquoi sont'],
                'when': ['Quand', 'Quand est', 'Quand sont', 'Quand fait'],
                'where': ['Où', 'Où est', 'Où sont', 'Où fait'],
                'who': ['Qui', 'Qui est', 'Qui sont', 'Qui fait']
            },
            'de': {
                'what': ['Was', 'Was ist', 'Was sind', 'Was macht', 'Was hat'],
                'how': ['Wie', 'Wie ist', 'Wie sind', 'Wie kann', 'Wie würde'],
                'why': ['Warum', 'Warum ist', 'Warum sind', 'Warum macht'],
                'when': ['Wann', 'Wann ist', 'Wann sind', 'Wann macht'],
                'where': ['Wo', 'Wo ist', 'Wo sind', 'Wo macht'],
                'who': ['Wer', 'Wer ist', 'Wer sind', 'Wer macht']
            }
        }
    
    def _load_answer_patterns(self) -> Dict[str, List[str]]:
        """Load answer patterns for different languages."""
        return {
            'en': ['The answer is', 'According to the text', 'Based on the information'],
            'es': ['La respuesta es', 'Según el texto', 'Basado en la información'],
            'fr': ['La réponse est', 'Selon le texte', 'Basé sur l\'information'],
            'de': ['Die Antwort ist', 'Laut dem Text', 'Basierend auf der Information']
        }
    
    def _load_cultural_adaptations(self) -> Dict[str, Dict[str, str]]:
        """Load cultural adaptations for different languages."""
        return {
            'en': {'date_format': 'MM/DD/YYYY', 'currency': '$', 'decimal_separator': '.'},
            'es': {'date_format': 'DD/MM/YYYY', 'currency': '€', 'decimal_separator': ','},
            'fr': {'date_format': 'DD/MM/YYYY', 'currency': '€', 'decimal_separator': ','},
            'de': {'date_format': 'DD.MM.YYYY', 'currency': '€', 'decimal_separator': ','}
        }
