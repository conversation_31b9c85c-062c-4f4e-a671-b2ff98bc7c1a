# QuizAIGen Package Documentation Template

This template provides a comprehensive structure for documenting your QuizAIGen package. Copy and customize this template for your specific implementation.

## Package Overview

### Quick Start

```bash
# Install from PyPI
pip install quizaigen

# Install with optional dependencies
pip install quizaigen[dev,docs,examples]

# Install from source
git clone https://github.com/yourusername/quizaigen.git
cd quizaigen
pip install -e .
```

### Basic Usage

```python
from quizaigen import QuestionGenerator, ExportManager

# Initialize the generator
gen = QuestionGenerator()

# Generate questions
text = "Artificial intelligence is transforming education..."
questions = gen.generate_mcq(text, num_questions=5)

# Export results
exporter = ExportManager()
exporter.export_json(questions, "quiz.json")
```

## Installation Guide

### System Requirements

- **Python**: 3.8 or higher
- **Operating System**: Windows, macOS, Linux
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: 2GB free space for models and cache

### Dependencies

#### Core Dependencies
- `torch>=1.9.0` - PyTorch for deep learning
- `transformers>=4.20.0` - Hugging Face transformers
- `spacy>=3.4.0` - Natural language processing
- `nltk>=3.7` - Natural language toolkit
- `pandas>=1.3.0` - Data manipulation
- `scikit-learn>=1.0.0` - Machine learning utilities

#### Optional Dependencies
- **Development**: `pytest`, `black`, `isort`, `mypy`
- **Documentation**: `sphinx`, `sphinx-rtd-theme`
- **Examples**: `jupyter`, `matplotlib`, `seaborn`
- **Performance**: `accelerate`, `optimum`, `onnx`
- **Multilingual**: `polyglot`, `fasttext`, `googletrans`

### Installation Methods

#### Method 1: PyPI (Recommended)

```bash
# Basic installation
pip install quizaigen

# With all optional dependencies
pip install quizaigen[all]

# Specific feature sets
pip install quizaigen[dev]          # Development tools
pip install quizaigen[docs]         # Documentation tools
pip install quizaigen[examples]     # Example notebooks
pip install quizaigen[performance]  # Performance optimizations
pip install quizaigen[multilingual] # Multi-language support
```

#### Method 2: Development Installation

```bash
# Clone repository
git clone https://github.com/yourusername/quizaigen.git
cd quizaigen

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install in editable mode
pip install -e .[dev]

# Download required models
python -m spacy download en_core_web_sm
```

#### Method 3: Docker

```bash
# Pull from Docker Hub
docker pull quizaigen/quizaigen:latest

# Run container
docker run -it --rm quizaigen/quizaigen:latest

# Build from source
docker build -t quizaigen .
docker run -it --rm quizaigen
```

### Post-Installation Setup

#### Download Language Models

```bash
# spaCy models
python -m spacy download en_core_web_sm
python -m spacy download en_core_web_md  # Optional: better accuracy

# NLTK data
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"
```

#### Environment Configuration

Create a `.env` file in your project root:

```env
# API Keys (optional)
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_API_TOKEN=your_huggingface_token_here

# Model Configuration
QUIZAIGEN_MODEL_CACHE_DIR=./models
QUIZAIGEN_DEFAULT_MODEL=distilbert-base-uncased

# Processing Configuration
QUIZAIGEN_MAX_WORKERS=4
QUIZAIGEN_BATCH_SIZE=32

# Logging
QUIZAIGEN_LOG_LEVEL=INFO
QUIZAIGEN_LOG_FILE=logs/quizaigen.log
```

## API Reference

### Core Classes

#### QuestionGenerator

Main class for generating questions from text.

```python
class QuestionGenerator:
    def __init__(
        self,
        model_name: str = "distilbert-base-uncased",
        device: str = "auto",
        cache_dir: Optional[str] = None
    ):
        """Initialize the question generator.
        
        Args:
            model_name: Name of the transformer model to use
            device: Device to run on ('cpu', 'cuda', or 'auto')
            cache_dir: Directory to cache downloaded models
        """
    
    def generate_mcq(
        self,
        text: str,
        num_questions: int = 5,
        difficulty: str = "medium"
    ) -> List[Dict[str, Any]]:
        """Generate multiple choice questions.
        
        Args:
            text: Input text to generate questions from
            num_questions: Number of questions to generate
            difficulty: Question difficulty ('easy', 'medium', 'hard')
            
        Returns:
            List of question dictionaries
        """
    
    def generate_boolean(
        self,
        text: str,
        num_questions: int = 5
    ) -> List[Dict[str, Any]]:
        """Generate true/false questions."""
    
    def generate_fill_blank(
        self,
        text: str,
        num_questions: int = 5
    ) -> List[Dict[str, Any]]:
        """Generate fill-in-the-blank questions."""
```

#### ExportManager

Class for exporting questions to various formats.

```python
class ExportManager:
    def export_json(
        self,
        questions: List[Dict[str, Any]],
        filename: str
    ) -> None:
        """Export questions to JSON format."""
    
    def export_csv(
        self,
        questions: List[Dict[str, Any]],
        filename: str
    ) -> None:
        """Export questions to CSV format."""
    
    def export_moodle(
        self,
        questions: List[Dict[str, Any]],
        filename: str
    ) -> None:
        """Export questions to Moodle XML format."""
```

### Configuration

#### ModelConfig

```python
class ModelConfig:
    model_name: str = "distilbert-base-uncased"
    device: str = "auto"
    cache_dir: Optional[str] = None
    max_length: int = 512
    batch_size: int = 32
```

#### ProcessingConfig

```python
class ProcessingConfig:
    max_workers: int = 4
    chunk_size: int = 1000
    overlap_size: int = 100
    min_sentence_length: int = 10
```

## Usage Examples

### Basic Question Generation

```python
from quizaigen import QuestionGenerator

# Initialize generator
gen = QuestionGenerator()

# Sample text
text = """
Artificial Intelligence (AI) is a branch of computer science that aims to create 
intelligent machines that work and react like humans. Some of the activities 
computers with artificial intelligence are designed for include speech recognition, 
learning, planning, and problem solving.
"""

# Generate different types of questions
mcq_questions = gen.generate_mcq(text, num_questions=3)
boolean_questions = gen.generate_boolean(text, num_questions=2)
fill_blank_questions = gen.generate_fill_blank(text, num_questions=2)

print(f"Generated {len(mcq_questions)} MCQ questions")
print(f"Generated {len(boolean_questions)} Boolean questions")
print(f"Generated {len(fill_blank_questions)} Fill-blank questions")
```

### Batch Processing

```python
from quizaigen import BatchProcessor

# Initialize batch processor
processor = BatchProcessor(num_workers=4)

# Process multiple documents
documents = [
    "path/to/document1.pdf",
    "path/to/document2.docx",
    "path/to/document3.txt"
]

# Generate questions from all documents
results = processor.process_documents(
    documents,
    question_types=["mcq", "boolean"],
    num_questions_per_type=5
)

for doc_path, questions in results.items():
    print(f"Generated {len(questions)} questions from {doc_path}")
```

### Custom Model Configuration

```python
from quizaigen import QuestionGenerator, ModelConfig

# Custom model configuration
config = ModelConfig(
    model_name="bert-base-uncased",
    device="cuda",
    max_length=256,
    batch_size=16
)

# Initialize with custom config
gen = QuestionGenerator(config=config)

# Generate questions
questions = gen.generate_mcq(text, num_questions=10)
```

### Export to Different Formats

```python
from quizaigen import QuestionGenerator, ExportManager

# Generate questions
gen = QuestionGenerator()
questions = gen.generate_mcq(text, num_questions=5)

# Initialize exporter
exporter = ExportManager()

# Export to various formats
exporter.export_json(questions, "quiz.json")
exporter.export_csv(questions, "quiz.csv")
exporter.export_moodle(questions, "quiz.xml")
exporter.export_qti(questions, "quiz_qti.xml")
exporter.export_gift(questions, "quiz.gift")

print("Questions exported to multiple formats")
```

### Multi-language Support

```python
from quizaigen import QuestionGenerator, MultiLanguageConfig

# Configure for multiple languages
config = MultiLanguageConfig(
    source_language="en",
    target_languages=["es", "fr", "de"],
    translation_service="google"
)

# Initialize generator with multi-language support
gen = QuestionGenerator(multilang_config=config)

# Generate questions in multiple languages
text = "The solar system consists of the Sun and eight planets..."
questions = gen.generate_multilingual_mcq(text, num_questions=3)

for lang, lang_questions in questions.items():
    print(f"Generated {len(lang_questions)} questions in {lang}")
```

## Advanced Features

### Custom Question Templates

```python
from quizaigen import QuestionGenerator, QuestionTemplate

# Define custom template
template = QuestionTemplate(
    pattern="What is the {concept} of {subject}?",
    difficulty="medium",
    category="definition"
)

# Use custom template
gen = QuestionGenerator()
questions = gen.generate_from_template(text, template, num_questions=3)
```

### Performance Optimization

```python
from quizaigen import QuestionGenerator, PerformanceConfig

# Configure for better performance
perf_config = PerformanceConfig(
    use_gpu=True,
    enable_caching=True,
    optimize_memory=True,
    parallel_processing=True
)

gen = QuestionGenerator(performance_config=perf_config)
```

### Quality Assessment

```python
from quizaigen import QuestionGenerator, QualityAssessor

# Generate questions
gen = QuestionGenerator()
questions = gen.generate_mcq(text, num_questions=10)

# Assess quality
assessor = QualityAssessor()
quality_scores = assessor.assess_questions(questions)

# Filter high-quality questions
high_quality = [
    q for q, score in zip(questions, quality_scores)
    if score > 0.8
]

print(f"Filtered to {len(high_quality)} high-quality questions")
```

## Troubleshooting

### Common Issues

#### Installation Problems

**Issue**: `pip install quizaigen` fails with dependency conflicts

**Solution**:
```bash
# Create fresh virtual environment
python -m venv fresh_env
source fresh_env/bin/activate
pip install --upgrade pip
pip install quizaigen
```

**Issue**: CUDA out of memory errors

**Solution**:
```python
# Reduce batch size
config = ModelConfig(batch_size=8)  # Default is 32
gen = QuestionGenerator(config=config)

# Or use CPU
config = ModelConfig(device="cpu")
gen = QuestionGenerator(config=config)
```

#### Model Loading Issues

**Issue**: Model download fails or is slow

**Solution**:
```python
# Set custom cache directory
config = ModelConfig(cache_dir="/path/to/fast/storage")
gen = QuestionGenerator(config=config)

# Or download manually
from transformers import AutoModel, AutoTokenizer
model = AutoModel.from_pretrained("distilbert-base-uncased")
tokenizer = AutoTokenizer.from_pretrained("distilbert-base-uncased")
```

#### Quality Issues

**Issue**: Generated questions are low quality

**Solution**:
```python
# Use better model
config = ModelConfig(model_name="bert-large-uncased")
gen = QuestionGenerator(config=config)

# Increase text preprocessing
from quizaigen import TextProcessor
processor = TextProcessor(min_sentence_length=20)
processed_text = processor.clean_text(text)
questions = gen.generate_mcq(processed_text)
```

### Performance Optimization

#### Memory Usage

```python
# Monitor memory usage
import psutil
import os

def get_memory_usage():
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # MB

print(f"Memory before: {get_memory_usage():.1f} MB")
questions = gen.generate_mcq(text)
print(f"Memory after: {get_memory_usage():.1f} MB")
```

#### Speed Optimization

```python
# Use smaller model for faster inference
fast_config = ModelConfig(
    model_name="distilbert-base-uncased",  # Smaller than bert-base
    max_length=256,  # Shorter sequences
    batch_size=64   # Larger batches
)

gen = QuestionGenerator(config=fast_config)
```

### Debugging

#### Enable Logging

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# QuizAIGen specific logging
logger = logging.getLogger("quizaigen")
logger.setLevel(logging.DEBUG)
```

#### Validate Input

```python
from quizaigen.utils import validate_text

# Check if text is suitable for question generation
is_valid, issues = validate_text(text)
if not is_valid:
    print(f"Text validation issues: {issues}")
```

## Contributing

### Development Setup

```bash
# Clone repository
git clone https://github.com/yourusername/quizaigen.git
cd quizaigen

# Create development environment
python -m venv dev_env
source dev_env/bin/activate

# Install in development mode
pip install -e .[dev]

# Install pre-commit hooks
pre-commit install

# Run tests
pytest tests/
```

### Code Style

```bash
# Format code
black quizaigen/ tests/
isort quizaigen/ tests/

# Lint code
flake8 quizaigen/ tests/
mypy quizaigen/

# Security check
bandit -r quizaigen/
```

### Testing

```bash
# Run all tests
pytest tests/

# Run with coverage
pytest tests/ --cov=quizaigen --cov-report=html

# Run specific test categories
pytest tests/ -m "not slow"  # Skip slow tests
pytest tests/ -m "unit"      # Only unit tests
pytest tests/ -m "integration"  # Only integration tests
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Documentation**: https://quizaigen.readthedocs.io
- **Issues**: https://github.com/yourusername/quizaigen/issues
- **Discussions**: https://github.com/yourusername/quizaigen/discussions
- **Email**: <EMAIL>

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a detailed history of changes.

## Acknowledgments

- Hugging Face for the transformers library
- spaCy team for natural language processing tools
- PyTorch team for the deep learning framework
- All contributors and users of QuizAIGen

---

**Note**: This is a template. Customize the content, URLs, and examples to match your specific implementation and requirements.