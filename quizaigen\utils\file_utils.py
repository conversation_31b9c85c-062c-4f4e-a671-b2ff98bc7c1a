"""
QuizAIGen File Utilities

This module provides file handling utilities.
"""

import os
import json
import csv
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import xml.etree.ElementTree as ET

from ..core.exceptions import InputError


def read_file(file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
    """
    Read text content from a file.
    
    Args:
        file_path: Path to the file
        encoding: File encoding
    
    Returns:
        File content as string
    
    Raises:
        InputError: If file cannot be read
    """
    try:
        path = Path(file_path)
        if not path.exists():
            raise InputError('file', f"File not found: {file_path}")
        
        with open(path, 'r', encoding=encoding) as f:
            return f.read()
    
    except Exception as e:
        raise InputError('file', f"Failed to read file {file_path}: {str(e)}")


def write_file(file_path: Union[str, Path], content: str, 
               encoding: str = 'utf-8', create_dirs: bool = True) -> None:
    """
    Write content to a file.
    
    Args:
        file_path: Path to the file
        content: Content to write
        encoding: File encoding
        create_dirs: Whether to create parent directories
    
    Raises:
        InputError: If file cannot be written
    """
    try:
        path = Path(file_path)
        
        if create_dirs:
            path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(path, 'w', encoding=encoding) as f:
            f.write(content)
    
    except Exception as e:
        raise InputError('file', f"Failed to write file {file_path}: {str(e)}")


def read_json(file_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Read JSON data from a file.
    
    Args:
        file_path: Path to the JSON file
    
    Returns:
        Parsed JSON data
    
    Raises:
        InputError: If JSON cannot be read or parsed
    """
    try:
        content = read_file(file_path)
        return json.loads(content)
    
    except json.JSONDecodeError as e:
        raise InputError('json', f"Invalid JSON in file {file_path}: {str(e)}")
    except Exception as e:
        raise InputError('json', f"Failed to read JSON file {file_path}: {str(e)}")


def write_json(file_path: Union[str, Path], data: Any, 
               indent: int = 2, ensure_ascii: bool = False) -> None:
    """
    Write data to a JSON file.
    
    Args:
        file_path: Path to the JSON file
        data: Data to write
        indent: JSON indentation
        ensure_ascii: Whether to ensure ASCII encoding
    
    Raises:
        InputError: If JSON cannot be written
    """
    try:
        content = json.dumps(data, indent=indent, ensure_ascii=ensure_ascii)
        write_file(file_path, content)
    
    except Exception as e:
        raise InputError('json', f"Failed to write JSON file {file_path}: {str(e)}")


def read_csv(file_path: Union[str, Path], delimiter: str = ',') -> List[Dict[str, str]]:
    """
    Read CSV data from a file.
    
    Args:
        file_path: Path to the CSV file
        delimiter: CSV delimiter
    
    Returns:
        List of dictionaries representing CSV rows
    
    Raises:
        InputError: If CSV cannot be read
    """
    try:
        path = Path(file_path)
        if not path.exists():
            raise InputError('csv', f"File not found: {file_path}")
        
        with open(path, 'r', encoding='utf-8', newline='') as f:
            reader = csv.DictReader(f, delimiter=delimiter)
            return list(reader)
    
    except Exception as e:
        raise InputError('csv', f"Failed to read CSV file {file_path}: {str(e)}")


def write_csv(file_path: Union[str, Path], data: List[Dict[str, Any]], 
              delimiter: str = ',', create_dirs: bool = True) -> None:
    """
    Write data to a CSV file.
    
    Args:
        file_path: Path to the CSV file
        data: List of dictionaries to write
        delimiter: CSV delimiter
        create_dirs: Whether to create parent directories
    
    Raises:
        InputError: If CSV cannot be written
    """
    try:
        if not data:
            return
        
        path = Path(file_path)
        if create_dirs:
            path.parent.mkdir(parents=True, exist_ok=True)
        
        fieldnames = list(data[0].keys())
        
        with open(path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, delimiter=delimiter)
            writer.writeheader()
            writer.writerows(data)
    
    except Exception as e:
        raise InputError('csv', f"Failed to write CSV file {file_path}: {str(e)}")


def read_xml(file_path: Union[str, Path]) -> ET.Element:
    """
    Read XML data from a file.
    
    Args:
        file_path: Path to the XML file
    
    Returns:
        XML root element
    
    Raises:
        InputError: If XML cannot be read or parsed
    """
    try:
        path = Path(file_path)
        if not path.exists():
            raise InputError('xml', f"File not found: {file_path}")
        
        tree = ET.parse(path)
        return tree.getroot()
    
    except ET.ParseError as e:
        raise InputError('xml', f"Invalid XML in file {file_path}: {str(e)}")
    except Exception as e:
        raise InputError('xml', f"Failed to read XML file {file_path}: {str(e)}")


def write_xml(file_path: Union[str, Path], root: ET.Element, 
              create_dirs: bool = True) -> None:
    """
    Write XML data to a file.
    
    Args:
        file_path: Path to the XML file
        root: XML root element
        create_dirs: Whether to create parent directories
    
    Raises:
        InputError: If XML cannot be written
    """
    try:
        path = Path(file_path)
        if create_dirs:
            path.parent.mkdir(parents=True, exist_ok=True)
        
        tree = ET.ElementTree(root)
        tree.write(path, encoding='utf-8', xml_declaration=True)
    
    except Exception as e:
        raise InputError('xml', f"Failed to write XML file {file_path}: {str(e)}")


def get_file_extension(file_path: Union[str, Path]) -> str:
    """
    Get file extension from path.
    
    Args:
        file_path: Path to the file
    
    Returns:
        File extension (without dot)
    """
    return Path(file_path).suffix.lower().lstrip('.')


def get_file_size(file_path: Union[str, Path]) -> int:
    """
    Get file size in bytes.
    
    Args:
        file_path: Path to the file
    
    Returns:
        File size in bytes
    
    Raises:
        InputError: If file cannot be accessed
    """
    try:
        path = Path(file_path)
        if not path.exists():
            raise InputError('file', f"File not found: {file_path}")
        
        return path.stat().st_size
    
    except Exception as e:
        raise InputError('file', f"Failed to get file size for {file_path}: {str(e)}")


def ensure_directory(dir_path: Union[str, Path]) -> None:
    """
    Ensure directory exists, create if it doesn't.
    
    Args:
        dir_path: Path to the directory
    
    Raises:
        InputError: If directory cannot be created
    """
    try:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    except Exception as e:
        raise InputError('directory', f"Failed to create directory {dir_path}: {str(e)}")


def list_files(directory: Union[str, Path], pattern: str = "*", 
               recursive: bool = False) -> List[Path]:
    """
    List files in a directory.
    
    Args:
        directory: Directory path
        pattern: File pattern (glob style)
        recursive: Whether to search recursively
    
    Returns:
        List of file paths
    
    Raises:
        InputError: If directory cannot be accessed
    """
    try:
        path = Path(directory)
        if not path.exists():
            raise InputError('directory', f"Directory not found: {directory}")
        
        if not path.is_dir():
            raise InputError('directory', f"Path is not a directory: {directory}")
        
        if recursive:
            return list(path.rglob(pattern))
        else:
            return list(path.glob(pattern))
    
    except Exception as e:
        raise InputError('directory', f"Failed to list files in {directory}: {str(e)}")


def is_text_file(file_path: Union[str, Path]) -> bool:
    """
    Check if a file is a text file based on extension.
    
    Args:
        file_path: Path to the file
    
    Returns:
        True if file is likely a text file
    """
    text_extensions = {
        'txt', 'md', 'rst', 'py', 'js', 'html', 'htm', 'css', 'json', 
        'xml', 'yaml', 'yml', 'csv', 'tsv', 'log', 'cfg', 'conf', 'ini'
    }
    
    extension = get_file_extension(file_path)
    return extension in text_extensions
