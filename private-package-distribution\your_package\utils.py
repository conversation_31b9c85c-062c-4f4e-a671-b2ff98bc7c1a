"""
Utility Functions and Helpers
=============================

Common utilities and helper functions for the package.
"""

import os
import sys
import platform
import json
import hashlib
from typing import Dict, Any, List, Optional
from pathlib import Path


def get_package_info() -> Dict[str, Any]:
    """
    Get comprehensive package information.
    
    Returns:
        Dict containing package metadata and system info
    """
    from . import __version__, __author__, __email__
    
    return {
        "package": {
            "name": "your-commercial-package",
            "version": __version__,
            "author": __author__,
            "email": __email__,
        },
        "system": {
            "platform": platform.platform(),
            "python_version": sys.version,
            "architecture": platform.architecture(),
            "processor": platform.processor(),
            "hostname": platform.node(),
        },
        "environment": {
            "python_executable": sys.executable,
            "python_path": sys.path[:3],  # First 3 entries
            "working_directory": os.getcwd(),
            "user_home": str(Path.home()),
        }
    }


def validate_environment() -> Dict[str, Any]:
    """
    Validate the current environment for package compatibility.
    
    Returns:
        Dict containing validation results
    """
    issues = []
    warnings = []
    
    # Check Python version
    if sys.version_info < (3, 8):
        issues.append(f"Python 3.8+ required, found {sys.version_info.major}.{sys.version_info.minor}")
    
    # Check required modules
    required_modules = [
        'requests', 'cryptography', 'jwt', 'click', 'pydantic'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        issues.append(f"Missing required modules: {', '.join(missing_modules)}")
    
    # Check write permissions for license storage
    license_dirs = [
        Path.home() / '.your_package',
        Path('/etc/your_package'),
        Path.cwd()
    ]
    
    writable_dirs = []
    for dir_path in license_dirs:
        try:
            if dir_path.exists() or dir_path.parent.exists():
                test_file = dir_path / 'test_write'
                test_file.parent.mkdir(parents=True, exist_ok=True)
                test_file.write_text('test')
                test_file.unlink()
                writable_dirs.append(str(dir_path))
        except (PermissionError, OSError):
            continue
    
    if not writable_dirs:
        warnings.append("No writable directories found for license storage")
    
    # Check network connectivity (for license validation)
    try:
        import requests
        response = requests.get('https://httpbin.org/status/200', timeout=5)
        network_ok = response.status_code == 200
    except Exception:
        network_ok = False
        warnings.append("Network connectivity issues detected")
    
    return {
        "valid": len(issues) == 0,
        "issues": issues,
        "warnings": warnings,
        "details": {
            "python_version_ok": sys.version_info >= (3, 8),
            "required_modules_ok": len(missing_modules) == 0,
            "writable_dirs": writable_dirs,
            "network_ok": network_ok
        }
    }


def generate_support_info() -> Dict[str, Any]:
    """
    Generate comprehensive support information for troubleshooting.
    
    Returns:
        Dict containing detailed support information
    """
    from .license import get_license_validator
    
    # Get package and system info
    package_info = get_package_info()
    env_validation = validate_environment()
    
    # Get license information (safely)
    license_info = {}
    try:
        validator = get_license_validator()
        license_info = validator.get_license_info()
        # Remove sensitive information
        if 'customer_id' in license_info:
            license_info['customer_id'] = license_info['customer_id'][:8] + '...'
    except Exception as e:
        license_info = {"error": str(e)}
    
    # Generate system fingerprint for support
    system_data = json.dumps({
        "platform": platform.platform(),
        "python": sys.version,
        "architecture": platform.architecture()
    }, sort_keys=True)
    
    support_fingerprint = hashlib.sha256(system_data.encode()).hexdigest()[:16]
    
    return {
        "support_fingerprint": support_fingerprint,
        "package_info": package_info,
        "environment_validation": env_validation,
        "license_info": license_info,
        "timestamp": "2024-01-01T00:00:00Z"
    }


def create_license_directory() -> Path:
    """
    Create directory for license storage.
    
    Returns:
        Path to created license directory
    """
    license_dir = Path.home() / '.your_package'
    license_dir.mkdir(parents=True, exist_ok=True)
    
    # Create .gitignore to prevent accidental commits
    gitignore_file = license_dir / '.gitignore'
    if not gitignore_file.exists():
        gitignore_file.write_text('*.key\n*.license\n*.json\n')
    
    return license_dir


def save_license_key(license_key: str, file_path: Optional[str] = None) -> str:
    """
    Save license key to file.
    
    Args:
        license_key: License key to save
        file_path: Optional custom file path
        
    Returns:
        Path where license was saved
    """
    if file_path:
        save_path = Path(file_path)
    else:
        license_dir = create_license_directory()
        save_path = license_dir / 'license.key'
    
    # Ensure parent directory exists
    save_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save license key
    save_path.write_text(license_key.strip())
    
    # Set restrictive permissions (Unix-like systems)
    try:
        save_path.chmod(0o600)
    except (OSError, AttributeError):
        # Windows or permission error
        pass
    
    return str(save_path)


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Load package configuration from file.
    
    Args:
        config_path: Optional path to config file
        
    Returns:
        Configuration dictionary
    """
    if config_path:
        config_file = Path(config_path)
    else:
        # Try default locations
        config_locations = [
            Path.home() / '.your_package' / 'config.json',
            Path('/etc/your_package/config.json'),
            Path.cwd() / 'your_package_config.json'
        ]
        
        config_file = None
        for location in config_locations:
            if location.exists():
                config_file = location
                break
    
    if not config_file or not config_file.exists():
        return {}
    
    try:
        return json.loads(config_file.read_text())
    except (json.JSONDecodeError, OSError) as e:
        raise ValueError(f"Failed to load config from {config_file}: {e}")


def save_config(config: Dict[str, Any], config_path: Optional[str] = None) -> str:
    """
    Save package configuration to file.
    
    Args:
        config: Configuration dictionary to save
        config_path: Optional path to save config
        
    Returns:
        Path where config was saved
    """
    if config_path:
        save_path = Path(config_path)
    else:
        license_dir = create_license_directory()
        save_path = license_dir / 'config.json'
    
    # Ensure parent directory exists
    save_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save configuration
    save_path.write_text(json.dumps(config, indent=2))
    
    return str(save_path)


def check_for_updates() -> Dict[str, Any]:
    """
    Check for package updates.
    
    Returns:
        Dict containing update information
    """
    from . import __version__
    
    try:
        import requests
        
        response = requests.get(
            'https://api.yourcompany.com/package/version',
            timeout=10,
            headers={'User-Agent': f'YourPackage/{__version__}'}
        )
        
        if response.status_code == 200:
            data = response.json()
            latest_version = data.get('latest_version')
            
            return {
                "current_version": __version__,
                "latest_version": latest_version,
                "update_available": latest_version != __version__,
                "download_url": data.get('download_url'),
                "release_notes": data.get('release_notes', '')
            }
        else:
            return {
                "current_version": __version__,
                "error": f"Failed to check for updates: {response.status_code}"
            }
            
    except Exception as e:
        return {
            "current_version": __version__,
            "error": f"Failed to check for updates: {str(e)}"
        }
