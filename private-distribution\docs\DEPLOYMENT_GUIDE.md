# QuizAIGen Private Distribution Deployment Guide

## Overview

This guide covers the complete deployment of QuizAIGen's private distribution system, including:

- Private PyPI server
- License management API
- Customer portal
- SaaS integration
- Monitoring and maintenance

## Prerequisites

### System Requirements

- **Server**: Linux (Ubuntu 20.04+ recommended)
- **RAM**: Minimum 8GB, Recommended 16GB+
- **Storage**: Minimum 100GB SSD
- **CPU**: 4+ cores
- **Network**: Static IP with domain name

### Software Requirements

- Docker 20.10+
- Docker Compose 2.0+
- Git
- SSL certificates (Let's Encrypt recommended)

### Domain Setup

You'll need the following subdomains:
- `pypi.yourdomain.com` - Private PyPI server
- `api.yourdomain.com` - License API
- `portal.yourdomain.com` - Customer portal
- `app.yourdomain.com` - Main SaaS application

## Quick Start

### 1. Clone Repository

```bash
git clone https://github.com/yourcompany/quizaigen-distribution.git
cd quizaigen-distribution
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

Required environment variables:
```bash
# Security
JWT_SECRET_KEY=your-jwt-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here
GRAFANA_PASSWORD=your-grafana-password

# Database
POSTGRES_PASSWORD=your-postgres-password

# Email
SMTP_SERVER=smtp.yourdomain.com
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-smtp-password

# Backup
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret

# Monitoring
SLACK_WEBHOOK_URL=your-slack-webhook
```

### 3. SSL Certificates

```bash
# Using Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d pypi.yourdomain.com
sudo certbot certonly --standalone -d api.yourdomain.com
sudo certbot certonly --standalone -d portal.yourdomain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/*/fullchain.pem ./ssl/
sudo cp /etc/letsencrypt/live/*/privkey.pem ./ssl/
```

### 4. Initial Deployment

```bash
# Start core services
docker-compose up -d postgres redis

# Wait for database to be ready
sleep 30

# Initialize database
docker-compose exec license-api python manage.py migrate

# Start all services
docker-compose up -d
```

### 5. Verify Deployment

```bash
# Check service status
docker-compose ps

# Check logs
docker-compose logs -f license-api

# Test endpoints
curl https://api.yourdomain.com/health
curl https://pypi.yourdomain.com/simple/
```

## Detailed Configuration

### Private PyPI Server

The PyPI server uses `pypiserver` with authentication:

```yaml
# config/pypi-server/htpasswd
# Generate with: htpasswd -c htpasswd username
customer1:$2y$05$encrypted_password_hash
customer2:$2y$05$encrypted_password_hash
```

### License API Configuration

```python
# config/license-api/settings.py
DATABASE_URL = "************************************************/license_db"
JWT_SECRET_KEY = "your-secret-key"
ENCRYPTION_KEY = "your-encryption-key"

# License validation settings
LICENSE_VALIDATION_TIMEOUT = 30  # seconds
HARDWARE_BINDING_ENABLED = True
USAGE_TRACKING_ENABLED = True
```

### Customer Portal

```javascript
// config/portal/config.js
export const config = {
  apiUrl: 'https://api.yourdomain.com',
  pypiUrl: 'https://pypi.yourdomain.com',
  supportEmail: '<EMAIL>',
  features: {
    selfService: true,
    downloadHistory: true,
    usageAnalytics: true
  }
};
```

## Package Building and Distribution

### 1. Build Commercial Packages

```bash
# Configure build
cp build-config.example.json build-config.json
nano build-config.json

# Build all tiers
python build-scripts/build_commercial_package.py build-config.json --tier all

# Upload to PyPI server
python scripts/upload_packages.py --tier premium --server https://pypi.yourdomain.com
```

### 2. License Key Generation

```bash
# Generate license keys
python scripts/generate_license.py \
  --customer-id "customer-123" \
  --tier premium \
  --features "fill_blank,advanced_export" \
  --duration-months 12
```

### 3. Customer Onboarding

```bash
# Automated onboarding
python automation/customer_onboarding.py \
  config/onboarding.json \
  customer-data.json
```

## Monitoring and Maintenance

### Health Checks

The system includes comprehensive health monitoring:

```bash
# Manual health check
curl https://api.yourdomain.com/health

# Prometheus metrics
curl https://api.yourdomain.com/metrics

# Service status
docker-compose ps
```

### Log Management

Logs are centralized using ELK stack:

- **Elasticsearch**: Log storage
- **Logstash**: Log processing
- **Kibana**: Log visualization

Access Kibana at: `https://yourdomain.com:5601`

### Backup Procedures

Automated backups run daily:

```bash
# Manual backup
docker-compose exec backup-service python backup.py --full

# Restore from backup
docker-compose exec backup-service python restore.py --date 2024-01-01
```

### Updates and Maintenance

```bash
# Update system
git pull origin main
docker-compose pull
docker-compose up -d

# Database migrations
docker-compose exec license-api python manage.py migrate

# Clear caches
docker-compose exec redis redis-cli FLUSHALL
```

## Security Considerations

### Network Security

- Use firewall to restrict access
- Enable fail2ban for SSH protection
- Use VPN for administrative access

### Application Security

- Regular security updates
- SSL/TLS encryption everywhere
- Strong password policies
- Regular security audits

### Data Protection

- Encrypt sensitive data at rest
- Regular backups with encryption
- Access logging and monitoring
- GDPR compliance measures

## Scaling

### Horizontal Scaling

```yaml
# docker-compose.scale.yml
services:
  license-api:
    deploy:
      replicas: 3
  
  pypi-server:
    deploy:
      replicas: 2
```

### Load Balancing

Use Traefik for automatic load balancing:

```yaml
# traefik configuration
labels:
  - "traefik.enable=true"
  - "traefik.http.services.api.loadbalancer.server.port=8000"
  - "traefik.http.routers.api.rule=Host(`api.yourdomain.com`)"
```

### Database Scaling

For high load, consider:
- Read replicas
- Connection pooling
- Database sharding

## Troubleshooting

### Common Issues

1. **License validation fails**
   ```bash
   # Check license API logs
   docker-compose logs license-api
   
   # Verify JWT configuration
   docker-compose exec license-api python check_jwt.py
   ```

2. **PyPI upload fails**
   ```bash
   # Check authentication
   curl -u username:password https://pypi.yourdomain.com/simple/
   
   # Verify package format
   python -m twine check dist/*
   ```

3. **Customer portal not loading**
   ```bash
   # Check nginx configuration
   docker-compose exec nginx nginx -t
   
   # Verify SSL certificates
   openssl x509 -in ssl/cert.pem -text -noout
   ```

### Performance Optimization

1. **Database optimization**
   ```sql
   -- Add indexes for common queries
   CREATE INDEX idx_licenses_customer_id ON licenses(customer_id);
   CREATE INDEX idx_usage_timestamp ON usage_logs(timestamp);
   ```

2. **Redis optimization**
   ```bash
   # Increase memory limit
   echo "maxmemory 2gb" >> config/redis/redis.conf
   echo "maxmemory-policy allkeys-lru" >> config/redis/redis.conf
   ```

3. **Nginx optimization**
   ```nginx
   # Enable gzip compression
   gzip on;
   gzip_types text/plain application/json application/javascript;
   
   # Enable caching
   location /static/ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

## Support and Maintenance

### Regular Maintenance Tasks

- **Daily**: Check service health, review logs
- **Weekly**: Update packages, review security alerts
- **Monthly**: Full backup verification, performance review
- **Quarterly**: Security audit, capacity planning

### Emergency Procedures

1. **Service outage**
   - Check service status
   - Review recent changes
   - Restore from backup if needed
   - Notify customers

2. **Security incident**
   - Isolate affected systems
   - Preserve evidence
   - Patch vulnerabilities
   - Update security measures

### Contact Information

- **Technical Support**: <EMAIL>
- **Security Issues**: <EMAIL>
- **Emergency**: +1-555-EMERGENCY

## Appendix

### Useful Commands

```bash
# View all logs
docker-compose logs -f

# Restart specific service
docker-compose restart license-api

# Scale service
docker-compose up -d --scale license-api=3

# Database backup
docker-compose exec postgres pg_dump -U license_user license_db > backup.sql

# Redis backup
docker-compose exec redis redis-cli BGSAVE
```

### Configuration Templates

See the `config/` directory for complete configuration templates and examples.

### API Documentation

Complete API documentation is available at: `https://api.yourdomain.com/docs`
