"""
FAQ-Style Question Generator

This module generates FAQ-style short answer questions from text.
"""

import re
import random
from typing import List, Dict, Any, Optional

from .base import BaseQuestionGenerator, Question
from ..utils.text_utils import extract_sentences, extract_keywords


class FAQGenerator(BaseQuestionGenerator):
    """Generator for FAQ-style short answer questions."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize ShortAnswerGenerator.
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        self.question_types = [
            'what_is',
            'who_is', 
            'when_did',
            'where_is',
            'why_does',
            'how_does',
            'define',
            'explain'
        ]
        self.log_info("Initialized FAQGenerator")

    def _get_question_type(self) -> str:
        """Get the question type identifier."""
        return "faq"

    def _generate_questions_impl(self, text: str, num_questions: int, **kwargs) -> List[Question]:
        """
        Generate short answer questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of generated questions
        """
        # Extract sentences and keywords
        sentences = extract_sentences(text)
        if len(sentences) < 2:
            self.log_warning("Text has too few sentences for short answer generation")
            return []
        
        # Extract important information
        keywords = extract_keywords(text, top_k=20)
        entities = self._extract_entities(text)
        
        questions = []
        used_sentences = set()
        
        for _ in range(num_questions * 3):  # Try more attempts
            if len(questions) >= num_questions:
                break
                
            # Select a sentence that hasn't been used
            available_sentences = [s for i, s in enumerate(sentences) 
                                 if i not in used_sentences and len(s.split()) > 5]
            
            if not available_sentences:
                break
                
            sentence = random.choice(available_sentences)
            sentence_idx = sentences.index(sentence)
            
            # Generate question from sentence
            question_data = self._generate_question_from_sentence(
                sentence, keywords, entities
            )
            
            if question_data:
                questions.append(question_data)
                used_sentences.add(sentence_idx)
        
        return questions[:num_questions]
    
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        Extract named entities from text.
        
        Args:
            text: Input text
            
        Returns:
            Dictionary of entity types and their values
        """
        entities = {
            'persons': [],
            'places': [],
            'organizations': [],
            'dates': [],
            'numbers': []
        }
        
        # Simple pattern-based entity extraction
        # Person names (comprehensive pattern for various name formats)
        person_patterns = [
            r'\b[A-Z][a-z]+(?:[-\s][A-Z][a-z]+)*\s+[A-Z][a-z]+\b',  # Hyphenated and compound names
            r'\b[A-Z]\.?[A-Z]\.?[A-Z]\.\s+[A-Z][a-z]+\b',  # Initials like J.R.R. Tolkien
            r'\b[A-Z][a-z]+\s+[a-z]{2,3}\s+[A-Z][a-z]+\b',  # Names with lowercase middle (van, de, da, etc.)
        ]
        all_persons = []
        for pattern in person_patterns:
            all_persons.extend(re.findall(pattern, text))
        entities['persons'] = list(set(all_persons))

        # Places (words ending with common place suffixes or known places)
        place_pattern = r'\b[A-Z][a-z]*(?:town|city|state|country|land|burg|ville)\b'
        entities['places'] = list(set(re.findall(place_pattern, text)))

        # Organizations (words with Corp, Inc, Ltd, etc.)
        org_pattern = r'\b[A-Z][a-zA-Z\s]*(?:Corp|Inc|Ltd|Company|Organization|Institute)\b'
        entities['organizations'] = list(set(re.findall(org_pattern, text)))

        # Dates (various date formats including just years)
        date_pattern = r'\b(?:\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{4}|\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4})\b'
        entities['dates'] = list(set(re.findall(date_pattern, text)))

        # Numbers
        number_pattern = r'\b\d+(?:\.\d+)?\b'
        entities['numbers'] = list(set(re.findall(number_pattern, text)))
        
        return entities
    
    def _generate_question_from_sentence(self, sentence: str, keywords: List[str], 
                                       entities: Dict[str, List[str]]) -> Optional[Question]:
        """
        Generate a short answer question from a sentence.
        
        Args:
            sentence: Source sentence
            keywords: List of important keywords
            entities: Dictionary of named entities
            
        Returns:
            Generated question or None
        """
        sentence = sentence.strip()
        if not sentence or len(sentence.split()) < 5:
            return None
        
        # Try different question generation strategies
        strategies = [
            self._generate_what_question,
            self._generate_who_question,
            self._generate_when_question,
            self._generate_where_question,
            self._generate_why_question,
            self._generate_how_question,
            self._generate_definition_question
        ]
        
        random.shuffle(strategies)
        
        for strategy in strategies:
            question_data = strategy(sentence, keywords, entities)
            if question_data:
                return question_data
        
        return None
    
    def _generate_what_question(self, sentence: str, keywords: List[str], 
                              entities: Dict[str, List[str]]) -> Optional[Question]:
        """Generate 'What is/are/does' questions."""
        # Look for definitions or descriptions
        if any(word in sentence.lower() for word in ['is', 'are', 'means', 'refers to', 'defined as']):
            # Find the subject being defined
            words = sentence.split()
            for i, word in enumerate(words):
                if word.lower() in ['is', 'are'] and i > 0:
                    subject = ' '.join(words[:i])
                    definition = ' '.join(words[i+1:])
                    
                    if len(subject.split()) <= 3 and len(definition.split()) >= 3:
                        question_text = f"What is {subject.strip()}?"
                        answer = definition.strip().rstrip('.')
                        
                        return Question(
                            question=question_text,
                            answer=answer,
                            type='short_answer',
                            difficulty='medium',
                            confidence=0.8,
                            source_text=sentence,
                            metadata={
                                'question_type': 'what_is',
                                'subject': subject.strip()
                            }
                        )
        
        return None
    
    def _generate_who_question(self, sentence: str, keywords: List[str], 
                             entities: Dict[str, List[str]]) -> Optional[Question]:
        """Generate 'Who is/was/did' questions."""
        persons = entities.get('persons', [])
        if not persons:
            return None
        
        person = random.choice(persons)
        if person in sentence:
            # Create question by replacing person with "Who"
            question_text = sentence.replace(person, "Who")
            if not question_text.endswith('?'):
                question_text = question_text.rstrip('.') + '?'
            
            return Question(
                question=question_text,
                answer=person,
                type='short_answer',
                difficulty='easy',
                confidence=0.7,
                source_text=sentence,
                metadata={
                    'question_type': 'who_is',
                    'entity': person
                }
            )
        
        return None
    
    def _generate_when_question(self, sentence: str, keywords: List[str],
                              entities: Dict[str, List[str]]) -> Optional[Question]:
        """Generate 'When did/was' questions."""
        dates = entities.get('dates', [])
        if not dates:
            return None

        date = random.choice(dates)
        if date in sentence:
            # Create more natural questions based on sentence structure
            sentence_lower = sentence.lower()

            # Look for creation/founding/invention patterns
            if any(word in sentence_lower for word in ['created', 'founded', 'invented', 'developed', 'built', 'written']):
                # Extract the main subject
                words = sentence.split()
                subject = None
                for i, word in enumerate(words):
                    if word.lower() in ['created', 'founded', 'invented', 'developed', 'built', 'written']:
                        # Look for subject before the action word
                        if i > 0:
                            subject_words = []
                            for j in range(i):
                                if words[j][0].isupper() and not words[j].lower() in ['the', 'a', 'an']:
                                    subject_words.append(words[j])
                            if subject_words:
                                subject = ' '.join(subject_words)
                                break

                if subject:
                    question_text = f"When was {subject} created?"
                else:
                    question_text = f"When did this happen?"
            else:
                # Generic when question
                question_text = f"When did the events mentioned occur?"

            return Question(
                question=question_text,
                answer=date,
                type='short_answer',
                difficulty='easy',
                confidence=0.7,
                source_text=sentence,
                metadata={
                    'question_type': 'when_did',
                    'date': date
                }
            )

        return None
    
    def _generate_where_question(self, sentence: str, keywords: List[str], 
                               entities: Dict[str, List[str]]) -> Optional[Question]:
        """Generate 'Where is/was' questions."""
        places = entities.get('places', [])
        if not places:
            return None
        
        place = random.choice(places)
        if place in sentence:
            # Create question by replacing place with "Where"
            question_text = sentence.replace(place, "Where")
            if not question_text.endswith('?'):
                question_text = question_text.rstrip('.') + '?'
            
            return Question(
                question=question_text,
                answer=place,
                type='short_answer',
                difficulty='easy',
                confidence=0.7,
                source_text=sentence,
                metadata={
                    'question_type': 'where_is',
                    'place': place
                }
            )
        
        return None
    
    def _generate_why_question(self, sentence: str, keywords: List[str], 
                             entities: Dict[str, List[str]]) -> Optional[Question]:
        """Generate 'Why does/is' questions."""
        # Look for causal relationships
        causal_words = ['because', 'since', 'due to', 'as a result', 'therefore', 'thus']
        
        for word in causal_words:
            if word in sentence.lower():
                parts = sentence.lower().split(word)
                if len(parts) == 2:
                    cause = parts[1].strip()
                    effect = parts[0].strip()
                    
                    if len(cause.split()) >= 3:
                        question_text = f"Why {effect.rstrip(',')}?"
                        answer = cause.capitalize()
                        
                        return Question(
                            question=question_text,
                            answer=answer,
                            type='short_answer',
                            difficulty='hard',
                            confidence=0.6,
                            source_text=sentence,
                            metadata={
                                'question_type': 'why_does',
                                'causal_word': word
                            }
                        )
        
        return None
    
    def _generate_how_question(self, sentence: str, keywords: List[str], 
                             entities: Dict[str, List[str]]) -> Optional[Question]:
        """Generate 'How does/is' questions."""
        # Look for process descriptions
        process_words = ['process', 'method', 'way', 'procedure', 'technique']
        
        if any(word in sentence.lower() for word in process_words):
            # Simple how question generation
            if 'by' in sentence.lower():
                parts = sentence.lower().split('by')
                if len(parts) == 2:
                    process = parts[1].strip()
                    result = parts[0].strip()
                    
                    question_text = f"How {result.rstrip(',')}?"
                    answer = f"By {process}"
                    
                    return Question(
                        question=question_text,
                        answer=answer,
                        type='short_answer',
                        difficulty='medium',
                        confidence=0.6,
                        source_text=sentence,
                        metadata={
                            'question_type': 'how_does'
                        }
                    )
        
        return None
    
    def _generate_definition_question(self, sentence: str, keywords: List[str], 
                                    entities: Dict[str, List[str]]) -> Optional[Question]:
        """Generate definition questions."""
        # Look for important terms that could be defined
        important_words = [word for word in keywords if len(word) > 4 and word.lower() in sentence.lower()]
        
        if important_words:
            term = random.choice(important_words)
            question_text = f"Define {term}."
            
            # Extract definition from context
            sentences = sentence.split('.')
            for sent in sentences:
                if term.lower() in sent.lower():
                    answer = sent.strip()
                    if answer:
                        return Question(
                            question=question_text,
                            answer=answer,
                            type='short_answer',
                            difficulty='medium',
                            confidence=0.5,
                            source_text=sentence,
                            metadata={
                                'question_type': 'define',
                                'term': term
                            }
                        )
        
        return None
