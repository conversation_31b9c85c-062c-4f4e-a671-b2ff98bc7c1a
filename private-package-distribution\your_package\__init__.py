"""
Commercial Python Package for SaaS Application
==============================================

A secure, licensed Python package for commercial distribution.

Copyright (c) 2024 Your Company. All rights reserved.
This software is proprietary and confidential.
"""

import os
import sys
from typing import Optional

# Version management
__version__ = "1.0.0"
__author__ = "Your Company"
__email__ = "<EMAIL>"
__license__ = "Commercial License"

# License validation imports
from .license import LicenseValidator, LicenseError
from .core import YourPackageCore
from .utils import get_package_info, validate_environment

# Global license validator instance
_license_validator: Optional[LicenseValidator] = None


def initialize_package(license_key: Optional[str] = None, 
                      license_file: Optional[str] = None,
                      validate_on_import: bool = True) -> bool:
    """
    Initialize the package with license validation.
    
    Args:
        license_key: License key string
        license_file: Path to license file
        validate_on_import: Whether to validate license immediately
        
    Returns:
        bool: True if initialization successful
        
    Raises:
        LicenseError: If license validation fails
    """
    global _license_validator
    
    try:
        # Initialize license validator
        _license_validator = LicenseValidator()
        
        # Try to load license from various sources
        if license_key:
            _license_validator.set_license_key(license_key)
        elif license_file:
            _license_validator.load_license_file(license_file)
        else:
            # Try environment variable
            env_license = os.getenv('YOUR_PACKAGE_LICENSE_KEY')
            if env_license:
                _license_validator.set_license_key(env_license)
            else:
                # Try default license file locations
                default_locations = [
                    os.path.expanduser('~/.your_package/license.key'),
                    '/etc/your_package/license.key',
                    './license.key'
                ]
                
                for location in default_locations:
                    if os.path.exists(location):
                        _license_validator.load_license_file(location)
                        break
        
        # Validate license if required
        if validate_on_import:
            if not _license_validator.validate():
                raise LicenseError("Invalid or missing license")
        
        return True
        
    except Exception as e:
        if validate_on_import:
            raise LicenseError(f"Package initialization failed: {str(e)}")
        return False


def get_license_info() -> dict:
    """Get current license information."""
    if _license_validator is None:
        return {"status": "not_initialized"}
    
    return _license_validator.get_license_info()


def is_licensed() -> bool:
    """Check if package is properly licensed."""
    if _license_validator is None:
        return False
    
    return _license_validator.validate()


# Auto-initialize on import (can be disabled via environment variable)
if os.getenv('YOUR_PACKAGE_SKIP_AUTO_INIT', '').lower() not in ('1', 'true', 'yes'):
    try:
        initialize_package(validate_on_import=True)
    except LicenseError:
        # Allow import but warn about licensing
        import warnings
        warnings.warn(
            "Package imported without valid license. "
            "Some features may be restricted. "
            "<NAME_EMAIL> for licensing.",
            UserWarning,
            stacklevel=2
        )


# Public API exports
__all__ = [
    '__version__',
    '__author__',
    '__email__',
    '__license__',
    'initialize_package',
    'get_license_info',
    'is_licensed',
    'YourPackageCore',
    'LicenseError',
    'get_package_info',
    'validate_environment',
]


# Convenience imports for common use cases
try:
    if is_licensed():
        # Only expose full API if properly licensed
        from .api import *  # noqa: F401,F403
        from .advanced import *  # noqa: F401,F403
    else:
        # Limited API for unlicensed usage
        from .api import BasicAPI  # noqa: F401
        
except ImportError:
    # Graceful degradation if modules are missing
    pass


def _check_python_version():
    """Ensure minimum Python version requirement."""
    if sys.version_info < (3, 8):
        raise RuntimeError(
            f"This package requires Python 3.8 or later. "
            f"Current version: {sys.version_info.major}.{sys.version_info.minor}"
        )


# Version check on import
_check_python_version()
