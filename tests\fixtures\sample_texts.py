"""
Sample texts for testing QuizAIGen

Provides a collection of sample texts in different languages,
topics, and complexity levels for comprehensive testing.
"""

from typing import Dict, List


class SampleTexts:
    """Collection of sample texts for testing."""
    
    PROGRAMMING_TEXTS = {
        'python_basics': """
        Python is a high-level, interpreted programming language with dynamic semantics.
        Its high-level built-in data structures, combined with dynamic typing and dynamic binding,
        make it very attractive for Rapid Application Development, as well as for use as a
        scripting or glue language to connect existing components together.
        
        Python's simple, easy to learn syntax emphasizes readability and therefore reduces
        the cost of program maintenance. Python supports modules and packages, which encourages
        program modularity and code reuse.
        """,
        
        'machine_learning': """
        Machine learning is a subset of artificial intelligence that focuses on the use of data
        and algorithms to imitate the way that humans learn, gradually improving its accuracy.
        Machine learning is an important component of the growing field of data science.
        
        Through the use of statistical methods, algorithms are trained to make classifications
        or predictions, uncovering key insights within data mining projects. These insights
        subsequently drive decision making within applications and businesses.
        """,
        
        'web_development': """
        Web development refers to the creating, building, and maintaining of websites.
        It includes aspects such as web design, web publishing, web programming, and database management.
        It is the creation of an application that works over the internet i.e. websites.
        
        Web development can be divided into two parts: frontend development and backend development.
        Frontend development deals with the user interface, while backend development handles
        the server-side logic and database interactions.
        """
    }
    
    SCIENCE_TEXTS = {
        'climate_change': """
        Climate change refers to long-term shifts in global or regional climate patterns.
        Since the mid-20th century, scientists have observed that the primary cause of climate change
        is human activities, particularly the burning of fossil fuels, which increases heat-trapping
        greenhouse gas levels in Earth's atmosphere.
        
        The effects of climate change include rising sea levels, changing precipitation patterns,
        and more frequent extreme weather events. These changes pose significant challenges
        to ecosystems, human health, and economic systems worldwide.
        """,
        
        'renewable_energy': """
        Renewable energy comes from natural sources or processes that are constantly replenished.
        For example, sunlight or wind keep shining and blowing, even if their availability
        depends on time and weather. While renewable energy is often thought of as a new technology,
        harnessing nature's power has long been used for heating, transportation, lighting, and more.
        
        The most common renewable energy sources include solar, wind, hydroelectric, biomass,
        and geothermal energy. These sources are becoming increasingly important as the world
        seeks to reduce greenhouse gas emissions and combat climate change.
        """
    }
    
    MULTILINGUAL_TEXTS = {
        'spanish': {
            'programming': """
            Python es un lenguaje de programación interpretado cuya filosofía hace hincapié
            en la legibilidad de su código. Se trata de un lenguaje de programación
            multiparadigma, ya que soporta orientación a objetos, programación imperativa
            y, en menor medida, programación funcional.
            
            Es un lenguaje interpretado, dinámico y multiplataforma. Es administrado por
            la Python Software Foundation. Posee una licencia de código abierto,
            denominada Python Software Foundation License.
            """,
            
            'science': """
            La inteligencia artificial es la combinación de algoritmos planteados con el
            propósito de crear máquinas que presenten las mismas capacidades que el ser humano.
            Una tecnología que todavía nos resulta lejana y misteriosa, pero que desde hace
            unos años está presente en nuestro día a día a todas horas.
            """
        },
        
        'french': {
            'programming': """
            Python est un langage de programmation interprété, multi-paradigme et
            multiplateformes. Il favorise la programmation impérative structurée,
            fonctionnelle et orientée objet. Il est doté d'un typage dynamique fort,
            d'une gestion automatique de la mémoire par ramasse-miettes et d'un système
            de gestion d'exception.
            
            Il est ainsi particulièrement adapté au développement d'applications
            indépendantes ou de scripts d'automatisation.
            """,
            
            'science': """
            L'intelligence artificielle est une technologie qui permet aux machines
            d'apprendre, de raisonner et d'agir de manière autonome. Elle utilise
            des algorithmes complexes pour analyser des données et prendre des décisions.
            L'IA est utilisée dans de nombreux domaines, de la médecine à la finance.
            """
        },
        
        'german': {
            'programming': """
            Python ist eine universelle, üblicherweise interpretierte, höhere Programmiersprache.
            Sie hat den Anspruch, einen gut lesbaren, knappen Programmierstil zu fördern.
            So werden beispielsweise Blöcke nicht durch geschweifte Klammern, sondern durch
            Einrückungen strukturiert.
            
            Python unterstützt mehrere Programmierparadigmen, z. B. die objektorientierte,
            die aspektorientierte und die funktionale Programmierung.
            """
        }
    }
    
    SHORT_TEXTS = {
        'simple': "Python is a programming language.",
        'question': "What is machine learning?",
        'statement': "Artificial intelligence helps solve complex problems.",
        'definition': "A database is a structured collection of data."
    }
    
    COMPLEX_TEXTS = {
        'technical_detailed': """
        Deep learning is a subset of machine learning in artificial intelligence that has networks
        capable of learning unsupervised from data that is unstructured or unlabeled. Also known
        as deep neural learning or deep neural network, it mimics the workings of the human brain
        in processing data and creating patterns for use in decision making.
        
        Deep learning uses multiple layers to progressively extract higher-level features from
        the raw input. For example, in image processing, lower layers may identify edges, while
        higher layers may identify the concepts relevant to a human such as digits or letters or faces.
        
        The term "deep" in deep learning refers to the number of layers through which the data
        is transformed. More precisely, deep learning systems have a substantial credit assignment
        path (CAP) depth. The CAP is the chain of transformations from input to output.
        
        Deep learning architectures such as deep neural networks, deep belief networks, recurrent
        neural networks and convolutional neural networks have been applied to fields including
        computer vision, speech recognition, natural language processing, machine translation,
        bioinformatics, drug design, medical image analysis, material inspection and board game programs.
        """,
        
        'academic_style': """
        The paradigm of artificial intelligence has undergone significant evolution since its
        inception in the mid-20th century. Contemporary approaches to AI development emphasize
        the integration of statistical learning methods with symbolic reasoning capabilities,
        creating hybrid systems that can address complex real-world problems.
        
        Recent advances in neural network architectures, particularly transformer models,
        have demonstrated remarkable capabilities in natural language understanding and generation.
        These developments have profound implications for various domains, including automated
        content creation, language translation, and human-computer interaction.
        
        The ethical considerations surrounding AI deployment have become increasingly prominent
        in academic discourse. Issues such as algorithmic bias, privacy preservation, and
        the societal impact of automation require careful consideration as AI systems become
        more prevalent in critical applications.
        """
    }
    
    @classmethod
    def get_text_by_category(cls, category: str, key: str = None) -> str:
        """
        Get text by category and optional key.
        
        Args:
            category: Text category (programming, science, multilingual, etc.)
            key: Specific text key within category
            
        Returns:
            Text content
        """
        category_map = {
            'programming': cls.PROGRAMMING_TEXTS,
            'science': cls.SCIENCE_TEXTS,
            'multilingual': cls.MULTILINGUAL_TEXTS,
            'short': cls.SHORT_TEXTS,
            'complex': cls.COMPLEX_TEXTS
        }
        
        texts = category_map.get(category, {})
        
        if key:
            return texts.get(key, "")
        else:
            # Return first available text
            return next(iter(texts.values())) if texts else ""
    
    @classmethod
    def get_all_categories(cls) -> List[str]:
        """Get list of all available text categories."""
        return ['programming', 'science', 'multilingual', 'short', 'complex']
    
    @classmethod
    def get_multilingual_sample(cls, language: str, topic: str = None) -> str:
        """
        Get multilingual text sample.
        
        Args:
            language: Language code (spanish, french, german)
            topic: Topic within language (programming, science)
            
        Returns:
            Text in specified language
        """
        lang_texts = cls.MULTILINGUAL_TEXTS.get(language, {})
        
        if topic and topic in lang_texts:
            return lang_texts[topic]
        elif lang_texts:
            return next(iter(lang_texts.values()))
        else:
            return ""
