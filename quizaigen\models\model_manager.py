"""
Model Manager for QuizAIGen

Centralized management of AI models with tier-based access control.
Handles model loading, unloading, and resource management.
"""

from typing import Dict, Any, Optional, List, Type
from enum import Enum
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

from .base_model import BaseModel, ModelTier, ModelConfig, LightweightModel
from .model_cache import ModelCache
from ..core.exceptions import ProcessingError, LicenseError
from ..utils.logger import LoggerMixin

try:
    from .t5_integration import T5QuestionGenerator
    from .bert_integration import BERTAnswerValidator
    from .ai_quality_enhancer import AIQualityEnhancer
    AI_MODELS_AVAILABLE = True
except ImportError:
    AI_MODELS_AVAILABLE = False


class ModelType(Enum):
    """Available model types."""
    LIGHTWEIGHT = "lightweight"
    T5_GENERATOR = "t5_generator"
    BERT_VALIDATOR = "bert_validator"
    QUALITY_ENHANCER = "quality_enhancer"


class ModelManager(LoggerMixin):
    """
    Centralized manager for all AI models in QuizAIGen.
    
    Features:
    - Tier-based model access control
    - Lazy loading of models
    - Resource management and cleanup
    - Model caching and sharing
    - Thread-safe operations
    """
    
    def __init__(self,
                 tier: ModelTier = ModelTier.FREE,
                 cache_dir: Optional[str] = None,
                 enable_model_cache: bool = True):
        """
        Initialize model manager.

        Args:
            tier: Current license tier
            cache_dir: Directory for model caching
            enable_model_cache: Whether to enable model output caching
        """
        super().__init__()  # Initialize LoggerMixin
        self.tier = tier
        self.cache_dir = cache_dir
        self.enable_model_cache = enable_model_cache
        
        # Model registry
        self._models: Dict[ModelType, BaseModel] = {}
        self._model_configs: Dict[ModelType, ModelConfig] = {}
        self._lock = threading.Lock()
        
        # Model cache for outputs
        self.model_cache = None
        if enable_model_cache:
            self.model_cache = ModelCache(
                cache_dir=cache_dir,
                max_memory_entries=1000,
                default_ttl=3600
            )
        
        # Define tier-based model availability
        self.tier_models = {
            ModelTier.FREE: [ModelType.LIGHTWEIGHT],
            ModelTier.PREMIUM: [
                ModelType.LIGHTWEIGHT,
                ModelType.T5_GENERATOR,
                ModelType.BERT_VALIDATOR
            ],
            ModelTier.ENTERPRISE: [
                ModelType.LIGHTWEIGHT,
                ModelType.T5_GENERATOR,
                ModelType.BERT_VALIDATOR,
                ModelType.QUALITY_ENHANCER
            ]
        }
        
        # Concurrent loading settings
        self._loading_futures: Dict[ModelType, Any] = {}
        self._loading_lock = threading.Lock()

        self.log_info(f"Initialized ModelManager for {tier.value} tier")
    
    def get_model(self, model_type: ModelType, 
                  config: Optional[ModelConfig] = None) -> BaseModel:
        """
        Get a model instance, loading it if necessary.
        
        Args:
            model_type: Type of model to get
            config: Optional model configuration
            
        Returns:
            Model instance
            
        Raises:
            LicenseError: If model is not available for current tier
            ProcessingError: If model loading fails
        """
        # Check tier access
        if model_type not in self.tier_models.get(self.tier, []):
            raise LicenseError(
                f"Model {model_type.value} not available for {self.tier.value} tier"
            )
        
        with self._lock:
            # Return existing model if available
            if model_type in self._models:
                return self._models[model_type]
            
            # Create new model
            model = self._create_model(model_type, config)
            self._models[model_type] = model
            
            self.log_info(f"Loaded model: {model_type.value}")
            return model
    
    def _create_model(self, model_type: ModelType, 
                     config: Optional[ModelConfig] = None) -> BaseModel:
        """
        Create a new model instance.
        
        Args:
            model_type: Type of model to create
            config: Optional model configuration
            
        Returns:
            Model instance
        """
        if model_type == ModelType.LIGHTWEIGHT:
            if config is None:
                config = ModelConfig(
                    model_name="lightweight",
                    tier=ModelTier.FREE
                )
            return LightweightModel(config)
        
        elif model_type == ModelType.T5_GENERATOR:
            if not AI_MODELS_AVAILABLE:
                raise ProcessingError(
                    stage="model_loading",
                    message="T5 models require transformers. Install with: pip install transformers torch"
                )
            return T5QuestionGenerator(config, self.tier)
        
        elif model_type == ModelType.BERT_VALIDATOR:
            if not AI_MODELS_AVAILABLE:
                raise ProcessingError(
                    stage="model_loading",
                    message="BERT models require transformers. Install with: pip install transformers torch"
                )
            return BERTAnswerValidator(config, self.tier)
        
        elif model_type == ModelType.QUALITY_ENHANCER:
            if not AI_MODELS_AVAILABLE:
                raise ProcessingError(
                    stage="model_loading",
                    message="Quality enhancer requires transformers. Install with: pip install transformers torch"
                )
            return AIQualityEnhancer(self.tier, self.enable_model_cache, self.cache_dir)
        
        else:
            raise ProcessingError(
                stage="model_creation",
                message=f"Unknown model type: {model_type.value}"
            )
    
    def unload_model(self, model_type: ModelType) -> None:
        """
        Unload a specific model to free resources.
        
        Args:
            model_type: Type of model to unload
        """
        with self._lock:
            if model_type in self._models:
                model = self._models[model_type]
                model.unload_model()
                del self._models[model_type]
                self.log_info(f"Unloaded model: {model_type.value}")
    
    def unload_all_models(self) -> None:
        """Unload all models to free resources."""
        with self._lock:
            for model_type in list(self._models.keys()):
                self.unload_model(model_type)
            self.log_info("Unloaded all models")
    
    def is_model_loaded(self, model_type: ModelType) -> bool:
        """
        Check if a model is currently loaded.
        
        Args:
            model_type: Type of model to check
            
        Returns:
            True if model is loaded
        """
        with self._lock:
            return (model_type in self._models and 
                   self._models[model_type].is_loaded)
    
    def get_available_models(self) -> List[ModelType]:
        """
        Get list of models available for current tier.
        
        Returns:
            List of available model types
        """
        return self.tier_models.get(self.tier, [])
    
    def get_loaded_models(self) -> List[ModelType]:
        """
        Get list of currently loaded models.
        
        Returns:
            List of loaded model types
        """
        with self._lock:
            return [
                model_type for model_type, model in self._models.items()
                if model.is_loaded
            ]
    
    def get_model_info(self, model_type: Optional[ModelType] = None) -> Dict[str, Any]:
        """
        Get information about models.
        
        Args:
            model_type: Specific model type, or None for all models
            
        Returns:
            Model information dictionary
        """
        info = {
            "tier": self.tier.value,
            "available_models": [mt.value for mt in self.get_available_models()],
            "loaded_models": [mt.value for mt in self.get_loaded_models()],
            "ai_models_available": AI_MODELS_AVAILABLE,
            "cache_enabled": self.enable_model_cache
        }
        
        if model_type is None:
            # Return info for all loaded models
            model_details = {}
            with self._lock:
                for mt, model in self._models.items():
                    model_details[mt.value] = model.get_model_info()
            info["model_details"] = model_details
        else:
            # Return info for specific model
            with self._lock:
                if model_type in self._models:
                    info["model_details"] = self._models[model_type].get_model_info()
        
        # Add cache stats if available
        if self.model_cache:
            info["cache_stats"] = self.model_cache.get_cache_stats()
        
        return info
    
    def clear_cache(self, tier: Optional[ModelTier] = None) -> None:
        """
        Clear model output cache.
        
        Args:
            tier: If specified, only clear cache for this tier
        """
        if self.model_cache:
            self.model_cache.clear_cache(tier)
            self.log_info(f"Cleared model cache for tier: {tier.value if tier else 'all'}")
    
    def optimize_memory(self) -> Dict[str, Any]:
        """
        Optimize memory usage by unloading unused models.
        
        Returns:
            Optimization statistics
        """
        stats = {
            "models_before": len(self._models),
            "models_unloaded": 0,
            "memory_freed": 0
        }
        
        with self._lock:
            # For now, just report current state
            # In a full implementation, this would analyze usage patterns
            # and unload least recently used models
            
            stats["models_after"] = len(self._models)
            stats["loaded_models"] = [mt.value for mt in self.get_loaded_models()]
        
        self.log_info(f"Memory optimization completed: {stats}")
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on all loaded models.
        
        Returns:
            Health check results
        """
        health = {
            "overall_status": "healthy",
            "timestamp": time.time(),
            "tier": self.tier.value,
            "models": {}
        }
        
        with self._lock:
            for model_type, model in self._models.items():
                try:
                    model_info = model.get_model_info()
                    health["models"][model_type.value] = {
                        "status": "healthy" if model.is_loaded else "unloaded",
                        "info": model_info
                    }
                except Exception as e:
                    health["models"][model_type.value] = {
                        "status": "error",
                        "error": str(e)
                    }
                    health["overall_status"] = "degraded"
        
        return health

    def preload_models(self, model_types: List[ModelType],
                      concurrent: bool = True) -> Dict[ModelType, bool]:
        """
        Preload multiple models, optionally in parallel.

        Args:
            model_types: List of model types to preload
            concurrent: Whether to load models concurrently

        Returns:
            Dictionary mapping model types to success status
        """
        start_time = time.time()
        results = {}

        # Filter models available for current tier
        available_models = [
            model_type for model_type in model_types
            if model_type in self.tier_models.get(self.tier, [])
        ]

        if not available_models:
            self.log_warning("No models available for preloading in current tier")
            return {model_type: False for model_type in model_types}

        self.log_info(f"Preloading {len(available_models)} models (concurrent: {concurrent})")

        if not concurrent or len(available_models) == 1:
            # Sequential loading
            for model_type in available_models:
                try:
                    self.get_model(model_type)
                    results[model_type] = True
                except Exception as e:
                    self.log_error(f"Failed to preload {model_type.value}: {str(e)}")
                    results[model_type] = False
        else:
            # Concurrent loading
            results = self._preload_concurrent(available_models)

        # Add results for unavailable models
        for model_type in model_types:
            if model_type not in results:
                results[model_type] = False

        loading_time = time.time() - start_time
        successful_loads = sum(1 for success in results.values() if success)

        self.log_info(f"Preloading completed: {successful_loads}/{len(model_types)} models in {loading_time:.2f}s")
        return results

    def _preload_concurrent(self, model_types: List[ModelType]) -> Dict[ModelType, bool]:
        """Preload models concurrently using ThreadPoolExecutor."""
        results = {}

        with ThreadPoolExecutor(max_workers=min(len(model_types), 4)) as executor:
            # Submit loading tasks
            future_to_model = {
                executor.submit(self._load_model_safe, model_type): model_type
                for model_type in model_types
            }

            # Collect results
            for future in as_completed(future_to_model):
                model_type = future_to_model[future]
                try:
                    success = future.result()
                    results[model_type] = success
                    if success:
                        self.log_info(f"Concurrent loading completed: {model_type.value}")
                    else:
                        self.log_warning(f"Concurrent loading failed: {model_type.value}")
                except Exception as e:
                    self.log_error(f"Concurrent loading error for {model_type.value}: {str(e)}")
                    results[model_type] = False

        return results

    def _load_model_safe(self, model_type: ModelType) -> bool:
        """Safely load a model with error handling."""
        try:
            self.get_model(model_type)
            return True
        except Exception as e:
            self.log_error(f"Safe model loading failed for {model_type.value}: {str(e)}")
            return False

    def __del__(self):
        """Cleanup when manager is destroyed."""
        try:
            self.unload_all_models()
        except:
            pass  # Ignore errors during cleanup
