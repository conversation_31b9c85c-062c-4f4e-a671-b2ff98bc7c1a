import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Snackbar
} from '@mui/material';
import {
  Download,
  Key,
  Info,
  Update,
  Security,
  Support
} from '@mui/icons-material';

const CustomerDashboard = () => {
  const [customer, setCustomer] = useState(null);
  const [licenses, setLicenses] = useState([]);
  const [downloads, setDownloads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [licenseDialogOpen, setLicenseDialogOpen] = useState(false);
  const [selectedLicense, setSelectedLicense] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  useEffect(() => {
    fetchCustomerData();
  }, []);

  const fetchCustomerData = async () => {
    try {
      setLoading(true);
      
      // Fetch customer info
      const customerResponse = await fetch('/api/customers/me', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (!customerResponse.ok) {
        throw new Error('Failed to fetch customer data');
      }
      
      const customerData = await customerResponse.json();
      setCustomer(customerData);
      
      // Fetch licenses
      const licensesResponse = await fetch('/api/licenses/me', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (!licensesResponse.ok) {
        throw new Error('Failed to fetch licenses');
      }
      
      const licensesData = await licensesResponse.json();
      setLicenses(licensesData);
      
      // Fetch available downloads
      const downloadsResponse = await fetch('/api/downloads/available', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (downloadsResponse.ok) {
        const downloadsData = await downloadsResponse.json();
        setDownloads(downloadsData);
      }
      
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (downloadItem) => {
    try {
      const response = await fetch(`/api/downloads/${downloadItem.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Download failed');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = downloadItem.filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      
      setSnackbarMessage('Download started successfully');
      setSnackbarOpen(true);
      
    } catch (err) {
      setError(err.message);
    }
  };

  const handleViewLicense = (license) => {
    setSelectedLicense(license);
    setLicenseDialogOpen(true);
  };

  const copyLicenseKey = (licenseKey) => {
    navigator.clipboard.writeText(licenseKey);
    setSnackbarMessage('License key copied to clipboard');
    setSnackbarOpen(true);
  };

  const getTierColor = (tier) => {
    switch (tier.toLowerCase()) {
      case 'free':
        return 'default';
      case 'premium':
        return 'primary';
      case 'enterprise':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'expired':
        return 'error';
      case 'suspended':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Container>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Typography>Loading...</Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          Welcome, {customer?.name || customer?.email}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Manage your QuizAIGen licenses and downloads
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Customer Info Card */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Account Information
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Email: {customer?.email}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Customer ID: {customer?.id}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Member since: {new Date(customer?.created_at).toLocaleDateString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Grid container spacing={2}>
                <Grid item>
                  <Button
                    variant="outlined"
                    startIcon={<Download />}
                    onClick={() => window.open('/docs/installation', '_blank')}
                  >
                    Installation Guide
                  </Button>
                </Grid>
                <Grid item>
                  <Button
                    variant="outlined"
                    startIcon={<Info />}
                    onClick={() => window.open('/docs/api', '_blank')}
                  >
                    API Documentation
                  </Button>
                </Grid>
                <Grid item>
                  <Button
                    variant="outlined"
                    startIcon={<Support />}
                    onClick={() => window.open('/support', '_blank')}
                  >
                    Get Support
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Licenses */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Your Licenses
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Tier</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Expires</TableCell>
                      <TableCell>Features</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {licenses.map((license) => (
                      <TableRow key={license.id}>
                        <TableCell>
                          <Chip
                            label={license.tier}
                            color={getTierColor(license.tier)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={license.status}
                            color={getStatusColor(license.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {license.expires_at 
                            ? new Date(license.expires_at).toLocaleDateString()
                            : 'Never'
                          }
                        </TableCell>
                        <TableCell>
                          {license.features?.slice(0, 2).join(', ')}
                          {license.features?.length > 2 && '...'}
                        </TableCell>
                        <TableCell>
                          <Button
                            size="small"
                            startIcon={<Key />}
                            onClick={() => handleViewLicense(license)}
                          >
                            View Key
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Downloads */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Available Downloads
              </Typography>
              <Grid container spacing={2}>
                {downloads.map((download) => (
                  <Grid item xs={12} sm={6} md={4} key={download.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          {download.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {download.description}
                        </Typography>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Chip
                            label={download.tier}
                            color={getTierColor(download.tier)}
                            size="small"
                          />
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<Download />}
                            onClick={() => handleDownload(download)}
                            disabled={!download.available}
                          >
                            Download
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* License Key Dialog */}
      <Dialog
        open={licenseDialogOpen}
        onClose={() => setLicenseDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          License Key - {selectedLicense?.tier} Tier
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Keep your license key secure. Do not share it publicly.
          </Alert>
          <TextField
            fullWidth
            label="License Key"
            value={selectedLicense?.license_key || ''}
            InputProps={{
              readOnly: true,
            }}
            sx={{ mb: 2 }}
          />
          <Typography variant="body2" color="text.secondary">
            Features included: {selectedLicense?.features?.join(', ')}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Expires: {selectedLicense?.expires_at 
              ? new Date(selectedLicense.expires_at).toLocaleDateString()
              : 'Never'
            }
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLicenseDialogOpen(false)}>
            Close
          </Button>
          <Button
            variant="contained"
            onClick={() => copyLicenseKey(selectedLicense?.license_key)}
          >
            Copy Key
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Container>
  );
};

export default CustomerDashboard;
