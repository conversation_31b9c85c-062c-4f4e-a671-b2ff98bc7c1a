2025-06-28 21:37:30 - quizaigen.test_basic - DEBUG - test_basic_logging:72 - Debug message
2025-06-28 21:37:30 - quizaigen.test_basic - INFO - test_basic_logging:73 - Info message
2025-06-28 21:37:30 - quizaigen.test_basic - WARNING - test_basic_logging:74 - Warning message
2025-06-28 21:37:30 - quizaigen.test_basic - ERROR - test_basic_logging:75 - Error message
2025-06-28 21:37:30 - quizaigen.TestClass - INFO - log_info:128 - Starting work for worker_0
2025-06-28 21:37:30 - quizaigen.TestClass - INFO - log_performance:169 - Performance: work_operation completed in 0.0569s
2025-06-28 21:37:30 - quizaigen.TestClass - INFO - log_info:128 - Work completed for worker_0
2025-06-28 21:37:30 - quizaigen.TestClass - ERROR - log_error:144 - An error occurred during simulation
Traceback (most recent call last):
  File "E:\Drive\Business Plans\QuizAIGen\test_enhanced_logging.py", line 47, in simulate_error
    raise ValueError(f"Simulated error from {self.name}")
ValueError: Simulated error from worker_0
2025-06-28 21:37:30 - quizaigen.TestClass - INFO - log_info:128 - Starting work for worker_1
2025-06-28 21:37:30 - quizaigen.TestClass - INFO - log_performance:169 - Performance: work_operation completed in 0.0922s
2025-06-28 21:37:30 - quizaigen.TestClass - INFO - log_info:128 - Work completed for worker_1
2025-06-28 21:37:30 - quizaigen.TestClass - ERROR - log_error:144 - An error occurred during simulation
Traceback (most recent call last):
  File "E:\Drive\Business Plans\QuizAIGen\test_enhanced_logging.py", line 47, in simulate_error
    raise ValueError(f"Simulated error from {self.name}")
ValueError: Simulated error from worker_1
2025-06-28 21:37:30 - quizaigen.TestClass - INFO - log_info:128 - Starting work for worker_2
2025-06-28 21:37:30 - quizaigen.TestClass - INFO - log_performance:169 - Performance: work_operation completed in 0.1375s
2025-06-28 21:37:30 - quizaigen.TestClass - INFO - log_info:128 - Work completed for worker_2
2025-06-28 21:37:30 - quizaigen.operations - INFO - __enter__:597 - Starting operation: test_operation_success
2025-06-28 21:37:30 - quizaigen.operations - INFO - __exit__:628 - Operation completed: test_operation_success (0.1011s)
2025-06-28 21:37:30 - quizaigen.operations - INFO - __enter__:597 - Starting operation: test_operation_failure
2025-06-28 21:37:31 - quizaigen.operations - ERROR - __exit__:622 - Operation failed: test_operation_failure (0.0515s)
Traceback (most recent call last):
  File "E:\Drive\Business Plans\QuizAIGen\test_enhanced_logging.py", line 113, in test_operation_logging
    raise RuntimeError("Simulated operation failure")
RuntimeError: Simulated operation failure
2025-06-28 21:37:31 - quizaigen.operation.session_123.batch_processing - INFO - test_operation_logging:119 - Operation-specific log message
{"timestamp": "2025-06-28T16:07:31.028624+00:00", "level": "INFO", "logger_name": "quizaigen.test_json", "module": "test_enhanced_logging", "function": "test_json_logging", "line_number": 138, "message": "JSON structured log", "extra_data": {"user_id": "user_123", "operation": "test_json_logging", "metrics": {"duration": 0.123, "success": true}}}
2025-06-28 21:37:31 - quizaigen.PerformanceTest - INFO - log_performance:169 - Performance: cpu_intensive_task completed in 0.0000s
2025-06-28 21:37:31 - quizaigen.PerformanceTest - INFO - log_performance:169 - Performance: io_simulation completed in 0.0512s
