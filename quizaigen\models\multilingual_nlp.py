"""
Multilingual NLP Models Integration for QuizAIGen

Provides language-specific NLP models and processing capabilities
for text analysis and question generation in multiple languages.
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging
from pathlib import Path

from ..utils.logger import LoggerMixin
from ..core.exceptions import ProcessingError, ModelLoadError
from ..core.language_detector import LanguageDetectionResult


@dataclass
class MultilingualModelConfig:
    """Configuration for multilingual models."""
    language: str
    spacy_model: str
    transformers_model: str
    available: bool = False
    loaded: bool = False
    
    def to_dict(self) -> Dict:
        """Convert to dictionary."""
        return {
            'language': self.language,
            'spacy_model': self.spacy_model,
            'transformers_model': self.transformers_model,
            'available': self.available,
            'loaded': self.loaded
        }


class MultilingualNLPManager(LoggerMixin):
    """
    Manages multilingual NLP models and provides language-specific processing.
    
    Handles spaCy models for different languages and integrates with
    transformers models for advanced multilingual capabilities.
    """
    
    def __init__(self):
        """Initialize multilingual NLP manager."""
        super().__init__()
        self.models = {}
        self.spacy_models = {}
        self.transformers_models = {}
        
        # Define supported language models
        self.model_configs = self._get_model_configurations()
        
        # Initialize available models
        self._check_model_availability()
        
        self.log_info("Multilingual NLP manager initialized")
    
    def _get_model_configurations(self) -> Dict[str, MultilingualModelConfig]:
        """Get model configurations for supported languages."""
        return {
            'en': MultilingualModelConfig(
                language='en',
                spacy_model='en_core_web_sm',
                transformers_model='bert-base-uncased'
            ),
            'es': MultilingualModelConfig(
                language='es',
                spacy_model='es_core_news_sm',
                transformers_model='dccuchile/bert-base-spanish-wwm-uncased'
            ),
            'fr': MultilingualModelConfig(
                language='fr',
                spacy_model='fr_core_news_sm',
                transformers_model='camembert-base'
            ),
            'de': MultilingualModelConfig(
                language='de',
                spacy_model='de_core_news_sm',
                transformers_model='bert-base-german-cased'
            ),
            'it': MultilingualModelConfig(
                language='it',
                spacy_model='it_core_news_sm',
                transformers_model='dbmdz/bert-base-italian-cased'
            ),
            'pt': MultilingualModelConfig(
                language='pt',
                spacy_model='pt_core_news_sm',
                transformers_model='neuralmind/bert-base-portuguese-cased'
            ),
            'nl': MultilingualModelConfig(
                language='nl',
                spacy_model='nl_core_news_sm',
                transformers_model='GroNLP/bert-base-dutch-cased'
            ),
            'ru': MultilingualModelConfig(
                language='ru',
                spacy_model='ru_core_news_sm',
                transformers_model='DeepPavlov/rubert-base-cased'
            )
        }
    
    def _check_model_availability(self):
        """Check which models are available for loading."""
        for lang, config in self.model_configs.items():
            # Check spaCy model availability
            try:
                import spacy
                spacy.load(config.spacy_model)
                config.available = True
                self.log_info(f"spaCy model available for {lang}: {config.spacy_model}")
            except (OSError, IOError):
                config.available = False
                self.log_warning(f"spaCy model not available for {lang}: {config.spacy_model}")
            except ImportError:
                self.log_warning("spaCy not available")
                config.available = False
    
    def load_language_model(self, language: str) -> bool:
        """
        Load NLP model for specific language.
        
        Args:
            language: Language code (e.g., 'en', 'es', 'fr')
            
        Returns:
            True if model loaded successfully, False otherwise
        """
        if language not in self.model_configs:
            self.log_warning(f"Unsupported language: {language}")
            return False
        
        config = self.model_configs[language]
        
        if not config.available:
            self.log_warning(f"Model not available for language: {language}")
            return False
        
        if config.loaded:
            self.log_debug(f"Model already loaded for language: {language}")
            return True
        
        try:
            # Load spaCy model
            import spacy
            nlp = spacy.load(config.spacy_model)
            self.spacy_models[language] = nlp
            
            # Load transformers model (lazy loading)
            self.transformers_models[language] = config.transformers_model
            
            config.loaded = True
            self.log_info(f"Successfully loaded models for language: {language}")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to load models for {language}: {str(e)}")
            return False
    
    def get_spacy_model(self, language: str):
        """Get spaCy model for specific language."""
        if language not in self.spacy_models:
            if not self.load_language_model(language):
                # Fallback to English
                if language != 'en' and 'en' not in self.spacy_models:
                    self.load_language_model('en')
                return self.spacy_models.get('en')
        
        return self.spacy_models.get(language)
    
    def process_text(self, text: str, language: str) -> Optional[Any]:
        """
        Process text using language-specific NLP model.
        
        Args:
            text: Text to process
            language: Language code
            
        Returns:
            Processed spaCy document or None if processing fails
        """
        nlp = self.get_spacy_model(language)
        if not nlp:
            self.log_warning(f"No NLP model available for language: {language}")
            return None
        
        try:
            doc = nlp(text)
            return doc
        except Exception as e:
            self.log_error(f"Text processing failed for {language}: {str(e)}")
            return None
    
    def extract_entities(self, text: str, language: str) -> List[Tuple[str, str, int, int]]:
        """
        Extract named entities from text using language-specific model.
        
        Args:
            text: Text to analyze
            language: Language code
            
        Returns:
            List of tuples (entity_text, entity_label, start_pos, end_pos)
        """
        doc = self.process_text(text, language)
        if not doc:
            return []
        
        entities = []
        for ent in doc.ents:
            entities.append((ent.text, ent.label_, ent.start_char, ent.end_char))
        
        return entities
    
    def get_pos_tags(self, text: str, language: str) -> List[Tuple[str, str]]:
        """
        Get part-of-speech tags for text.
        
        Args:
            text: Text to analyze
            language: Language code
            
        Returns:
            List of tuples (token_text, pos_tag)
        """
        doc = self.process_text(text, language)
        if not doc:
            return []
        
        pos_tags = []
        for token in doc:
            if not token.is_space:
                pos_tags.append((token.text, token.pos_))
        
        return pos_tags
    
    def get_sentences(self, text: str, language: str) -> List[str]:
        """
        Split text into sentences using language-specific model.
        
        Args:
            text: Text to split
            language: Language code
            
        Returns:
            List of sentences
        """
        doc = self.process_text(text, language)
        if not doc:
            # Fallback to simple sentence splitting
            import re
            sentences = re.split(r'[.!?]+', text)
            return [s.strip() for s in sentences if s.strip()]
        
        return [sent.text.strip() for sent in doc.sents if sent.text.strip()]
    
    def get_noun_phrases(self, text: str, language: str) -> List[str]:
        """
        Extract noun phrases from text.
        
        Args:
            text: Text to analyze
            language: Language code
            
        Returns:
            List of noun phrases
        """
        doc = self.process_text(text, language)
        if not doc:
            return []
        
        noun_phrases = []
        for chunk in doc.noun_chunks:
            noun_phrases.append(chunk.text.strip())
        
        return noun_phrases
    
    def is_model_available(self, language: str) -> bool:
        """Check if model is available for specific language."""
        return (language in self.model_configs and 
                self.model_configs[language].available)
    
    def is_model_loaded(self, language: str) -> bool:
        """Check if model is loaded for specific language."""
        return (language in self.model_configs and 
                self.model_configs[language].loaded)
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return list(self.model_configs.keys())
    
    def get_available_languages(self) -> List[str]:
        """Get list of languages with available models."""
        return [lang for lang, config in self.model_configs.items() 
                if config.available]
    
    def get_loaded_languages(self) -> List[str]:
        """Get list of languages with loaded models."""
        return [lang for lang, config in self.model_configs.items() 
                if config.loaded]
    
    def get_model_info(self, language: str) -> Optional[Dict]:
        """Get model information for specific language."""
        if language not in self.model_configs:
            return None
        
        return self.model_configs[language].to_dict()
    
    def get_all_model_info(self) -> Dict[str, Dict]:
        """Get information about all models."""
        return {lang: config.to_dict() 
                for lang, config in self.model_configs.items()}
    
    def unload_language_model(self, language: str) -> bool:
        """
        Unload model for specific language to free memory.
        
        Args:
            language: Language code
            
        Returns:
            True if unloaded successfully, False otherwise
        """
        try:
            if language in self.spacy_models:
                del self.spacy_models[language]
            
            if language in self.transformers_models:
                del self.transformers_models[language]
            
            if language in self.model_configs:
                self.model_configs[language].loaded = False
            
            self.log_info(f"Unloaded models for language: {language}")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to unload models for {language}: {str(e)}")
            return False
    
    def clear_all_models(self):
        """Clear all loaded models to free memory."""
        for language in list(self.spacy_models.keys()):
            self.unload_language_model(language)
        
        self.log_info("All models cleared")
