"""
Test suite for Multilingual Question Generator

Tests the integrated multilingual question generation functionality
including language detection, template selection, and question generation.
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock

from quizaigen.generators.multilingual_generator import (
    MultilingualQuestionGenerator, 
    MultilingualGenerationConfig
)
from quizaigen.generators.base import Question
from quizaigen.core.language_detector import LanguageDetectionResult


class TestMultilingualGenerationConfig(unittest.TestCase):
    """Test multilingual generation configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = MultilingualGenerationConfig()
        
        self.assertTrue(config.auto_detect_language)
        self.assertIsNone(config.target_language)
        self.assertEqual(config.fallback_language, 'en')
        self.assertEqual(config.min_confidence, 0.7)
        self.assertTrue(config.preserve_entities)
        self.assertTrue(config.cultural_adaptation)
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = MultilingualGenerationConfig(
            auto_detect_language=False,
            target_language='es',
            fallback_language='fr',
            min_confidence=0.8,
            preserve_entities=False,
            cultural_adaptation=False
        )
        
        self.assertFalse(config.auto_detect_language)
        self.assertEqual(config.target_language, 'es')
        self.assertEqual(config.fallback_language, 'fr')
        self.assertEqual(config.min_confidence, 0.8)
        self.assertFalse(config.preserve_entities)
        self.assertFalse(config.cultural_adaptation)
    
    def test_config_to_dict(self):
        """Test configuration serialization."""
        config = MultilingualGenerationConfig(target_language='de')
        config_dict = config.to_dict()
        
        self.assertIsInstance(config_dict, dict)
        self.assertIn('auto_detect_language', config_dict)
        self.assertIn('target_language', config_dict)
        self.assertEqual(config_dict['target_language'], 'de')


class TestMultilingualQuestionGenerator(unittest.TestCase):
    """Test multilingual question generator functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = MultilingualGenerationConfig()
        self.generator = MultilingualQuestionGenerator(self.config)
    
    def test_generator_initialization(self):
        """Test generator initialization."""
        self.assertIsNotNone(self.generator.language_detector)
        self.assertIsNotNone(self.generator.templates)
        self.assertIsNotNone(self.generator.nlp_manager)
        self.assertEqual(self.generator.config, self.config)
    
    def test_generator_initialization_without_config(self):
        """Test generator initialization with default config."""
        generator = MultilingualQuestionGenerator()
        self.assertIsNotNone(generator.config)
        self.assertTrue(generator.config.auto_detect_language)
    
    def test_supported_languages(self):
        """Test supported language methods."""
        supported = self.generator.get_supported_languages()
        self.assertIsInstance(supported, list)
        self.assertIn('en', supported)
        
        self.assertTrue(self.generator.is_language_supported('en'))
        self.assertFalse(self.generator.is_language_supported('xyz'))
    
    def test_language_detection(self):
        """Test text language detection."""
        text = "Python is a programming language."
        result = self.generator.detect_text_language(text)
        
        self.assertIsInstance(result, LanguageDetectionResult)
        self.assertEqual(result.language, 'en')
    
    def test_empty_text_validation(self):
        """Test validation of empty text input."""
        with self.assertRaises(Exception):  # Should raise ValidationError
            self.generator.generate_questions("")
        
        with self.assertRaises(Exception):  # Should raise ValidationError
            self.generator.generate_questions("   ")
    
    @patch('quizaigen.generators.multilingual_generator.MultilingualQuestionGenerator._process_text_for_language')
    @patch('quizaigen.generators.multilingual_generator.MultilingualQuestionGenerator._generate_mcq_questions')
    def test_generate_mcq_questions_english(self, mock_generate_mcq, mock_process_text):
        """Test MCQ question generation in English."""
        # Mock processed text
        mock_process_text.return_value = {
            'original_text': 'Python is a programming language.',
            'sentences': ['Python is a programming language.'],
            'entities': [('Python', 'PRODUCT', 0, 6)],
            'noun_phrases': ['Python', 'programming language'],
            'pos_tags': [('Python', 'PROPN'), ('is', 'VERB')],
            'language': 'en'
        }
        
        # Mock generated questions
        mock_questions = [
            Question(
                question="What is Python?",
                type="mcq",
                answer="A programming language",
                options=["A programming language", "A snake", "A tool", "A framework"],
                explanation="Python is a programming language",
                difficulty="medium",
                keywords=["Python"],
                source_text="Python is a programming language.",
                confidence=0.8
            )
        ]
        mock_generate_mcq.return_value = mock_questions
        
        # Test generation
        text = "Python is a programming language."
        questions = self.generator.generate_questions(text, "mcq", 1)
        
        self.assertEqual(len(questions), 1)
        self.assertEqual(questions[0].type, "mcq")
        self.assertEqual(questions[0].question, "What is Python?")
        self.assertIn('language', questions[0].metadata)
        self.assertEqual(questions[0].metadata['language'], 'en')
    
    @patch('quizaigen.generators.multilingual_generator.MultilingualQuestionGenerator._process_text_for_language')
    @patch('quizaigen.generators.multilingual_generator.MultilingualQuestionGenerator._generate_boolean_questions')
    def test_generate_boolean_questions_spanish(self, mock_generate_boolean, mock_process_text):
        """Test Boolean question generation in Spanish."""
        # Mock processed text
        mock_process_text.return_value = {
            'original_text': 'Python es un lenguaje de programación.',
            'sentences': ['Python es un lenguaje de programación.'],
            'entities': [('Python', 'PRODUCT', 0, 6)],
            'noun_phrases': ['Python', 'lenguaje de programación'],
            'pos_tags': [('Python', 'PROPN'), ('es', 'VERB')],
            'language': 'es'
        }
        
        # Mock generated questions
        mock_questions = [
            Question(
                question="¿Es Python un lenguaje de programación verdadero o falso?",
                type="boolean",
                answer="True",
                options=["True", "False"],
                explanation="Según el texto: Python es un lenguaje de programación.",
                difficulty="easy",
                keywords=["Python", "lenguaje"],
                source_text="Python es un lenguaje de programación.",
                confidence=0.7
            )
        ]
        mock_generate_boolean.return_value = mock_questions
        
        # Test generation with target language
        text = "Python es un lenguaje de programación."
        questions = self.generator.generate_questions(
            text, "boolean", 1, target_language='es'
        )
        
        self.assertEqual(len(questions), 1)
        self.assertEqual(questions[0].type, "boolean")
        self.assertIn('¿', questions[0].question)  # Spanish question mark
        self.assertEqual(questions[0].metadata['language'], 'es')
    
    @patch('quizaigen.generators.multilingual_generator.MultilingualQuestionGenerator._process_text_for_language')
    @patch('quizaigen.generators.multilingual_generator.MultilingualQuestionGenerator._generate_faq_questions')
    def test_generate_faq_questions_french(self, mock_generate_faq, mock_process_text):
        """Test FAQ question generation in French."""
        # Mock processed text
        mock_process_text.return_value = {
            'original_text': 'Python est un langage de programmation.',
            'sentences': ['Python est un langage de programmation.'],
            'entities': [('Python', 'PRODUCT', 0, 6)],
            'noun_phrases': ['Python', 'langage de programmation'],
            'pos_tags': [('Python', 'PROPN'), ('est', 'VERB')],
            'language': 'fr'
        }
        
        # Mock generated questions
        mock_questions = [
            Question(
                question="Qu'est-ce que Python?",
                type="faq",
                answer="Python est un langage de programmation.",
                options=[],
                explanation="Information about Python",
                difficulty="medium",
                keywords=["Python"],
                source_text="Python est un langage de programmation.",
                confidence=0.8
            )
        ]
        mock_generate_faq.return_value = mock_questions
        
        # Test generation
        text = "Python est un langage de programmation."
        questions = self.generator.generate_questions(
            text, "faq", 1, target_language='fr'
        )
        
        self.assertEqual(len(questions), 1)
        self.assertEqual(questions[0].type, "faq")
        self.assertIn("Qu'est-ce que", questions[0].question)  # French question pattern
        self.assertEqual(questions[0].metadata['language'], 'fr')
    
    @patch('quizaigen.generators.multilingual_generator.MultilingualQuestionGenerator._process_text_for_language')
    @patch('quizaigen.generators.multilingual_generator.MultilingualQuestionGenerator._generate_fill_blank_questions')
    def test_generate_fill_blank_questions_german(self, mock_generate_fill_blank, mock_process_text):
        """Test fill-in-the-blank question generation in German."""
        # Mock processed text
        mock_process_text.return_value = {
            'original_text': 'Python ist eine Programmiersprache.',
            'sentences': ['Python ist eine Programmiersprache.'],
            'entities': [('Python', 'PRODUCT', 0, 6)],
            'noun_phrases': ['Python', 'Programmiersprache'],
            'pos_tags': [('Python', 'PROPN'), ('ist', 'VERB')],
            'language': 'de'
        }
        
        # Mock generated questions
        mock_questions = [
            Question(
                question="Fill in the blank: Python ist eine _____.",
                type="fill_blank",
                answer="Programmiersprache",
                options=[],
                explanation="The missing word is 'Programmiersprache'",
                difficulty="medium",
                keywords=["Programmiersprache"],
                source_text="Python ist eine Programmiersprache.",
                confidence=0.7
            )
        ]
        mock_generate_fill_blank.return_value = mock_questions
        
        # Test generation
        text = "Python ist eine Programmiersprache."
        questions = self.generator.generate_questions(
            text, "fill_blank", 1, target_language='de'
        )
        
        self.assertEqual(len(questions), 1)
        self.assertEqual(questions[0].type, "fill_blank")
        self.assertIn("_____", questions[0].question)
        self.assertEqual(questions[0].metadata['language'], 'de')
    
    def test_unsupported_question_type(self):
        """Test handling of unsupported question type."""
        text = "Python is a programming language."
        
        with self.assertRaises(Exception):  # Should raise ValidationError
            self.generator.generate_questions(text, "unsupported_type", 1)
    
    @patch('quizaigen.generators.multilingual_generator.MultilingualQuestionGenerator._process_text_for_language')
    def test_text_processing_fallback(self, mock_process_text):
        """Test text processing with fallback when NLP fails."""
        # Mock processing failure and fallback
        mock_process_text.return_value = {
            'original_text': 'Test text.',
            'sentences': ['Test text.'],
            'entities': [],
            'noun_phrases': [],
            'pos_tags': [],
            'language': 'en'
        }
        
        text = "Test text."
        result = self.generator._process_text_for_language(text, 'en')
        
        self.assertIsInstance(result, dict)
        self.assertIn('original_text', result)
        self.assertIn('sentences', result)
        self.assertIn('language', result)
    
    def test_concept_extraction(self):
        """Test concept extraction from sentences."""
        sentence = "Python is a high-level programming language."
        entities = [('Python', 'PRODUCT', 0, 6), ('programming language', 'CONCEPT', 20, 40)]
        
        concepts = self.generator._extract_concepts_from_sentence(sentence, entities)
        
        self.assertIsInstance(concepts, list)
        self.assertIn('Python', concepts)
    
    def test_mcq_options_generation(self):
        """Test MCQ options generation."""
        correct_answer = "Python"
        
        # Test English options
        options_en = self.generator._generate_mcq_options(correct_answer, 'en')
        self.assertIn(correct_answer, options_en)
        self.assertEqual(len(options_en), 4)  # 1 correct + 3 distractors
        
        # Test Spanish options
        options_es = self.generator._generate_mcq_options(correct_answer, 'es')
        self.assertIn(correct_answer, options_es)
        self.assertEqual(len(options_es), 4)
    
    def test_statement_creation(self):
        """Test statement creation from sentences."""
        sentence = "What is Python?"
        statement = self.generator._create_statement_from_sentence(sentence)
        
        self.assertNotIn('?', statement)
        self.assertIsInstance(statement, str)
    
    def test_keyword_extraction(self):
        """Test keyword extraction from sentences."""
        sentence = "Python is a high-level programming language created by Guido."
        keywords = self.generator._extract_keywords_from_sentence(sentence)
        
        self.assertIsInstance(keywords, list)
        self.assertLessEqual(len(keywords), 5)  # Should return max 5 keywords
        
        # Should contain meaningful words
        meaningful_words = [kw for kw in keywords if len(kw) > 3]
        self.assertGreater(len(meaningful_words), 0)
    
    def test_important_words_finding(self):
        """Test finding important words for fill-in-the-blank."""
        sentence = "Python is a programming language."
        pos_tags = [('Python', 'PROPN'), ('is', 'VERB'), ('programming', 'NOUN'), ('language', 'NOUN')]
        
        important_words = self.generator._find_important_words(sentence, pos_tags)
        
        self.assertIsInstance(important_words, list)
        # Should include nouns and proper nouns
        self.assertTrue(any(word in ['Python', 'programming', 'language'] for word in important_words))


if __name__ == '__main__':
    unittest.main()
