# QuizAIGen Integration Guide

## Overview

QuizAIGen is a comprehensive AI-powered question generation library with a tiered feature system designed for SaaS integration. This guide provides complete integration instructions for frontend and backend development teams.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Architecture Overview](#architecture-overview)
3. [Feature Tiers](#feature-tiers)
4. [Installation & Setup](#installation--setup)
5. [Backend Integration](#backend-integration)
6. [Frontend Integration](#frontend-integration)
7. [API Reference](#api-reference)
8. [Export Formats](#export-formats)
9. [<PERSON>rror Handling](#error-handling)
10. [Performance Considerations](#performance-considerations)
11. [Security & Licensing](#security--licensing)

---

## Quick Start

### Basic Usage (Free Tier)

```python
from quizaigen import QuestionGenerator

# Initialize generator
generator = QuestionGenerator()

# Generate questions from text
text = "Python is a high-level programming language created by <PERSON>."
questions = generator.generate_mcq(text, num_questions=2)

# Export to JSON
from quizaigen import ExportManager
exporter = ExportManager()
exporter.export_questions(questions, "output.json", format="json")
```

### Advanced Usage (Premium Tier)

```python
# Premium features require license validation
from quizaigen import QuestionGenerator, ExportManager

generator = QuestionGenerator()

# Generate fill-in-blank questions (Premium)
fill_blank_questions = generator.generate_fill_blank(text, num_questions=2)

# Process PDF documents (Premium)
pdf_questions = generator.generate_from_pdf("document.pdf", num_questions=5)

# Export to advanced formats (Premium)
exporter = ExportManager()
exporter.export_questions(questions, "quiz.qti", format="qti")
exporter.export_questions(questions, "quiz.xml", format="moodle")
```

---

## Architecture Overview

### Core Components

```
QuizAIGen Library
├── Core Engine
│   ├── Question Generators (MCQ, Boolean, FAQ, Fill-blank, etc.)
│   ├── Input Processors (Text, PDF, Word, URL)
│   └── Export Managers (8 formats)
├── Configuration System
│   ├── License Validation
│   ├── Feature Detection
│   └── Settings Management
└── Utilities
    ├── NLP Processing
    ├── Error Handling
    └── Logging System
```

### Data Flow

```
Input Source → Input Processor → Question Generator → Export Manager → Output Format
     ↓              ↓                    ↓                 ↓             ↓
  Text/PDF/URL → Text Extraction → AI Processing → Format Conversion → JSON/QTI/etc
```

---

## Feature Tiers

### Free Tier ✅ (Open Source - MIT License)
- **Question Types**: MCQ, Boolean, FAQ, Paraphrasing, QA
- **Input**: Plain text only
- **Export Formats**: JSON, CSV
- **Processing**: Basic NLP with NLTK
- **Batch Size**: Up to 10 questions per request
- **Rate Limiting**: None

### Premium Tier 💎 (Commercial License Required)
- **All Free Tier Features** +
- **Question Types**: Fill-in-the-blank with intelligent blank selection
- **Input**: PDF documents, Word documents, URL content
- **Export Formats**: QTI, Moodle XML, AIKEN, RESPONDUS, GIFT
- **Processing**: Advanced NLP with enhanced algorithms
- **Batch Size**: Up to 100 questions per request
- **Quality Scoring**: Advanced confidence scoring

### Enterprise Tier 🏢 (Enterprise License Required)
- **All Premium Tier Features** +
- **Multi-language Support**: 15+ languages
- **Advanced AI Models**: Custom T5/BERT integration
- **Custom Training**: Domain-specific model fine-tuning
- **Analytics**: Advanced reporting and insights
- **Support**: Priority support and custom integrations
- **Deployment**: On-premises options available

---

## Installation & Setup

### Backend Setup

```bash
# Install the library
pip install quizaigen

# For development
pip install -e .

# Install with premium dependencies
pip install quizaigen[premium]

# Install with all dependencies
pip install quizaigen[all]
```

### Environment Configuration

```python
# config.py
import os
from quizaigen.core.config import Config

# Basic configuration
config = Config(
    license_key=os.getenv('QUIZAIGEN_LICENSE_KEY'),
    tier=os.getenv('QUIZAIGEN_TIER', 'free'),  # free, premium, enterprise
    max_questions_per_request=10,
    enable_logging=True,
    log_level='INFO'
)
```

### License Validation

```python
from quizaigen.core.license import LicenseValidator

# Validate license on startup
validator = LicenseValidator()
license_info = validator.validate_license(license_key)

if license_info.is_valid:
    print(f"License valid for tier: {license_info.tier}")
    print(f"Features available: {license_info.available_features}")
else:
    print(f"License validation failed: {license_info.error}")
```

---

## Backend Integration

### Flask Integration Example

```python
from flask import Flask, request, jsonify
from quizaigen import QuestionGenerator, ExportManager
from quizaigen.core.exceptions import ValidationError, ProcessingError

app = Flask(__name__)
generator = QuestionGenerator()
exporter = ExportManager()

@app.route('/api/generate', methods=['POST'])
def generate_questions():
    try:
        data = request.get_json()
        
        # Validate input
        text = data.get('text', '')
        question_type = data.get('type', 'mcq')
        num_questions = min(data.get('num_questions', 5), 10)  # Limit for free tier
        
        # Generate questions based on type
        if question_type == 'mcq':
            questions = generator.generate_mcq(text, num_questions=num_questions)
        elif question_type == 'boolean':
            questions = generator.generate_boolean(text, num_questions=num_questions)
        elif question_type == 'fill_blank':
            # Premium feature - check license
            questions = generator.generate_fill_blank(text, num_questions=num_questions)
        else:
            return jsonify({'error': 'Unsupported question type'}), 400
        
        return jsonify({
            'success': True,
            'questions': [q.to_dict() for q in questions],
            'count': len(questions)
        })
        
    except ValidationError as e:
        return jsonify({'error': f'Validation error: {str(e)}'}), 400
    except ProcessingError as e:
        return jsonify({'error': f'Processing error: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'Internal error: {str(e)}'}), 500

@app.route('/api/export', methods=['POST'])
def export_questions():
    try:
        data = request.get_json()
        questions = data.get('questions', [])
        format_type = data.get('format', 'json')
        
        # Export questions
        output_path = f"temp_export.{format_type}"
        exporter.export_questions(questions, output_path, format=format_type)
        
        # Read and return file content
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return jsonify({
            'success': True,
            'content': content,
            'format': format_type
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
```

### FastAPI Integration Example

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from quizaigen import QuestionGenerator, ExportManager

app = FastAPI(title="QuizAIGen API", version="1.0.0")
generator = QuestionGenerator()
exporter = ExportManager()

class QuestionRequest(BaseModel):
    text: str
    type: str = "mcq"
    num_questions: int = 5
    difficulty: Optional[str] = None

class ExportRequest(BaseModel):
    questions: List[dict]
    format: str = "json"

@app.post("/generate")
async def generate_questions(request: QuestionRequest):
    try:
        if request.type == "mcq":
            questions = generator.generate_mcq(
                request.text, 
                num_questions=request.num_questions
            )
        elif request.type == "fill_blank":
            questions = generator.generate_fill_blank(
                request.text, 
                num_questions=request.num_questions
            )
        else:
            raise HTTPException(status_code=400, detail="Unsupported question type")
        
        return {
            "success": True,
            "questions": [q.to_dict() for q in questions],
            "count": len(questions)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/export")
async def export_questions(request: ExportRequest):
    try:
        output_path = f"temp_export.{request.format}"
        exporter.export_questions(request.questions, output_path, format=request.format)
        
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return {
            "success": True,
            "content": content,
            "format": request.format
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

---

## Frontend Integration

### React Integration Example

```javascript
// QuizAIGenService.js
class QuizAIGenService {
    constructor(baseURL = '/api') {
        this.baseURL = baseURL;
    }

    async generateQuestions(text, type = 'mcq', numQuestions = 5) {
        try {
            const response = await fetch(`${this.baseURL}/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text,
                    type,
                    num_questions: numQuestions
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error generating questions:', error);
            throw error;
        }
    }

    async exportQuestions(questions, format = 'json') {
        try {
            const response = await fetch(`${this.baseURL}/export`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    questions,
                    format
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error exporting questions:', error);
            throw error;
        }
    }
}

export default QuizAIGenService;
```

### React Component Example

```jsx
// QuestionGenerator.jsx
import React, { useState } from 'react';
import QuizAIGenService from './QuizAIGenService';

const QuestionGenerator = () => {
    const [text, setText] = useState('');
    const [questions, setQuestions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [questionType, setQuestionType] = useState('mcq');
    const [numQuestions, setNumQuestions] = useState(5);
    
    const service = new QuizAIGenService();

    const handleGenerate = async () => {
        if (!text.trim()) {
            alert('Please enter some text');
            return;
        }

        setLoading(true);
        try {
            const result = await service.generateQuestions(text, questionType, numQuestions);
            if (result.success) {
                setQuestions(result.questions);
            } else {
                alert('Error generating questions: ' + result.error);
            }
        } catch (error) {
            alert('Error: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const handleExport = async (format) => {
        if (questions.length === 0) {
            alert('No questions to export');
            return;
        }

        try {
            const result = await service.exportQuestions(questions, format);
            if (result.success) {
                // Create download link
                const blob = new Blob([result.content], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `questions.${format}`;
                a.click();
                URL.revokeObjectURL(url);
            }
        } catch (error) {
            alert('Export error: ' + error.message);
        }
    };

    return (
        <div className="question-generator">
            <h2>AI Question Generator</h2>
            
            <div className="input-section">
                <textarea
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    placeholder="Enter your text here..."
                    rows={6}
                    cols={80}
                />
                
                <div className="controls">
                    <select 
                        value={questionType} 
                        onChange={(e) => setQuestionType(e.target.value)}
                    >
                        <option value="mcq">Multiple Choice</option>
                        <option value="boolean">True/False</option>
                        <option value="faq">FAQ</option>
                        <option value="fill_blank">Fill in the Blank (Premium)</option>
                    </select>
                    
                    <input
                        type="number"
                        value={numQuestions}
                        onChange={(e) => setNumQuestions(parseInt(e.target.value))}
                        min="1"
                        max="10"
                    />
                    
                    <button onClick={handleGenerate} disabled={loading}>
                        {loading ? 'Generating...' : 'Generate Questions'}
                    </button>
                </div>
            </div>

            {questions.length > 0 && (
                <div className="results-section">
                    <h3>Generated Questions ({questions.length})</h3>
                    
                    <div className="export-buttons">
                        <button onClick={() => handleExport('json')}>Export JSON</button>
                        <button onClick={() => handleExport('csv')}>Export CSV</button>
                        <button onClick={() => handleExport('qti')}>Export QTI (Premium)</button>
                        <button onClick={() => handleExport('moodle')}>Export Moodle (Premium)</button>
                    </div>
                    
                    <div className="questions-list">
                        {questions.map((q, index) => (
                            <div key={index} className="question-item">
                                <h4>Question {index + 1}</h4>
                                <p><strong>Q:</strong> {q.question}</p>
                                <p><strong>Type:</strong> {q.type}</p>
                                <p><strong>Answer:</strong> {q.answer}</p>
                                {q.options && (
                                    <div>
                                        <strong>Options:</strong>
                                        <ul>
                                            {q.options.map((option, i) => (
                                                <li key={i}>{option}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                                {q.explanation && (
                                    <p><strong>Explanation:</strong> {q.explanation}</p>
                                )}
                                <p><strong>Confidence:</strong> {(q.confidence * 100).toFixed(1)}%</p>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default QuestionGenerator;

---

## API Reference

### Core Classes

#### QuestionGenerator

The main class for generating questions from various input sources.

```python
from quizaigen import QuestionGenerator

generator = QuestionGenerator(config=None)
```

**Methods:**

| Method | Parameters | Returns | Tier | Description |
|--------|------------|---------|------|-------------|
| `generate_mcq(text, num_questions=5, **kwargs)` | text: str, num_questions: int | List[Question] | Free | Generate multiple choice questions |
| `generate_boolean(text, num_questions=5, **kwargs)` | text: str, num_questions: int | List[Question] | Free | Generate true/false questions |
| `generate_faq(text, num_questions=5, **kwargs)` | text: str, num_questions: int | List[Question] | Free | Generate FAQ pairs |
| `generate_fill_blank(text, num_questions=5, **kwargs)` | text: str, num_questions: int | List[Question] | Premium | Generate fill-in-the-blank questions |
| `generate_paraphrased(text, num_questions=5, **kwargs)` | text: str, num_questions: int | List[Question] | Free | Generate paraphrased questions |
| `generate_qa(text, num_questions=5, **kwargs)` | text: str, num_questions: int | List[Question] | Free | Generate question-answer pairs |
| `generate_mixed(text, types=['mcq', 'boolean'], **kwargs)` | text: str, types: List[str] | List[Question] | Free/Premium | Generate mixed question types |
| `generate_from_pdf(file_path, **kwargs)` | file_path: str | List[Question] | Premium | Generate questions from PDF |
| `generate_from_url(url, **kwargs)` | url: str | List[Question] | Premium | Generate questions from URL |

#### ExportManager

Handles exporting questions to various formats.

```python
from quizaigen import ExportManager

exporter = ExportManager()
```

**Methods:**

| Method | Parameters | Returns | Tier | Description |
|--------|------------|---------|------|-------------|
| `export_questions(questions, output_path, format='json', **kwargs)` | questions: List[dict], output_path: str, format: str | None | Free/Premium | Export questions to specified format |
| `get_supported_formats()` | None | List[str] | Free | Get list of supported export formats |
| `validate_export_data(questions)` | questions: List[dict] | bool | Free | Validate questions data before export |

**Supported Export Formats:**

| Format | Extension | Tier | Description | Use Case |
|--------|-----------|------|-------------|----------|
| JSON | .json | Free | JavaScript Object Notation | API integration, web apps |
| CSV | .csv | Free | Comma-separated values | Spreadsheets, data analysis |
| XML | .xml | Free | Extensible Markup Language | Data exchange |
| QTI | .qti | Premium | Question & Test Interoperability | LMS integration |
| Moodle | .xml | Premium | Moodle XML format | Moodle LMS |
| AIKEN | .txt | Premium | AIKEN text format | Simple quiz tools |
| RESPONDUS | .txt | Premium | Respondus format | Respondus test banks |
| GIFT | .txt | Premium | Moodle GIFT format | Moodle import |

### Data Models

#### Question Object

```python
{
    "question": "What is the capital of France?",
    "type": "mcq",
    "answer": "Paris",  # or index for MCQ
    "options": ["Paris", "London", "Berlin", "Madrid"],  # for MCQ
    "explanation": "Paris is the capital and largest city of France.",
    "difficulty": "easy",  # easy, medium, hard
    "keywords": ["France", "capital", "geography"],
    "source_text": "Original text snippet",
    "confidence": 0.95,  # 0.0 to 1.0
    "metadata": {
        "category": "geography",
        "topic": "capitals",
        "created_at": "2025-06-27T20:31:02Z"
    }
}
```

#### Configuration Object

```python
from quizaigen.core.config import Config

config = Config(
    license_key="your-license-key",
    tier="premium",  # free, premium, enterprise
    max_questions_per_request=100,
    enable_logging=True,
    log_level="INFO",
    model_cache_size=1000,
    timeout_seconds=30
)
```

---

## Export Formats

### JSON Format (Free Tier)

```json
{
    "questions": [
        {
            "question": "What is Python?",
            "type": "mcq",
            "answer": 0,
            "options": ["Programming language", "Snake", "Tool", "Framework"],
            "explanation": "Python is a high-level programming language.",
            "difficulty": "easy",
            "confidence": 0.95,
            "metadata": {
                "category": "programming",
                "created_at": "2025-06-27T20:31:02Z"
            }
        }
    ],
    "metadata": {
        "total_questions": 1,
        "export_format": "json",
        "generated_at": "2025-06-27T20:31:02Z",
        "library_version": "1.0.0"
    }
}
```

### CSV Format (Free Tier)

```csv
question,type,answer,options,explanation,difficulty,confidence
"What is Python?",mcq,0,"Programming language|Snake|Tool|Framework","Python is a high-level programming language.",easy,0.95
```

### QTI Format (Premium Tier)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<questestinterop>
    <assessment ident="quiz_001" title="Generated Quiz">
        <section ident="section_001">
            <item ident="item_001" title="Question 1">
                <presentation>
                    <material>
                        <mattext>What is Python?</mattext>
                    </material>
                    <response_lid ident="response_001" rcardinality="Single">
                        <render_choice>
                            <response_label ident="A">
                                <material><mattext>Programming language</mattext></material>
                            </response_label>
                            <!-- Additional options... -->
                        </render_choice>
                    </response_lid>
                </presentation>
                <resprocessing>
                    <outcomes>
                        <decvar varname="SCORE" vartype="Decimal"/>
                    </outcomes>
                    <respcondition continue="No">
                        <conditionvar>
                            <varequal respident="response_001">A</varequal>
                        </conditionvar>
                        <setvar action="Set" varname="SCORE">100</setvar>
                    </respcondition>
                </resprocessing>
            </item>
        </section>
    </assessment>
</questestinterop>
```

### AIKEN Format (Premium Tier)

```
What is Python?
A. Programming language
B. Snake
C. Tool
D. Framework
ANSWER: A

Python was created by whom?
A. Bill Gates
B. Guido van Rossum
C. Mark Zuckerberg
D. Steve Jobs
ANSWER: B
```

### GIFT Format (Premium Tier)

```
What is Python? {=Programming language ~Snake ~Tool ~Framework}

Python was created by Guido van Rossum. {T}

The first version of Python was released in {=1991}.
```

---

## Error Handling

### Exception Types

```python
from quizaigen.core.exceptions import (
    ValidationError,
    ProcessingError,
    LicenseError,
    ConfigurationError
)

try:
    questions = generator.generate_mcq(text, num_questions=5)
except ValidationError as e:
    # Handle input validation errors
    print(f"Validation error: {e.message}")
    print(f"Field: {e.field}")
    print(f"Value: {e.value}")
except ProcessingError as e:
    # Handle processing errors
    print(f"Processing error: {e.message}")
    print(f"Stage: {e.stage}")
except LicenseError as e:
    # Handle license validation errors
    print(f"License error: {e.message}")
    print(f"Required tier: {e.required_tier}")
except ConfigurationError as e:
    # Handle configuration errors
    print(f"Configuration error: {e.message}")
    print(f"Setting: {e.setting}")
```

### Error Response Format

```json
{
    "success": false,
    "error": {
        "type": "ValidationError",
        "message": "Text input is required",
        "field": "text",
        "code": "MISSING_REQUIRED_FIELD"
    },
    "timestamp": "2025-06-27T20:31:02Z",
    "request_id": "req_123456"
}
```

### Common Error Scenarios

| Error Type | Cause | Solution |
|------------|-------|----------|
| ValidationError | Invalid input parameters | Check input format and requirements |
| ProcessingError | AI model processing failure | Retry with different text or parameters |
| LicenseError | Invalid or expired license | Update license key or upgrade tier |
| ConfigurationError | Invalid configuration | Check configuration settings |
| RateLimitError | Too many requests | Implement rate limiting and retry logic |
| TimeoutError | Processing timeout | Reduce text size or increase timeout |

---

## Performance Considerations

### Optimization Tips

1. **Batch Processing**: Generate multiple questions in single requests
2. **Caching**: Enable model caching for repeated operations
3. **Text Preprocessing**: Clean and optimize input text
4. **Async Processing**: Use async/await for non-blocking operations
5. **Resource Management**: Monitor memory usage for large documents

### Performance Benchmarks

| Operation | Input Size | Processing Time | Memory Usage |
|-----------|------------|-----------------|--------------|
| MCQ Generation | 1000 words | ~2-3 seconds | ~50MB |
| Fill-blank Generation | 1000 words | ~3-4 seconds | ~60MB |
| PDF Processing | 10 pages | ~5-8 seconds | ~80MB |
| Export (JSON) | 100 questions | ~0.5 seconds | ~10MB |
| Export (QTI) | 100 questions | ~1-2 seconds | ~15MB |

### Scaling Recommendations

- **Small Scale** (< 100 requests/day): Single instance deployment
- **Medium Scale** (< 10K requests/day): Load balancer + 2-3 instances
- **Large Scale** (> 10K requests/day): Microservices architecture with caching

---

## Security & Licensing

### License Validation

```python
from quizaigen.core.license import LicenseValidator

# Validate license on application startup
validator = LicenseValidator()
license_info = validator.validate_license(
    license_key="your-license-key",
    tier="premium"
)

if not license_info.is_valid:
    raise LicenseError(f"Invalid license: {license_info.error}")
```

### Feature Access Control

```python
from quizaigen.core.features import FeatureManager

feature_manager = FeatureManager(license_info)

# Check feature availability
if feature_manager.is_available('fill_blank_generation'):
    questions = generator.generate_fill_blank(text)
else:
    raise LicenseError("Fill-blank generation requires Premium tier")
```

### Security Best Practices

1. **API Keys**: Store license keys securely (environment variables)
2. **Input Validation**: Sanitize all user inputs
3. **Rate Limiting**: Implement request rate limiting
4. **Logging**: Log all API requests for monitoring
5. **HTTPS**: Use HTTPS for all API communications
6. **Authentication**: Implement proper user authentication

### Compliance

- **GDPR**: No personal data is stored by the library
- **SOC 2**: Enterprise tier includes compliance features
- **HIPAA**: Available with enterprise licensing
- **Data Retention**: Configurable data retention policies

---

## Support & Resources

### Documentation
- **API Documentation**: [docs.quizaigen.com/api](https://docs.quizaigen.com/api)
- **Integration Examples**: [github.com/quizaigen/examples](https://github.com/quizaigen/examples)
- **Video Tutorials**: [youtube.com/quizaigen](https://youtube.com/quizaigen)

### Support Channels
- **Community Support**: [community.quizaigen.com](https://community.quizaigen.com)
- **Premium Support**: <EMAIL>
- **Enterprise Support**: <EMAIL>

### Licensing
- **Free Tier**: No license required
- **Premium Tier**: <EMAIL>
- **Enterprise Tier**: <EMAIL>

---

*This integration guide covers all aspects of QuizAIGen integration. For specific implementation questions, please contact our support team.*
```
