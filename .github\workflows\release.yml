name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., 1.0.0)'
        required: true
        type: string

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y tesseract-ocr tesseract-ocr-eng poppler-utils

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        pip install -e .

    - name: Download spaCy models
      run: |
        python -m spacy download en_core_web_sm

    - name: Run tests
      run: |
        pytest tests/ -v --cov=quizaigen

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: |
        python -m build

    - name: Check package
      run: |
        twine check dist/*

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  publish-test-pypi:
    needs: build
    runs-on: ubuntu-latest
    if: contains(github.ref_name, 'alpha') || contains(github.ref_name, 'beta') || contains(github.ref_name, 'rc')
    environment: test-release
    permissions:
      id-token: write  # For trusted publishing

    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Publish to Test PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: https://test.pypi.org/legacy/
        password: ${{ secrets.TEST_PYPI_API_TOKEN }}

  publish-pypi:
    needs: build
    runs-on: ubuntu-latest
    if: ${{ !contains(github.ref_name, 'alpha') && !contains(github.ref_name, 'beta') && !contains(github.ref_name, 'rc') }}
    environment: release
    permissions:
      id-token: write  # For trusted publishing

    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Validate package before publishing
      run: |
        pip install twine
        twine check dist/*

    - name: Publish to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.PYPI_API_TOKEN }}

  create-release:
    needs: publish-pypi
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Generate changelog
      id: changelog
      run: |
        # Get the latest tag
        LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
        
        if [ -z "$LATEST_TAG" ]; then
          # First release
          CHANGELOG=$(git log --pretty=format:"- %s" --reverse)
        else
          # Get changes since last tag
          CHANGELOG=$(git log ${LATEST_TAG}..HEAD --pretty=format:"- %s" --reverse)
        fi
        
        # Save changelog to file
        echo "$CHANGELOG" > CHANGELOG.md
        
        # Set output for GitHub release
        echo "changelog<<EOF" >> $GITHUB_OUTPUT
        echo "$CHANGELOG" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref_name }}
        release_name: Release ${{ github.ref_name }}
        body: |
          ## Changes in this release
          
          ${{ steps.changelog.outputs.changelog }}
          
          ## Installation
          
          ```bash
          pip install quizaigen==${{ github.ref_name }}
          ```
          
          ## Documentation
          
          See the [README](https://github.com/${{ github.repository }}/blob/${{ github.ref_name }}/README.md) for usage instructions.
        draft: false
        prerelease: ${{ contains(github.ref_name, 'alpha') || contains(github.ref_name, 'beta') || contains(github.ref_name, 'rc') }}

  post-release:
    needs: [publish-pypi, publish-test-pypi, create-release]
    runs-on: ubuntu-latest
    if: always() && (needs.publish-pypi.result == 'success' || needs.publish-test-pypi.result == 'success')

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests

    - name: Verify PyPI publication
      run: |
        python -c "
        import requests
        import time
        import sys

        package_name = 'quizaigen'
        version = '${{ github.ref_name }}'.replace('v', '')
        is_prerelease = any(x in version for x in ['alpha', 'beta', 'rc'])

        if is_prerelease:
            url = f'https://test.pypi.org/pypi/{package_name}/{version}/json'
            registry = 'Test PyPI'
        else:
            url = f'https://pypi.org/pypi/{package_name}/{version}/json'
            registry = 'PyPI'

        print(f'Checking {registry} for {package_name} v{version}...')

        # Wait up to 5 minutes for package to be available
        for attempt in range(30):
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    print(f'✅ Package successfully published to {registry}!')
                    data = response.json()
                    print(f'Package URL: {data[\"info\"][\"package_url\"]}')
                    sys.exit(0)
                elif response.status_code == 404:
                    print(f'Attempt {attempt + 1}/30: Package not yet available...')
                    time.sleep(10)
                else:
                    print(f'Unexpected response: {response.status_code}')
                    time.sleep(10)
            except Exception as e:
                print(f'Error checking package: {e}')
                time.sleep(10)

        print('❌ Package not found after 5 minutes')
        sys.exit(1)
        "

    - name: Create success summary
      run: |
        echo '## 🎉 Release Summary' >> $GITHUB_STEP_SUMMARY
        echo '' >> $GITHUB_STEP_SUMMARY
        echo '**Package**: quizaigen' >> $GITHUB_STEP_SUMMARY
        echo '**Version**: ${{ github.ref_name }}' >> $GITHUB_STEP_SUMMARY
        echo '**Status**: ✅ Successfully published' >> $GITHUB_STEP_SUMMARY
        echo '' >> $GITHUB_STEP_SUMMARY

        if [[ "${{ github.ref_name }}" == *"alpha"* ]] || [[ "${{ github.ref_name }}" == *"beta"* ]] || [[ "${{ github.ref_name }}" == *"rc"* ]]; then
          echo '**Registry**: Test PyPI' >> $GITHUB_STEP_SUMMARY
          echo '**Installation**: `pip install -i https://test.pypi.org/simple/ quizaigen`' >> $GITHUB_STEP_SUMMARY
        else
          echo '**Registry**: PyPI' >> $GITHUB_STEP_SUMMARY
          echo '**Installation**: `pip install quizaigen`' >> $GITHUB_STEP_SUMMARY
        fi

        echo '' >> $GITHUB_STEP_SUMMARY
        echo '### Next Steps' >> $GITHUB_STEP_SUMMARY
        echo '- Test the published package' >> $GITHUB_STEP_SUMMARY
        echo '- Update documentation if needed' >> $GITHUB_STEP_SUMMARY
        echo '- Announce the release' >> $GITHUB_STEP_SUMMARY

  notify:
    needs: [publish-pypi, create-release]
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: Notify success
      if: needs.publish-pypi.result == 'success' && needs.create-release.result == 'success'
      run: |
        echo "✅ Release ${{ github.ref_name }} published successfully!"
        echo "📦 Package available on PyPI: https://pypi.org/project/quizaigen/"
        echo "🚀 GitHub release created: https://github.com/${{ github.repository }}/releases/tag/${{ github.ref_name }}"

    - name: Notify failure
      if: needs.publish-pypi.result == 'failure' || needs.create-release.result == 'failure'
      run: |
        echo "❌ Release ${{ github.ref_name }} failed!"
        echo "Check the workflow logs for details."
        exit 1
