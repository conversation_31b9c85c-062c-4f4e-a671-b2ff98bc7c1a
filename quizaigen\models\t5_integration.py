"""
T5 Model Integration for QuizAIGen

Provides T5-based question generation for Premium and Enterprise tiers.
Focuses on improving fill-in-blank questions and general question quality.
"""

import time
from typing import Dict, Any, List, Optional, Union
import re

from .base_model import BaseModel, ModelConfig, ModelOutput, ModelTier
from ..core.exceptions import ProcessingError

try:
    from transformers import T5ForConditionalGeneration, T5Tokenizer
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False


class T5QuestionGenerator(BaseModel):
    """
    T5-based question generator for premium features.
    
    Specializes in:
    - Fill-in-blank question optimization
    - Question paraphrasing and improvement
    - Context-aware question generation
    """
    
    # Model configurations for different tiers
    TIER_MODELS = {
        ModelTier.PREMIUM: {
            "model_name": "t5-small",
            "max_length": 256,
            "batch_size": 2
        },
        ModelTier.ENTERPRISE: {
            "model_name": "t5-base",
            "max_length": 512,
            "batch_size": 4
        }
    }
    
    def __init__(self, config: Optional[ModelConfig] = None, tier: ModelTier = ModelTier.PREMIUM):
        """
        Initialize T5 question generator.
        
        Args:
            config: Model configuration (optional)
            tier: Model tier for feature access
        """
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError(
                "T5 integration requires transformers and torch. "
                "Install with: pip install transformers torch"
            )
        
        # Use tier-specific defaults if no config provided
        if config is None:
            tier_config = self.TIER_MODELS.get(tier, self.TIER_MODELS[ModelTier.PREMIUM])
            config = ModelConfig(
                model_name=tier_config["model_name"],
                max_length=tier_config["max_length"],
                batch_size=tier_config["batch_size"],
                tier=tier
            )
        
        super().__init__(config)
        self.tier = tier
        
        # Task-specific prompts
        self.task_prompts = {
            "fill_blank": "Generate a fill-in-the-blank question from: ",
            "improve_question": "Improve this question: ",
            "paraphrase": "Paraphrase this question: ",
            "generate_mcq": "Generate a multiple choice question from: "
        }
    
    def load_model(self) -> None:
        """Load T5 model and tokenizer."""
        try:
            self.log_info(f"Loading T5 model: {self.config.model_name}")
            
            # Load tokenizer
            self.tokenizer = T5Tokenizer.from_pretrained(
                self.config.model_name,
                cache_dir=self.config.cache_dir
            )
            
            # Load model
            self.model = T5ForConditionalGeneration.from_pretrained(
                self.config.model_name,
                cache_dir=self.config.cache_dir
            )
            
            # Move to specified device
            if self.config.device != "cpu" and torch.cuda.is_available():
                self.model = self.model.to(self.config.device)
                self.log_info(f"Model moved to {self.config.device}")
            
            self.is_loaded = True
            self.log_info("T5 model loaded successfully")
            
        except Exception as e:
            self.log_error(f"Failed to load T5 model: {str(e)}")
            raise ProcessingError(
                stage="model_loading",
                message=f"Failed to load T5 model: {str(e)}"
            )
    
    def _inference(self, input_text: str, task: str = "fill_blank", **kwargs) -> ModelOutput:
        """
        Perform T5 inference for question generation.
        
        Args:
            input_text: Input text
            task: Task type (fill_blank, improve_question, paraphrase, generate_mcq)
            **kwargs: Additional parameters
            
        Returns:
            Model output
        """
        start_time = time.time()
        
        # Prepare input with task-specific prompt
        prompt = self.task_prompts.get(task, "Generate a question from: ")
        full_input = prompt + input_text
        
        try:
            # Tokenize input
            inputs = self.tokenizer.encode(
                full_input,
                return_tensors="pt",
                max_length=self.config.max_length,
                truncation=True,
                padding=True
            )
            
            # Move to device if needed
            if self.config.device != "cpu":
                inputs = inputs.to(self.config.device)
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=self.config.max_length,
                    num_return_sequences=1,
                    temperature=self.config.temperature,
                    top_k=self.config.top_k,
                    top_p=self.config.top_p,
                    do_sample=True,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode output
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Post-process output
            processed_text = self._post_process_output(generated_text, task)
            
            # Calculate confidence
            confidence = self._calculate_confidence(input_text, processed_text, task)
            
            processing_time = time.time() - start_time
            
            return ModelOutput(
                text=processed_text,
                confidence=confidence,
                metadata={
                    "task": task,
                    "model_name": self.config.model_name,
                    "input_length": len(input_text),
                    "output_length": len(processed_text),
                    "device": self.config.device
                },
                processing_time=processing_time,
                model_name=self.config.model_name
            )
            
        except Exception as e:
            self.log_error(f"T5 inference failed: {str(e)}")
            raise ProcessingError(
                stage="t5_inference",
                message=f"T5 inference failed: {str(e)}"
            )
    
    def generate_fill_blank_question(self, text: str, **kwargs) -> ModelOutput:
        """
        Generate optimized fill-in-blank question using T5.
        
        Args:
            text: Input text
            **kwargs: Additional parameters
            
        Returns:
            Generated fill-blank question
        """
        return self._inference(text, task="fill_blank", **kwargs)
    
    def improve_question(self, question: str, **kwargs) -> ModelOutput:
        """
        Improve existing question using T5.
        
        Args:
            question: Original question
            **kwargs: Additional parameters
            
        Returns:
            Improved question
        """
        return self._inference(question, task="improve_question", **kwargs)
    
    def paraphrase_question(self, question: str, **kwargs) -> ModelOutput:
        """
        Paraphrase question using T5.
        
        Args:
            question: Original question
            **kwargs: Additional parameters
            
        Returns:
            Paraphrased question
        """
        return self._inference(question, task="paraphrase", **kwargs)
    
    def generate_mcq_question(self, text: str, **kwargs) -> ModelOutput:
        """
        Generate MCQ question using T5.
        
        Args:
            text: Input text
            **kwargs: Additional parameters
            
        Returns:
            Generated MCQ question
        """
        return self._inference(text, task="generate_mcq", **kwargs)
    
    def _post_process_output(self, generated_text: str, task: str) -> str:
        """
        Post-process T5 output for specific tasks.
        
        Args:
            generated_text: Raw T5 output
            task: Task type
            
        Returns:
            Post-processed text
        """
        # Clean up common T5 artifacts
        text = generated_text.strip()
        
        # Remove repetitive patterns
        text = re.sub(r'\b(\w+)\s+\1\b', r'\1', text)
        
        # Task-specific post-processing
        if task == "fill_blank":
            # Ensure proper blank formatting
            text = re.sub(r'_+', '______', text)
            # Remove multiple blanks in same sentence
            text = re.sub(r'(______.*?)______', r'\1', text)
        
        elif task == "improve_question":
            # Ensure question ends with proper punctuation
            if not text.endswith(('?', '.', '!')):
                text += '?'
        
        elif task == "generate_mcq":
            # Basic MCQ formatting
            if not text.endswith('?'):
                text += '?'
        
        return text
    
    def _calculate_confidence(self, input_text: str, output_text: str, task: str) -> float:
        """
        Calculate confidence score for T5 output.
        
        Args:
            input_text: Original input
            output_text: Generated output
            task: Task type
            
        Returns:
            Confidence score
        """
        confidence = 0.7  # Base confidence for T5
        
        # Length-based adjustments
        if len(output_text) < 10:
            confidence -= 0.2
        elif len(output_text) > 200:
            confidence -= 0.1
        
        # Task-specific confidence adjustments
        if task == "fill_blank":
            if "______" in output_text:
                confidence += 0.1
            else:
                confidence -= 0.3
        
        elif task == "improve_question":
            if output_text.endswith('?'):
                confidence += 0.05
        
        # Input quality factor
        if len(input_text) > 50:
            confidence += 0.05
        
        # Tier-based confidence boost
        if self.tier == ModelTier.ENTERPRISE:
            confidence += 0.1
        
        return min(0.95, max(0.1, confidence))
    
    def batch_generate(self, texts: List[str], task: str = "fill_blank", **kwargs) -> List[ModelOutput]:
        """
        Generate questions for multiple texts in batch.
        
        Args:
            texts: List of input texts
            task: Task type
            **kwargs: Additional parameters
            
        Returns:
            List of generated outputs
        """
        self._check_tier_access()
        
        if not self.is_loaded:
            self.load_model()
        
        outputs = []
        batch_size = self.config.batch_size
        
        # Process in batches
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            
            for text in batch_texts:
                try:
                    output = self._inference(text, task=task, **kwargs)
                    outputs.append(output)
                except Exception as e:
                    self.log_warning(f"Batch generation failed for text: {str(e)}")
                    # Create fallback output
                    outputs.append(ModelOutput(
                        text="",
                        confidence=0.0,
                        metadata={"error": str(e), "task": task},
                        processing_time=0.0,
                        model_name=self.config.model_name
                    ))
        
        return outputs
