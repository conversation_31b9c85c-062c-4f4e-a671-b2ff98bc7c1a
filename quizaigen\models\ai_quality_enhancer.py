"""
AI Quality Enhancer for QuizAIGen

Combines T5 and BERT models to provide comprehensive question quality enhancement.
Integrates with existing generators to improve question quality for Premium/Enterprise tiers.
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import time

from .t5_integration import T5QuestionGenerator
from .bert_integration import BERTAnswerValidator, AnswerValidationResult
from .base_model import ModelTier, ModelConfig
from .model_cache import ModelCache
from ..generators.base import Question
from ..core.exceptions import ProcessingError
from ..utils.logger import LoggerMixin


@dataclass
class QualityEnhancementResult:
    """Result of quality enhancement process."""
    original_question: Question
    enhanced_question: Question
    improvements: List[str]
    quality_scores: Dict[str, float]
    confidence_boost: float
    processing_time: float


class AIQualityEnhancer(LoggerMixin):
    """
    AI-powered quality enhancer for questions.
    
    Combines T5 and BERT models to:
    - Improve question phrasing and clarity
    - Validate answer quality
    - Enhance fill-in-blank questions
    - Score and filter questions by quality
    """
    
    def __init__(self, 
                 tier: ModelTier = ModelTier.PREMIUM,
                 enable_caching: bool = True,
                 cache_dir: Optional[str] = None):
        """
        Initialize AI quality enhancer.
        
        Args:
            tier: Model tier for feature access
            enable_caching: Whether to enable model caching
            cache_dir: Cache directory path
        """
        self.tier = tier
        self.enable_caching = enable_caching
        
        # Initialize models
        self.t5_generator = None
        self.bert_validator = None
        self.model_cache = None
        
        # Quality thresholds by tier
        self.quality_thresholds = {
            ModelTier.PREMIUM: {
                "min_confidence": 0.6,
                "min_semantic_similarity": 0.4,
                "min_overall_quality": 0.5
            },
            ModelTier.ENTERPRISE: {
                "min_confidence": 0.7,
                "min_semantic_similarity": 0.5,
                "min_overall_quality": 0.6
            }
        }
        
        # Initialize cache if enabled
        if enable_caching:
            self.model_cache = ModelCache(
                cache_dir=cache_dir,
                max_memory_entries=500,
                default_ttl=7200  # 2 hours
            )
        
        self.log_info(f"Initialized AIQualityEnhancer for {tier.value} tier")
    
    def _ensure_models_loaded(self) -> None:
        """Ensure T5 and BERT models are loaded."""
        if self.t5_generator is None:
            self.log_info("Loading T5 generator...")
            self.t5_generator = T5QuestionGenerator(tier=self.tier)
        
        if self.bert_validator is None:
            self.log_info("Loading BERT validator...")
            self.bert_validator = BERTAnswerValidator(tier=self.tier)
    
    def enhance_question(self, question: Question, context: str = "") -> QualityEnhancementResult:
        """
        Enhance a single question using AI models.
        
        Args:
            question: Original question to enhance
            context: Optional context text
            
        Returns:
            Quality enhancement result
        """
        start_time = time.time()
        
        try:
            self._ensure_models_loaded()
            
            improvements = []
            enhanced_question = Question(
                question=question.question,
                answer=question.answer,
                type=question.type,
                options=question.options,
                explanation=question.explanation,
                difficulty=question.difficulty,
                keywords=question.keywords,
                source_text=question.source_text,
                confidence=question.confidence,
                metadata=question.metadata.copy() if question.metadata else {}
            )
            
            # Step 1: Improve question text using T5
            if question.type in ['mcq', 'boolean', 'faq']:
                improved_text = self._improve_question_text(question.question, context)
                if improved_text and improved_text != question.question:
                    enhanced_question.question = improved_text
                    improvements.append("Improved question phrasing")
            
            # Step 2: Optimize fill-in-blank questions
            elif question.type == 'fill_blank':
                optimized_question = self._optimize_fill_blank(question, context)
                if optimized_question:
                    enhanced_question = optimized_question
                    improvements.append("Optimized fill-in-blank structure")
            
            # Step 3: Validate and improve answer
            if question.answer:
                answer_validation = self._validate_answer(
                    enhanced_question.question, 
                    question.answer, 
                    context
                )
                
                if not answer_validation.is_valid:
                    # Try to improve answer
                    improved_answer = self._improve_answer(
                        enhanced_question.question, 
                        question.answer, 
                        context
                    )
                    if improved_answer:
                        enhanced_question.answer = improved_answer
                        improvements.append("Improved answer quality")
                
                # Update confidence based on validation
                if answer_validation.confidence > enhanced_question.confidence:
                    enhanced_question.confidence = answer_validation.confidence
                    improvements.append("Boosted confidence score")
            
            # Step 4: Score overall quality
            quality_scores = self._score_question_quality(enhanced_question, context)
            
            # Step 5: Apply quality-based improvements
            if quality_scores.get("clarity", 0) < 0.6:
                clarified_question = self._clarify_question(enhanced_question.question)
                if clarified_question:
                    enhanced_question.question = clarified_question
                    improvements.append("Improved question clarity")
            
            # Update metadata
            enhanced_question.metadata.update({
                "ai_enhanced": True,
                "enhancement_tier": self.tier.value,
                "quality_scores": quality_scores,
                "improvements": improvements,
                "original_confidence": question.confidence
            })
            
            # Calculate confidence boost
            confidence_boost = enhanced_question.confidence - question.confidence
            
            processing_time = time.time() - start_time
            
            return QualityEnhancementResult(
                original_question=question,
                enhanced_question=enhanced_question,
                improvements=improvements,
                quality_scores=quality_scores,
                confidence_boost=confidence_boost,
                processing_time=processing_time
            )
            
        except Exception as e:
            self.log_error(f"Question enhancement failed: {str(e)}")
            # Return original question if enhancement fails
            return QualityEnhancementResult(
                original_question=question,
                enhanced_question=question,
                improvements=["Enhancement failed"],
                quality_scores={},
                confidence_boost=0.0,
                processing_time=time.time() - start_time
            )
    
    def enhance_question_batch(self, questions: List[Question], 
                             context: str = "") -> List[QualityEnhancementResult]:
        """
        Enhance multiple questions in batch.
        
        Args:
            questions: List of questions to enhance
            context: Optional context text
            
        Returns:
            List of enhancement results
        """
        self.log_info(f"Enhancing batch of {len(questions)} questions")
        
        results = []
        for i, question in enumerate(questions):
            try:
                result = self.enhance_question(question, context)
                results.append(result)
                
                if (i + 1) % 10 == 0:
                    self.log_info(f"Enhanced {i + 1}/{len(questions)} questions")
                    
            except Exception as e:
                self.log_warning(f"Failed to enhance question {i}: {str(e)}")
                # Add failed result
                results.append(QualityEnhancementResult(
                    original_question=question,
                    enhanced_question=question,
                    improvements=[f"Enhancement failed: {str(e)}"],
                    quality_scores={},
                    confidence_boost=0.0,
                    processing_time=0.0
                ))
        
        self.log_info(f"Completed batch enhancement: {len(results)} results")
        return results
    
    def filter_by_quality(self, questions: List[Question], 
                         context: str = "") -> Tuple[List[Question], List[Question]]:
        """
        Filter questions by quality thresholds.
        
        Args:
            questions: List of questions to filter
            context: Optional context text
            
        Returns:
            Tuple of (high_quality_questions, low_quality_questions)
        """
        self._ensure_models_loaded()
        
        thresholds = self.quality_thresholds.get(self.tier, self.quality_thresholds[ModelTier.PREMIUM])
        
        high_quality = []
        low_quality = []
        
        for question in questions:
            try:
                # Score question quality
                quality_scores = self._score_question_quality(question, context)
                
                # Check thresholds
                meets_quality = (
                    question.confidence >= thresholds["min_confidence"] and
                    quality_scores.get("overall_quality", 0) >= thresholds["min_overall_quality"]
                )
                
                if meets_quality:
                    high_quality.append(question)
                else:
                    low_quality.append(question)
                    
            except Exception as e:
                self.log_warning(f"Quality filtering failed for question: {str(e)}")
                low_quality.append(question)
        
        self.log_info(f"Quality filtering: {len(high_quality)} high quality, {len(low_quality)} low quality")
        return high_quality, low_quality
    
    def _improve_question_text(self, question_text: str, context: str = "") -> Optional[str]:
        """Improve question text using T5."""
        try:
            # Check cache first
            if self.model_cache:
                cached_result = self.model_cache.get(
                    question_text, "t5_improve", context=context
                )
                if cached_result:
                    return cached_result.text
            
            # Generate improvement
            result = self.t5_generator.improve_question(question_text)
            
            # Cache result
            if self.model_cache and result.confidence > 0.5:
                self.model_cache.put(
                    question_text, "t5_improve", result, self.tier, context=context
                )
            
            return result.text if result.confidence > 0.5 else None
            
        except Exception as e:
            self.log_warning(f"Question text improvement failed: {str(e)}")
            return None
    
    def _optimize_fill_blank(self, question: Question, context: str = "") -> Optional[Question]:
        """Optimize fill-in-blank question using T5."""
        try:
            # Use T5 to generate better fill-blank question
            source_text = context or question.source_text or question.question
            result = self.t5_generator.generate_fill_blank_question(source_text)
            
            if result.confidence > question.confidence:
                # Create optimized question
                optimized = Question(
                    question=result.text,
                    answer=question.answer,  # Keep original answer for now
                    type='fill_blank',
                    confidence=result.confidence,
                    metadata={
                        **(question.metadata or {}),
                        "t5_optimized": True
                    }
                )
                return optimized
            
            return None
            
        except Exception as e:
            self.log_warning(f"Fill-blank optimization failed: {str(e)}")
            return None
    
    def _validate_answer(self, question: str, answer: str, context: str = "") -> AnswerValidationResult:
        """Validate answer using BERT."""
        try:
            return self.bert_validator.validate_answer(question, answer, context)
        except Exception as e:
            self.log_warning(f"Answer validation failed: {str(e)}")
            return AnswerValidationResult(
                is_valid=True,  # Default to valid if validation fails
                confidence=0.5,
                relevance_score=0.5,
                semantic_similarity=0.5,
                explanation="Validation failed",
                metadata={"error": str(e)}
            )
    
    def _improve_answer(self, question: str, answer: str, context: str = "") -> Optional[str]:
        """Improve answer quality (placeholder for future implementation)."""
        # This could use T5 to paraphrase/improve answers
        # For now, return None (no improvement)
        return None
    
    def _score_question_quality(self, question: Question, context: str = "") -> Dict[str, float]:
        """Score question quality using BERT."""
        try:
            return self.bert_validator.score_question_quality(question.question, context)
        except Exception as e:
            self.log_warning(f"Quality scoring failed: {str(e)}")
            return {
                "clarity": 0.5,
                "complexity": 0.5,
                "context_relevance": 0.5,
                "semantic_coherence": 0.5,
                "overall_quality": 0.5
            }
    
    def _clarify_question(self, question_text: str) -> Optional[str]:
        """Clarify question using T5."""
        try:
            # Use T5 to improve clarity
            result = self.t5_generator.improve_question(question_text)
            return result.text if result.confidence > 0.6 else None
        except Exception as e:
            self.log_warning(f"Question clarification failed: {str(e)}")
            return None
    
    def get_enhancement_stats(self) -> Dict[str, Any]:
        """Get enhancement statistics."""
        stats = {
            "tier": self.tier.value,
            "models_loaded": {
                "t5": self.t5_generator is not None and self.t5_generator.is_loaded,
                "bert": self.bert_validator is not None and self.bert_validator.is_loaded
            },
            "caching_enabled": self.enable_caching
        }
        
        if self.model_cache:
            stats["cache_stats"] = self.model_cache.get_cache_stats()
        
        return stats
