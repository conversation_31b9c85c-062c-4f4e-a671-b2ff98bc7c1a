/* Generated by Cython 0.29.37 */

/* BEGIN: Cython Metadata
{
    "distutils": {
        "depends": [],
        "include_dirs": [
            "C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-build-env-npo5e4e8\\overlay\\Lib\\site-packages\\numpy\\_core\\include",
            "C:\\Users\\<USER>\\AppData\\Local\\pypa\\cibuildwheel\\Cache\\nuget-cpython\\python.3.12.7\\tools\\Include"
        ],
        "language": "c++",
        "name": "thinc.backends.linalg",
        "sources": [
            "thinc/backends/linalg.pyx"
        ]
    },
    "module_name": "thinc.backends.linalg"
}
END: Cython Metadata */

#ifndef PY_SSIZE_T_CLEAN
#define PY_SSIZE_T_CLEAN
#endif /* PY_SSIZE_T_CLEAN */
#include "Python.h"
#ifndef Py_PYTHON_H
    #error Python headers needed to compile C extensions, please install development version of Python.
#elif PY_VERSION_HEX < 0x02060000 || (0x03000000 <= PY_VERSION_HEX && PY_VERSION_HEX < 0x03030000)
    #error Cython requires Python 2.6+ or Python 3.3+.
#else
#define CYTHON_ABI "0_29_37"
#define CYTHON_HEX_VERSION 0x001D25F0
#define CYTHON_FUTURE_DIVISION 0
#include <stddef.h>
#ifndef offsetof
  #define offsetof(type, member) ( (size_t) & ((type*)0) -> member )
#endif
#if !defined(WIN32) && !defined(MS_WINDOWS)
  #ifndef __stdcall
    #define __stdcall
  #endif
  #ifndef __cdecl
    #define __cdecl
  #endif
  #ifndef __fastcall
    #define __fastcall
  #endif
#endif
#ifndef DL_IMPORT
  #define DL_IMPORT(t) t
#endif
#ifndef DL_EXPORT
  #define DL_EXPORT(t) t
#endif
#define __PYX_COMMA ,
#ifndef HAVE_LONG_LONG
  #if PY_VERSION_HEX >= 0x02070000
    #define HAVE_LONG_LONG
  #endif
#endif
#ifndef PY_LONG_LONG
  #define PY_LONG_LONG LONG_LONG
#endif
#ifndef Py_HUGE_VAL
  #define Py_HUGE_VAL HUGE_VAL
#endif
#ifdef PYPY_VERSION
  #define CYTHON_COMPILING_IN_PYPY 1
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #undef CYTHON_USE_TYPE_SLOTS
  #define CYTHON_USE_TYPE_SLOTS 0
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #if PY_VERSION_HEX < 0x03050000
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #undef CYTHON_USE_UNICODE_INTERNALS
  #define CYTHON_USE_UNICODE_INTERNALS 0
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #undef CYTHON_AVOID_BORROWED_REFS
  #define CYTHON_AVOID_BORROWED_REFS 1
  #undef CYTHON_ASSUME_SAFE_MACROS
  #define CYTHON_ASSUME_SAFE_MACROS 0
  #undef CYTHON_UNPACK_METHODS
  #define CYTHON_UNPACK_METHODS 0
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #if PY_VERSION_HEX < 0x03090000
    #undef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #elif !defined(CYTHON_PEP489_MULTI_PHASE_INIT)
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE (PY_VERSION_HEX >= 0x030400a1 && PYPY_VERSION_NUM >= 0x07030C00)
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
#elif defined(PYSTON_VERSION)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 1
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #undef CYTHON_USE_ASYNC_SLOTS
  #define CYTHON_USE_ASYNC_SLOTS 0
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #undef CYTHON_PEP489_MULTI_PHASE_INIT
  #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE 0
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
#elif defined(PY_NOGIL)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 1
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #ifndef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE 1
  #endif
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
#else
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 1
  #define CYTHON_COMPILING_IN_NOGIL 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYTYPE_LOOKUP
    #define CYTHON_USE_PYTYPE_LOOKUP 0
  #elif !defined(CYTHON_USE_PYTYPE_LOOKUP)
    #define CYTHON_USE_PYTYPE_LOOKUP 1
  #endif
  #if PY_MAJOR_VERSION < 3
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYLONG_INTERNALS
    #define CYTHON_USE_PYLONG_INTERNALS 0
  #elif !defined(CYTHON_USE_PYLONG_INTERNALS)
    #define CYTHON_USE_PYLONG_INTERNALS (PY_VERSION_HEX < 0x030C00A5)
  #endif
  #ifndef CYTHON_USE_PYLIST_INTERNALS
    #define CYTHON_USE_PYLIST_INTERNALS 1
  #endif
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #if PY_VERSION_HEX < 0x030300F0 || PY_VERSION_HEX >= 0x030B00A2
    #undef CYTHON_USE_UNICODE_WRITER
    #define CYTHON_USE_UNICODE_WRITER 0
  #elif !defined(CYTHON_USE_UNICODE_WRITER)
    #define CYTHON_USE_UNICODE_WRITER 1
  #endif
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #if PY_VERSION_HEX >= 0x030B00A4
    #undef CYTHON_FAST_THREAD_STATE
    #define CYTHON_FAST_THREAD_STATE 0
  #elif !defined(CYTHON_FAST_THREAD_STATE)
    #define CYTHON_FAST_THREAD_STATE 1
  #endif
  #ifndef CYTHON_FAST_PYCALL
    #define CYTHON_FAST_PYCALL (PY_VERSION_HEX < 0x030A0000)
  #endif
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT (PY_VERSION_HEX >= 0x03050000)
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE (PY_VERSION_HEX >= 0x030400a1)
  #endif
  #ifndef CYTHON_USE_DICT_VERSIONS
    #define CYTHON_USE_DICT_VERSIONS ((PY_VERSION_HEX >= 0x030600B1) && (PY_VERSION_HEX < 0x030C00A5))
  #endif
  #if PY_VERSION_HEX >= 0x030B00A4
    #undef CYTHON_USE_EXC_INFO_STACK
    #define CYTHON_USE_EXC_INFO_STACK 0
  #elif !defined(CYTHON_USE_EXC_INFO_STACK)
    #define CYTHON_USE_EXC_INFO_STACK (PY_VERSION_HEX >= 0x030700A3)
  #endif
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 1
  #endif
#endif
#if !defined(CYTHON_FAST_PYCCALL)
#define CYTHON_FAST_PYCCALL  (CYTHON_FAST_PYCALL && PY_VERSION_HEX >= 0x030600B1)
#endif
#if CYTHON_USE_PYLONG_INTERNALS
  #if PY_MAJOR_VERSION < 3
    #include "longintrepr.h"
  #endif
  #undef SHIFT
  #undef BASE
  #undef MASK
  #ifdef SIZEOF_VOID_P
    enum { __pyx_check_sizeof_voidp = 1 / (int)(SIZEOF_VOID_P == sizeof(void*)) };
  #endif
#endif
#ifndef __has_attribute
  #define __has_attribute(x) 0
#endif
#ifndef __has_cpp_attribute
  #define __has_cpp_attribute(x) 0
#endif
#ifndef CYTHON_RESTRICT
  #if defined(__GNUC__)
    #define CYTHON_RESTRICT __restrict__
  #elif defined(_MSC_VER) && _MSC_VER >= 1400
    #define CYTHON_RESTRICT __restrict
  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define CYTHON_RESTRICT restrict
  #else
    #define CYTHON_RESTRICT
  #endif
#endif
#ifndef CYTHON_UNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define CYTHON_UNUSED __attribute__ ((__unused__))
#   else
#     define CYTHON_UNUSED
#   endif
# elif defined(__ICC) || (defined(__INTEL_COMPILER) && !defined(_MSC_VER))
#   define CYTHON_UNUSED __attribute__ ((__unused__))
# else
#   define CYTHON_UNUSED
# endif
#endif
#ifndef CYTHON_MAYBE_UNUSED_VAR
#  if defined(__cplusplus)
     template<class T> void CYTHON_MAYBE_UNUSED_VAR( const T& ) { }
#  else
#    define CYTHON_MAYBE_UNUSED_VAR(x) (void)(x)
#  endif
#endif
#ifndef CYTHON_NCP_UNUSED
# if CYTHON_COMPILING_IN_CPYTHON
#  define CYTHON_NCP_UNUSED
# else
#  define CYTHON_NCP_UNUSED CYTHON_UNUSED
# endif
#endif
#define __Pyx_void_to_None(void_result) ((void)(void_result), Py_INCREF(Py_None), Py_None)
#ifdef _MSC_VER
    #ifndef _MSC_STDINT_H_
        #if _MSC_VER < 1300
           typedef unsigned char     uint8_t;
           typedef unsigned int      uint32_t;
        #else
           typedef unsigned __int8   uint8_t;
           typedef unsigned __int32  uint32_t;
        #endif
    #endif
#else
   #include <stdint.h>
#endif
#ifndef CYTHON_FALLTHROUGH
  #if defined(__cplusplus) && __cplusplus >= 201103L
    #if __has_cpp_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH [[fallthrough]]
    #elif __has_cpp_attribute(clang::fallthrough)
      #define CYTHON_FALLTHROUGH [[clang::fallthrough]]
    #elif __has_cpp_attribute(gnu::fallthrough)
      #define CYTHON_FALLTHROUGH [[gnu::fallthrough]]
    #endif
  #endif
  #ifndef CYTHON_FALLTHROUGH
    #if __has_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH __attribute__((fallthrough))
    #else
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
  #if defined(__clang__ ) && defined(__apple_build_version__)
    #if __apple_build_version__ < 7000000
      #undef  CYTHON_FALLTHROUGH
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
#endif

#ifndef __cplusplus
  #error "Cython files generated with the C++ option must be compiled with a C++ compiler."
#endif
#ifndef CYTHON_INLINE
  #if defined(__clang__)
    #define CYTHON_INLINE __inline__ __attribute__ ((__unused__))
  #else
    #define CYTHON_INLINE inline
  #endif
#endif
template<typename T>
void __Pyx_call_destructor(T& x) {
    x.~T();
}
template<typename T>
class __Pyx_FakeReference {
  public:
    __Pyx_FakeReference() : ptr(NULL) { }
    __Pyx_FakeReference(const T& ref) : ptr(const_cast<T*>(&ref)) { }
    T *operator->() { return ptr; }
    T *operator&() { return ptr; }
    operator T&() { return *ptr; }
    template<typename U> bool operator ==(U other) { return *ptr == other; }
    template<typename U> bool operator !=(U other) { return *ptr != other; }
  private:
    T *ptr;
};

#define __PYX_BUILD_PY_SSIZE_T "n"
#define CYTHON_FORMAT_SSIZE_T "z"
#if PY_MAJOR_VERSION < 3
  #define __Pyx_BUILTIN_MODULE_NAME "__builtin__"
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a+k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
  #define __Pyx_DefaultClassType PyClass_Type
#else
  #define __Pyx_BUILTIN_MODULE_NAME "builtins"
  #define __Pyx_DefaultClassType PyType_Type
#if PY_VERSION_HEX >= 0x030B00A1
    static CYTHON_INLINE PyCodeObject* __Pyx_PyCode_New(int a, int k, int l, int s, int f,
                                                    PyObject *code, PyObject *c, PyObject* n, PyObject *v,
                                                    PyObject *fv, PyObject *cell, PyObject* fn,
                                                    PyObject *name, int fline, PyObject *lnos) {
        PyObject *kwds=NULL, *argcount=NULL, *posonlyargcount=NULL, *kwonlyargcount=NULL;
        PyObject *nlocals=NULL, *stacksize=NULL, *flags=NULL, *replace=NULL, *call_result=NULL, *empty=NULL;
        const char *fn_cstr=NULL;
        const char *name_cstr=NULL;
        PyCodeObject* co=NULL;
        PyObject *type, *value, *traceback;
        PyErr_Fetch(&type, &value, &traceback);
        if (!(kwds=PyDict_New())) goto end;
        if (!(argcount=PyLong_FromLong(a))) goto end;
        if (PyDict_SetItemString(kwds, "co_argcount", argcount) != 0) goto end;
        if (!(posonlyargcount=PyLong_FromLong(0))) goto end;
        if (PyDict_SetItemString(kwds, "co_posonlyargcount", posonlyargcount) != 0) goto end;
        if (!(kwonlyargcount=PyLong_FromLong(k))) goto end;
        if (PyDict_SetItemString(kwds, "co_kwonlyargcount", kwonlyargcount) != 0) goto end;
        if (!(nlocals=PyLong_FromLong(l))) goto end;
        if (PyDict_SetItemString(kwds, "co_nlocals", nlocals) != 0) goto end;
        if (!(stacksize=PyLong_FromLong(s))) goto end;
        if (PyDict_SetItemString(kwds, "co_stacksize", stacksize) != 0) goto end;
        if (!(flags=PyLong_FromLong(f))) goto end;
        if (PyDict_SetItemString(kwds, "co_flags", flags) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_code", code) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_consts", c) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_names", n) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_varnames", v) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_freevars", fv) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_cellvars", cell) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_linetable", lnos) != 0) goto end;
        if (!(fn_cstr=PyUnicode_AsUTF8AndSize(fn, NULL))) goto end;
        if (!(name_cstr=PyUnicode_AsUTF8AndSize(name, NULL))) goto end;
        if (!(co = PyCode_NewEmpty(fn_cstr, name_cstr, fline))) goto end;
        if (!(replace = PyObject_GetAttrString((PyObject*)co, "replace"))) goto cleanup_code_too;
        if (!(empty = PyTuple_New(0))) goto cleanup_code_too; // unfortunately __pyx_empty_tuple isn't available here
        if (!(call_result = PyObject_Call(replace, empty, kwds))) goto cleanup_code_too;
        Py_XDECREF((PyObject*)co);
        co = (PyCodeObject*)call_result;
        call_result = NULL;
        if (0) {
            cleanup_code_too:
            Py_XDECREF((PyObject*)co);
            co = NULL;
        }
        end:
        Py_XDECREF(kwds);
        Py_XDECREF(argcount);
        Py_XDECREF(posonlyargcount);
        Py_XDECREF(kwonlyargcount);
        Py_XDECREF(nlocals);
        Py_XDECREF(stacksize);
        Py_XDECREF(replace);
        Py_XDECREF(call_result);
        Py_XDECREF(empty);
        if (type) {
            PyErr_Restore(type, value, traceback);
        }
        return co;
    }
#else
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
#endif
  #define __Pyx_DefaultClassType PyType_Type
#endif
#if PY_VERSION_HEX >= 0x030900F0 && !CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyObject_GC_IsFinalized(o) PyObject_GC_IsFinalized(o)
#else
  #define __Pyx_PyObject_GC_IsFinalized(o) _PyGC_FINALIZED(o)
#endif
#ifndef Py_TPFLAGS_CHECKTYPES
  #define Py_TPFLAGS_CHECKTYPES 0
#endif
#ifndef Py_TPFLAGS_HAVE_INDEX
  #define Py_TPFLAGS_HAVE_INDEX 0
#endif
#ifndef Py_TPFLAGS_HAVE_NEWBUFFER
  #define Py_TPFLAGS_HAVE_NEWBUFFER 0
#endif
#ifndef Py_TPFLAGS_HAVE_FINALIZE
  #define Py_TPFLAGS_HAVE_FINALIZE 0
#endif
#ifndef METH_STACKLESS
  #define METH_STACKLESS 0
#endif
#if PY_VERSION_HEX <= 0x030700A3 || !defined(METH_FASTCALL)
  #ifndef METH_FASTCALL
     #define METH_FASTCALL 0x80
  #endif
  typedef PyObject *(*__Pyx_PyCFunctionFast) (PyObject *self, PyObject *const *args, Py_ssize_t nargs);
  typedef PyObject *(*__Pyx_PyCFunctionFastWithKeywords) (PyObject *self, PyObject *const *args,
                                                          Py_ssize_t nargs, PyObject *kwnames);
#else
  #define __Pyx_PyCFunctionFast _PyCFunctionFast
  #define __Pyx_PyCFunctionFastWithKeywords _PyCFunctionFastWithKeywords
#endif
#if CYTHON_FAST_PYCCALL
#define __Pyx_PyFastCFunction_Check(func)\
    ((PyCFunction_Check(func) && (METH_FASTCALL == (PyCFunction_GET_FLAGS(func) & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)))))
#else
#define __Pyx_PyFastCFunction_Check(func) 0
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Malloc)
  #define PyObject_Malloc(s)   PyMem_Malloc(s)
  #define PyObject_Free(p)     PyMem_Free(p)
  #define PyObject_Realloc(p)  PyMem_Realloc(p)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030400A1
  #define PyMem_RawMalloc(n)           PyMem_Malloc(n)
  #define PyMem_RawRealloc(p, n)       PyMem_Realloc(p, n)
  #define PyMem_RawFree(p)             PyMem_Free(p)
#endif
#if CYTHON_COMPILING_IN_PYSTON
  #define __Pyx_PyCode_HasFreeVars(co)  PyCode_HasFreeVars(co)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno) PyFrame_SetLineNumber(frame, lineno)
#else
  #define __Pyx_PyCode_HasFreeVars(co)  (PyCode_GetNumFree(co) > 0)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno)  (frame)->f_lineno = (lineno)
#endif
#if !CYTHON_FAST_THREAD_STATE || PY_VERSION_HEX < 0x02070000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#elif PY_VERSION_HEX >= 0x03060000
  #define __Pyx_PyThreadState_Current _PyThreadState_UncheckedGet()
#elif PY_VERSION_HEX >= 0x03000000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#else
  #define __Pyx_PyThreadState_Current _PyThreadState_Current
#endif
#if PY_VERSION_HEX < 0x030700A2 && !defined(PyThread_tss_create) && !defined(Py_tss_NEEDS_INIT)
#include "pythread.h"
#define Py_tss_NEEDS_INIT 0
typedef int Py_tss_t;
static CYTHON_INLINE int PyThread_tss_create(Py_tss_t *key) {
  *key = PyThread_create_key();
  return 0;
}
static CYTHON_INLINE Py_tss_t * PyThread_tss_alloc(void) {
  Py_tss_t *key = (Py_tss_t *)PyObject_Malloc(sizeof(Py_tss_t));
  *key = Py_tss_NEEDS_INIT;
  return key;
}
static CYTHON_INLINE void PyThread_tss_free(Py_tss_t *key) {
  PyObject_Free(key);
}
static CYTHON_INLINE int PyThread_tss_is_created(Py_tss_t *key) {
  return *key != Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE void PyThread_tss_delete(Py_tss_t *key) {
  PyThread_delete_key(*key);
  *key = Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE int PyThread_tss_set(Py_tss_t *key, void *value) {
  return PyThread_set_key_value(*key, value);
}
static CYTHON_INLINE void * PyThread_tss_get(Py_tss_t *key) {
  return PyThread_get_key_value(*key);
}
#endif
#if CYTHON_COMPILING_IN_CPYTHON || defined(_PyDict_NewPresized)
#define __Pyx_PyDict_NewPresized(n)  ((n <= 8) ? PyDict_New() : _PyDict_NewPresized(n))
#else
#define __Pyx_PyDict_NewPresized(n)  PyDict_New()
#endif
#if PY_MAJOR_VERSION >= 3 || CYTHON_FUTURE_DIVISION
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_TrueDivide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceTrueDivide(x,y)
#else
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_Divide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceDivide(x,y)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1 && CYTHON_USE_UNICODE_INTERNALS
#define __Pyx_PyDict_GetItemStr(dict, name)  _PyDict_GetItem_KnownHash(dict, name, ((PyASCIIObject *) name)->hash)
#else
#define __Pyx_PyDict_GetItemStr(dict, name)  PyDict_GetItem(dict, name)
#endif
#if PY_VERSION_HEX > 0x03030000 && defined(PyUnicode_KIND)
  #define CYTHON_PEP393_ENABLED 1
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_READY(op)       (0)
  #else
    #define __Pyx_PyUnicode_READY(op)       (likely(PyUnicode_IS_READY(op)) ?\
                                                0 : _PyUnicode_Ready((PyObject *)(op)))
  #endif
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_LENGTH(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) PyUnicode_READ_CHAR(u, i)
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   PyUnicode_MAX_CHAR_VALUE(u)
  #define __Pyx_PyUnicode_KIND(u)         PyUnicode_KIND(u)
  #define __Pyx_PyUnicode_DATA(u)         PyUnicode_DATA(u)
  #define __Pyx_PyUnicode_READ(k, d, i)   PyUnicode_READ(k, d, i)
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  PyUnicode_WRITE(k, d, i, ch)
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_LENGTH(u))
  #else
    #if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x03090000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : ((PyCompactUnicodeObject *)(u))->wstr_length))
    #else
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : PyUnicode_GET_SIZE(u)))
    #endif
  #endif
#else
  #define CYTHON_PEP393_ENABLED 0
  #define PyUnicode_1BYTE_KIND  1
  #define PyUnicode_2BYTE_KIND  2
  #define PyUnicode_4BYTE_KIND  4
  #define __Pyx_PyUnicode_READY(op)       (0)
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_SIZE(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) ((Py_UCS4)(PyUnicode_AS_UNICODE(u)[i]))
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   ((sizeof(Py_UNICODE) == 2) ? 65535 : 1114111)
  #define __Pyx_PyUnicode_KIND(u)         (sizeof(Py_UNICODE))
  #define __Pyx_PyUnicode_DATA(u)         ((void*)PyUnicode_AS_UNICODE(u))
  #define __Pyx_PyUnicode_READ(k, d, i)   ((void)(k), (Py_UCS4)(((Py_UNICODE*)d)[i]))
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  (((void)(k)), ((Py_UNICODE*)d)[i] = ch)
  #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_SIZE(u))
#endif
#if CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyUnicode_Concat(a, b)      PyNumber_Add(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  PyNumber_Add(a, b)
#else
  #define __Pyx_PyUnicode_Concat(a, b)      PyUnicode_Concat(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  ((unlikely((a) == Py_None) || unlikely((b) == Py_None)) ?\
      PyNumber_Add(a, b) : __Pyx_PyUnicode_Concat(a, b))
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyUnicode_Contains)
  #define PyUnicode_Contains(u, s)  PySequence_Contains(u, s)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyByteArray_Check)
  #define PyByteArray_Check(obj)  PyObject_TypeCheck(obj, &PyByteArray_Type)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Format)
  #define PyObject_Format(obj, fmt)  PyObject_CallMethod(obj, "__format__", "O", fmt)
#endif
#define __Pyx_PyString_FormatSafe(a, b)   ((unlikely((a) == Py_None || (PyString_Check(b) && !PyString_CheckExact(b)))) ? PyNumber_Remainder(a, b) : __Pyx_PyString_Format(a, b))
#define __Pyx_PyUnicode_FormatSafe(a, b)  ((unlikely((a) == Py_None || (PyUnicode_Check(b) && !PyUnicode_CheckExact(b)))) ? PyNumber_Remainder(a, b) : PyUnicode_Format(a, b))
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyString_Format(a, b)  PyUnicode_Format(a, b)
#else
  #define __Pyx_PyString_Format(a, b)  PyString_Format(a, b)
#endif
#if PY_MAJOR_VERSION < 3 && !defined(PyObject_ASCII)
  #define PyObject_ASCII(o)            PyObject_Repr(o)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBaseString_Type            PyUnicode_Type
  #define PyStringObject               PyUnicodeObject
  #define PyString_Type                PyUnicode_Type
  #define PyString_Check               PyUnicode_Check
  #define PyString_CheckExact          PyUnicode_CheckExact
#ifndef PyObject_Unicode
  #define PyObject_Unicode             PyObject_Str
#endif
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyBaseString_Check(obj) PyUnicode_Check(obj)
  #define __Pyx_PyBaseString_CheckExact(obj) PyUnicode_CheckExact(obj)
#else
  #define __Pyx_PyBaseString_Check(obj) (PyString_Check(obj) || PyUnicode_Check(obj))
  #define __Pyx_PyBaseString_CheckExact(obj) (PyString_CheckExact(obj) || PyUnicode_CheckExact(obj))
#endif
#ifndef PySet_CheckExact
  #define PySet_CheckExact(obj)        (Py_TYPE(obj) == &PySet_Type)
#endif
#if PY_VERSION_HEX >= 0x030900A4
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_SET_REFCNT(obj, refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SET_SIZE(obj, size)
#else
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_REFCNT(obj) = (refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SIZE(obj) = (size)
#endif
#if CYTHON_ASSUME_SAFE_MACROS
  #define __Pyx_PySequence_SIZE(seq)  Py_SIZE(seq)
#else
  #define __Pyx_PySequence_SIZE(seq)  PySequence_Size(seq)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyIntObject                  PyLongObject
  #define PyInt_Type                   PyLong_Type
  #define PyInt_Check(op)              PyLong_Check(op)
  #define PyInt_CheckExact(op)         PyLong_CheckExact(op)
  #define PyInt_FromString             PyLong_FromString
  #define PyInt_FromUnicode            PyLong_FromUnicode
  #define PyInt_FromLong               PyLong_FromLong
  #define PyInt_FromSize_t             PyLong_FromSize_t
  #define PyInt_FromSsize_t            PyLong_FromSsize_t
  #define PyInt_AsLong                 PyLong_AsLong
  #define PyInt_AS_LONG                PyLong_AS_LONG
  #define PyInt_AsSsize_t              PyLong_AsSsize_t
  #define PyInt_AsUnsignedLongMask     PyLong_AsUnsignedLongMask
  #define PyInt_AsUnsignedLongLongMask PyLong_AsUnsignedLongLongMask
  #define PyNumber_Int                 PyNumber_Long
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBoolObject                 PyLongObject
#endif
#if PY_MAJOR_VERSION >= 3 && CYTHON_COMPILING_IN_PYPY
  #ifndef PyUnicode_InternFromString
    #define PyUnicode_InternFromString(s) PyUnicode_FromString(s)
  #endif
#endif
#if PY_VERSION_HEX < 0x030200A4
  typedef long Py_hash_t;
  #define __Pyx_PyInt_FromHash_t PyInt_FromLong
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsHash_t
#else
  #define __Pyx_PyInt_FromHash_t PyInt_FromSsize_t
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsSsize_t
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyMethod_New(func, self, klass) ((self) ? ((void)(klass), PyMethod_New(func, self)) : __Pyx_NewRef(func))
#else
  #define __Pyx_PyMethod_New(func, self, klass) PyMethod_New(func, self, klass)
#endif
#if CYTHON_USE_ASYNC_SLOTS
  #if PY_VERSION_HEX >= 0x030500B1
    #define __Pyx_PyAsyncMethodsStruct PyAsyncMethods
    #define __Pyx_PyType_AsAsync(obj) (Py_TYPE(obj)->tp_as_async)
  #else
    #define __Pyx_PyType_AsAsync(obj) ((__Pyx_PyAsyncMethodsStruct*) (Py_TYPE(obj)->tp_reserved))
  #endif
#else
  #define __Pyx_PyType_AsAsync(obj) NULL
#endif
#ifndef __Pyx_PyAsyncMethodsStruct
    typedef struct {
        unaryfunc am_await;
        unaryfunc am_aiter;
        unaryfunc am_anext;
    } __Pyx_PyAsyncMethodsStruct;
#endif

#if defined(_WIN32) || defined(WIN32) || defined(MS_WINDOWS)
  #if !defined(_USE_MATH_DEFINES)
    #define _USE_MATH_DEFINES
  #endif
#endif
#include <math.h>
#ifdef NAN
#define __PYX_NAN() ((float) NAN)
#else
static CYTHON_INLINE float __PYX_NAN() {
  float value;
  memset(&value, 0xFF, sizeof(value));
  return value;
}
#endif
#if defined(__CYGWIN__) && defined(_LDBL_EQ_DBL)
#define __Pyx_truncl trunc
#else
#define __Pyx_truncl truncl
#endif

#define __PYX_MARK_ERR_POS(f_index, lineno) \
    { __pyx_filename = __pyx_f[f_index]; (void)__pyx_filename; __pyx_lineno = lineno; (void)__pyx_lineno; __pyx_clineno = __LINE__; (void)__pyx_clineno; }
#define __PYX_ERR(f_index, lineno, Ln_error) \
    { __PYX_MARK_ERR_POS(f_index, lineno) goto Ln_error; }

#ifndef __PYX_EXTERN_C
  #ifdef __cplusplus
    #define __PYX_EXTERN_C extern "C"
  #else
    #define __PYX_EXTERN_C extern
  #endif
#endif

#define __PYX_HAVE__thinc__backends__linalg
#define __PYX_HAVE_API__thinc__backends__linalg
/* Early includes */
#include <stdint.h>
#include <string.h>
#include "math.h"
#ifdef _OPENMP
#include <omp.h>
#endif /* _OPENMP */

#if defined(PYREX_WITHOUT_ASSERTIONS) && !defined(CYTHON_WITHOUT_ASSERTIONS)
#define CYTHON_WITHOUT_ASSERTIONS
#endif

typedef struct {PyObject **p; const char *s; const Py_ssize_t n; const char* encoding;
                const char is_unicode; const char is_str; const char intern; } __Pyx_StringTabEntry;

#define __PYX_DEFAULT_STRING_ENCODING_IS_ASCII 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_UTF8 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT (PY_MAJOR_VERSION >= 3 && __PYX_DEFAULT_STRING_ENCODING_IS_UTF8)
#define __PYX_DEFAULT_STRING_ENCODING ""
#define __Pyx_PyObject_FromString __Pyx_PyBytes_FromString
#define __Pyx_PyObject_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#define __Pyx_uchar_cast(c) ((unsigned char)c)
#define __Pyx_long_cast(x) ((long)x)
#define __Pyx_fits_Py_ssize_t(v, type, is_signed)  (\
    (sizeof(type) < sizeof(Py_ssize_t))  ||\
    (sizeof(type) > sizeof(Py_ssize_t) &&\
          likely(v < (type)PY_SSIZE_T_MAX ||\
                 v == (type)PY_SSIZE_T_MAX)  &&\
          (!is_signed || likely(v > (type)PY_SSIZE_T_MIN ||\
                                v == (type)PY_SSIZE_T_MIN)))  ||\
    (sizeof(type) == sizeof(Py_ssize_t) &&\
          (is_signed || likely(v < (type)PY_SSIZE_T_MAX ||\
                               v == (type)PY_SSIZE_T_MAX)))  )
static CYTHON_INLINE int __Pyx_is_valid_index(Py_ssize_t i, Py_ssize_t limit) {
    return (size_t) i < (size_t) limit;
}
#if defined (__cplusplus) && __cplusplus >= 201103L
    #include <cstdlib>
    #define __Pyx_sst_abs(value) std::abs(value)
#elif SIZEOF_INT >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) abs(value)
#elif SIZEOF_LONG >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) labs(value)
#elif defined (_MSC_VER)
    #define __Pyx_sst_abs(value) ((Py_ssize_t)_abs64(value))
#elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define __Pyx_sst_abs(value) llabs(value)
#elif defined (__GNUC__)
    #define __Pyx_sst_abs(value) __builtin_llabs(value)
#else
    #define __Pyx_sst_abs(value) ((value<0) ? -value : value)
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject*);
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject*, Py_ssize_t* length);
#define __Pyx_PyByteArray_FromString(s) PyByteArray_FromStringAndSize((const char*)s, strlen((const char*)s))
#define __Pyx_PyByteArray_FromStringAndSize(s, l) PyByteArray_FromStringAndSize((const char*)s, l)
#define __Pyx_PyBytes_FromString        PyBytes_FromString
#define __Pyx_PyBytes_FromStringAndSize PyBytes_FromStringAndSize
static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char*);
#if PY_MAJOR_VERSION < 3
    #define __Pyx_PyStr_FromString        __Pyx_PyBytes_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#else
    #define __Pyx_PyStr_FromString        __Pyx_PyUnicode_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyUnicode_FromStringAndSize
#endif
#define __Pyx_PyBytes_AsWritableString(s)     ((char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableSString(s)    ((signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableUString(s)    ((unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsString(s)     ((const char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsSString(s)    ((const signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsUString(s)    ((const unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyObject_AsWritableString(s)    ((char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableSString(s)    ((signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableUString(s)    ((unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsSString(s)    ((const signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsUString(s)    ((const unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_FromCString(s)  __Pyx_PyObject_FromString((const char*)s)
#define __Pyx_PyBytes_FromCString(s)   __Pyx_PyBytes_FromString((const char*)s)
#define __Pyx_PyByteArray_FromCString(s)   __Pyx_PyByteArray_FromString((const char*)s)
#define __Pyx_PyStr_FromCString(s)     __Pyx_PyStr_FromString((const char*)s)
#define __Pyx_PyUnicode_FromCString(s) __Pyx_PyUnicode_FromString((const char*)s)
static CYTHON_INLINE size_t __Pyx_Py_UNICODE_strlen(const Py_UNICODE *u) {
    const Py_UNICODE *u_end = u;
    while (*u_end++) ;
    return (size_t)(u_end - u - 1);
}
#define __Pyx_PyUnicode_FromUnicode(u)       PyUnicode_FromUnicode(u, __Pyx_Py_UNICODE_strlen(u))
#define __Pyx_PyUnicode_FromUnicodeAndLength PyUnicode_FromUnicode
#define __Pyx_PyUnicode_AsUnicode            PyUnicode_AsUnicode
#define __Pyx_NewRef(obj) (Py_INCREF(obj), obj)
#define __Pyx_Owned_Py_None(b) __Pyx_NewRef(Py_None)
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b);
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject*);
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject*);
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x);
#define __Pyx_PySequence_Tuple(obj)\
    (likely(PyTuple_CheckExact(obj)) ? __Pyx_NewRef(obj) : PySequence_Tuple(obj))
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject*);
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t);
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject*);
#if CYTHON_ASSUME_SAFE_MACROS
#define __pyx_PyFloat_AsDouble(x) (PyFloat_CheckExact(x) ? PyFloat_AS_DOUBLE(x) : PyFloat_AsDouble(x))
#else
#define __pyx_PyFloat_AsDouble(x) PyFloat_AsDouble(x)
#endif
#define __pyx_PyFloat_AsFloat(x) ((float) __pyx_PyFloat_AsDouble(x))
#if PY_MAJOR_VERSION >= 3
#define __Pyx_PyNumber_Int(x) (PyLong_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Long(x))
#else
#define __Pyx_PyNumber_Int(x) (PyInt_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Int(x))
#endif
#define __Pyx_PyNumber_Float(x) (PyFloat_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Float(x))
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
static int __Pyx_sys_getdefaultencoding_not_ascii;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    PyObject* ascii_chars_u = NULL;
    PyObject* ascii_chars_b = NULL;
    const char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    if (strcmp(default_encoding_c, "ascii") == 0) {
        __Pyx_sys_getdefaultencoding_not_ascii = 0;
    } else {
        char ascii_chars[128];
        int c;
        for (c = 0; c < 128; c++) {
            ascii_chars[c] = c;
        }
        __Pyx_sys_getdefaultencoding_not_ascii = 1;
        ascii_chars_u = PyUnicode_DecodeASCII(ascii_chars, 128, NULL);
        if (!ascii_chars_u) goto bad;
        ascii_chars_b = PyUnicode_AsEncodedString(ascii_chars_u, default_encoding_c, NULL);
        if (!ascii_chars_b || !PyBytes_Check(ascii_chars_b) || memcmp(ascii_chars, PyBytes_AS_STRING(ascii_chars_b), 128) != 0) {
            PyErr_Format(
                PyExc_ValueError,
                "This module compiled with c_string_encoding=ascii, but default encoding '%.200s' is not a superset of ascii.",
                default_encoding_c);
            goto bad;
        }
        Py_DECREF(ascii_chars_u);
        Py_DECREF(ascii_chars_b);
    }
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    Py_XDECREF(ascii_chars_u);
    Py_XDECREF(ascii_chars_b);
    return -1;
}
#endif
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT && PY_MAJOR_VERSION >= 3
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_DecodeUTF8(c_str, size, NULL)
#else
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_Decode(c_str, size, __PYX_DEFAULT_STRING_ENCODING, NULL)
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
static char* __PYX_DEFAULT_STRING_ENCODING;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) (const char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    __PYX_DEFAULT_STRING_ENCODING = (char*) malloc(strlen(default_encoding_c) + 1);
    if (!__PYX_DEFAULT_STRING_ENCODING) goto bad;
    strcpy(__PYX_DEFAULT_STRING_ENCODING, default_encoding_c);
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    return -1;
}
#endif
#endif


/* Test for GCC > 2.95 */
#if defined(__GNUC__)     && (__GNUC__ > 2 || (__GNUC__ == 2 && (__GNUC_MINOR__ > 95)))
  #define likely(x)   __builtin_expect(!!(x), 1)
  #define unlikely(x) __builtin_expect(!!(x), 0)
#else /* !__GNUC__ or GCC < 2.95 */
  #define likely(x)   (x)
  #define unlikely(x) (x)
#endif /* __GNUC__ */
static CYTHON_INLINE void __Pyx_pretend_to_initialize(void* ptr) { (void)ptr; }

static PyObject *__pyx_m = NULL;
static PyObject *__pyx_d;
static PyObject *__pyx_b;
static PyObject *__pyx_cython_runtime = NULL;
static PyObject *__pyx_empty_tuple;
static PyObject *__pyx_empty_bytes;
static PyObject *__pyx_empty_unicode;
static int __pyx_lineno;
static int __pyx_clineno = 0;
static const char * __pyx_cfilenm= __FILE__;
static const char *__pyx_filename;


static const char *__pyx_f[] = {
  "thinc\\backends\\linalg.pxd",
  "thinc\\backends\\linalg.pyx",
  "cymem.pxd",
};

/* "thinc/backends/linalg.pxd":9
 * from libc.string cimport memcpy, memset
 * 
 * ctypedef float weight_t             # <<<<<<<<<<<<<<
 * 
 * DEF USE_BLAS = False
 */
typedef float __pyx_t_5thinc_8backends_6linalg_weight_t;

/*--- Type declarations ---*/
struct __pyx_obj_5cymem_5cymem_PyMalloc;
struct __pyx_obj_5cymem_5cymem_PyFree;
struct __pyx_obj_5cymem_5cymem_Pool;
struct __pyx_obj_5cymem_5cymem_Address;
struct __pyx_obj_5thinc_8backends_6linalg_Matrix;
struct __pyx_obj_5thinc_8backends_6linalg_Vec;
struct __pyx_obj_5thinc_8backends_6linalg_VecVec;
struct __pyx_obj_5thinc_8backends_6linalg_Mat;

/* "cymem/cymem.pxd":1
 * ctypedef void* (*malloc_t)(size_t n)             # <<<<<<<<<<<<<<
 * ctypedef void (*free_t)(void *p)
 * 
 */
typedef void *(*__pyx_t_5cymem_5cymem_malloc_t)(size_t);

/* "cymem/cymem.pxd":2
 * ctypedef void* (*malloc_t)(size_t n)
 * ctypedef void (*free_t)(void *p)             # <<<<<<<<<<<<<<
 * 
 * cdef class PyMalloc:
 */
typedef void (*__pyx_t_5cymem_5cymem_free_t)(void *);

/* "cymem/cymem.pxd":4
 * ctypedef void (*free_t)(void *p)
 * 
 * cdef class PyMalloc:             # <<<<<<<<<<<<<<
 *     cdef malloc_t malloc
 *     cdef void _set(self, malloc_t malloc)
 */
struct __pyx_obj_5cymem_5cymem_PyMalloc {
  PyObject_HEAD
  struct __pyx_vtabstruct_5cymem_5cymem_PyMalloc *__pyx_vtab;
  __pyx_t_5cymem_5cymem_malloc_t malloc;
};


/* "cymem/cymem.pxd":10
 * cdef PyMalloc WrapMalloc(malloc_t malloc)
 * 
 * cdef class PyFree:             # <<<<<<<<<<<<<<
 *     cdef free_t free
 *     cdef void _set(self, free_t free)
 */
struct __pyx_obj_5cymem_5cymem_PyFree {
  PyObject_HEAD
  struct __pyx_vtabstruct_5cymem_5cymem_PyFree *__pyx_vtab;
  __pyx_t_5cymem_5cymem_free_t free;
};


/* "cymem/cymem.pxd":16
 * cdef PyFree WrapFree(free_t free)
 * 
 * cdef class Pool:             # <<<<<<<<<<<<<<
 *     cdef readonly size_t size
 *     cdef readonly dict addresses
 */
struct __pyx_obj_5cymem_5cymem_Pool {
  PyObject_HEAD
  struct __pyx_vtabstruct_5cymem_5cymem_Pool *__pyx_vtab;
  size_t size;
  PyObject *addresses;
  PyObject *refs;
  struct __pyx_obj_5cymem_5cymem_PyMalloc *pymalloc;
  struct __pyx_obj_5cymem_5cymem_PyFree *pyfree;
};


/* "cymem/cymem.pxd":28
 * 
 * 
 * cdef class Address:             # <<<<<<<<<<<<<<
 *     cdef void* ptr
 *     cdef readonly PyMalloc pymalloc
 */
struct __pyx_obj_5cymem_5cymem_Address {
  PyObject_HEAD
  void *ptr;
  struct __pyx_obj_5cymem_5cymem_PyMalloc *pymalloc;
  struct __pyx_obj_5cymem_5cymem_PyFree *pyfree;
};


/* "thinc/backends/linalg.pxd":23
 * 
 * 
 * cdef class Matrix:             # <<<<<<<<<<<<<<
 *     cdef readonly Pool mem
 *     cdef weight_t* data
 */
struct __pyx_obj_5thinc_8backends_6linalg_Matrix {
  PyObject_HEAD
  struct __pyx_obj_5cymem_5cymem_Pool *mem;
  __pyx_t_5thinc_8backends_6linalg_weight_t *data;
  int32_t nr_row;
  int32_t nr_col;
};


/* "thinc/backends/linalg.pxd":30
 * 
 * 
 * cdef class Vec:             # <<<<<<<<<<<<<<
 *     @staticmethod
 *     cdef inline int arg_max(const weight_t* scores, const int n_classes) nogil:
 */
struct __pyx_obj_5thinc_8backends_6linalg_Vec {
  PyObject_HEAD
  struct __pyx_vtabstruct_5thinc_8backends_6linalg_Vec *__pyx_vtab;
};


/* "thinc/backends/linalg.pxd":159
 * 
 * 
 * cdef class VecVec:             # <<<<<<<<<<<<<<
 *     @staticmethod
 *     cdef inline void add(weight_t* output,
 */
struct __pyx_obj_5thinc_8backends_6linalg_VecVec {
  PyObject_HEAD
  struct __pyx_vtabstruct_5thinc_8backends_6linalg_VecVec *__pyx_vtab;
};


/* "thinc/backends/linalg.pxd":250
 * 
 * 
 * cdef class Mat:             # <<<<<<<<<<<<<<
 *     @staticmethod
 *     cdef inline void mean_row(weight_t* Ex,
 */
struct __pyx_obj_5thinc_8backends_6linalg_Mat {
  PyObject_HEAD
  struct __pyx_vtabstruct_5thinc_8backends_6linalg_Mat *__pyx_vtab;
};



/* "cymem/cymem.pxd":4
 * ctypedef void (*free_t)(void *p)
 * 
 * cdef class PyMalloc:             # <<<<<<<<<<<<<<
 *     cdef malloc_t malloc
 *     cdef void _set(self, malloc_t malloc)
 */

struct __pyx_vtabstruct_5cymem_5cymem_PyMalloc {
  void (*_set)(struct __pyx_obj_5cymem_5cymem_PyMalloc *, __pyx_t_5cymem_5cymem_malloc_t);
};
static struct __pyx_vtabstruct_5cymem_5cymem_PyMalloc *__pyx_vtabptr_5cymem_5cymem_PyMalloc;


/* "cymem/cymem.pxd":10
 * cdef PyMalloc WrapMalloc(malloc_t malloc)
 * 
 * cdef class PyFree:             # <<<<<<<<<<<<<<
 *     cdef free_t free
 *     cdef void _set(self, free_t free)
 */

struct __pyx_vtabstruct_5cymem_5cymem_PyFree {
  void (*_set)(struct __pyx_obj_5cymem_5cymem_PyFree *, __pyx_t_5cymem_5cymem_free_t);
};
static struct __pyx_vtabstruct_5cymem_5cymem_PyFree *__pyx_vtabptr_5cymem_5cymem_PyFree;


/* "cymem/cymem.pxd":16
 * cdef PyFree WrapFree(free_t free)
 * 
 * cdef class Pool:             # <<<<<<<<<<<<<<
 *     cdef readonly size_t size
 *     cdef readonly dict addresses
 */

struct __pyx_vtabstruct_5cymem_5cymem_Pool {
  void *(*alloc)(struct __pyx_obj_5cymem_5cymem_Pool *, size_t, size_t);
  void (*free)(struct __pyx_obj_5cymem_5cymem_Pool *, void *);
  void *(*realloc)(struct __pyx_obj_5cymem_5cymem_Pool *, void *, size_t);
};
static struct __pyx_vtabstruct_5cymem_5cymem_Pool *__pyx_vtabptr_5cymem_5cymem_Pool;


/* "thinc/backends/linalg.pxd":30
 * 
 * 
 * cdef class Vec:             # <<<<<<<<<<<<<<
 *     @staticmethod
 *     cdef inline int arg_max(const weight_t* scores, const int n_classes) nogil:
 */

struct __pyx_vtabstruct_5thinc_8backends_6linalg_Vec {
  int (*arg_max)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int const );
  __pyx_t_5thinc_8backends_6linalg_weight_t (*max)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
  __pyx_t_5thinc_8backends_6linalg_weight_t (*sum)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
  __pyx_t_5thinc_8backends_6linalg_weight_t (*norm)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
  void (*add)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*add_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*mul)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*mul_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*pow)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*pow_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const , int32_t);
  void (*div)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*div_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const , int32_t);
  void (*exp)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
  void (*exp_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, int32_t);
  void (*reciprocal_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, int32_t);
  __pyx_t_5thinc_8backends_6linalg_weight_t (*mean)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
  __pyx_t_5thinc_8backends_6linalg_weight_t (*variance)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
};
static struct __pyx_vtabstruct_5thinc_8backends_6linalg_Vec *__pyx_vtabptr_5thinc_8backends_6linalg_Vec;
static CYTHON_INLINE int __pyx_f_5thinc_8backends_6linalg_3Vec_arg_max(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int const );
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_max(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_sum(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_norm(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_add(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_add_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_mul(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_mul_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_pow(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_pow_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const , int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_div(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_div_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const , int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_exp(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_exp_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_reciprocal_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, int32_t);
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_mean(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_variance(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);


/* "thinc/backends/linalg.pxd":159
 * 
 * 
 * cdef class VecVec:             # <<<<<<<<<<<<<<
 *     @staticmethod
 *     cdef inline void add(weight_t* output,
 */

struct __pyx_vtabstruct_5thinc_8backends_6linalg_VecVec {
  void (*add)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*add_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*batch_add_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t, int32_t);
  void (*add_pow)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*add_pow_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
  void (*mul)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
  void (*mul_i)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
  __pyx_t_5thinc_8backends_6linalg_weight_t (*dot)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
  int (*arg_max_if_true)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int const *, int const );
  int (*arg_max_if_zero)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int const );
};
static struct __pyx_vtabstruct_5thinc_8backends_6linalg_VecVec *__pyx_vtabptr_5thinc_8backends_6linalg_VecVec;
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_batch_add_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add_pow(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add_pow_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_mul(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_mul_i(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_6VecVec_dot(__pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t);
static CYTHON_INLINE int __pyx_f_5thinc_8backends_6linalg_6VecVec_arg_max_if_true(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int const *, int const );
static CYTHON_INLINE int __pyx_f_5thinc_8backends_6linalg_6VecVec_arg_max_if_zero(__pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int const );


/* "thinc/backends/linalg.pxd":250
 * 
 * 
 * cdef class Mat:             # <<<<<<<<<<<<<<
 *     @staticmethod
 *     cdef inline void mean_row(weight_t* Ex,
 */

struct __pyx_vtabstruct_5thinc_8backends_6linalg_Mat {
  void (*mean_row)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t, int32_t);
  void (*var_row)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t, int32_t, __pyx_t_5thinc_8backends_6linalg_weight_t);
};
static struct __pyx_vtabstruct_5thinc_8backends_6linalg_Mat *__pyx_vtabptr_5thinc_8backends_6linalg_Mat;
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Mat_mean_row(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t, int32_t);
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Mat_var_row(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t, int32_t, __pyx_t_5thinc_8backends_6linalg_weight_t);

/* --- Runtime support code (head) --- */
/* Refnanny.proto */
#ifndef CYTHON_REFNANNY
  #define CYTHON_REFNANNY 0
#endif
#if CYTHON_REFNANNY
  typedef struct {
    void (*INCREF)(void*, PyObject*, int);
    void (*DECREF)(void*, PyObject*, int);
    void (*GOTREF)(void*, PyObject*, int);
    void (*GIVEREF)(void*, PyObject*, int);
    void* (*SetupContext)(const char*, int, const char*);
    void (*FinishContext)(void**);
  } __Pyx_RefNannyAPIStruct;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNanny = NULL;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname);
  #define __Pyx_RefNannyDeclarations void *__pyx_refnanny = NULL;
#ifdef WITH_THREAD
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          if (acquire_gil) {\
              PyGILState_STATE __pyx_gilstate_save = PyGILState_Ensure();\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
              PyGILState_Release(__pyx_gilstate_save);\
          } else {\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
          }
#else
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__)
#endif
  #define __Pyx_RefNannyFinishContext()\
          __Pyx_RefNanny->FinishContext(&__pyx_refnanny)
  #define __Pyx_INCREF(r)  __Pyx_RefNanny->INCREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_DECREF(r)  __Pyx_RefNanny->DECREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GOTREF(r)  __Pyx_RefNanny->GOTREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GIVEREF(r) __Pyx_RefNanny->GIVEREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_XINCREF(r)  do { if((r) != NULL) {__Pyx_INCREF(r); }} while(0)
  #define __Pyx_XDECREF(r)  do { if((r) != NULL) {__Pyx_DECREF(r); }} while(0)
  #define __Pyx_XGOTREF(r)  do { if((r) != NULL) {__Pyx_GOTREF(r); }} while(0)
  #define __Pyx_XGIVEREF(r) do { if((r) != NULL) {__Pyx_GIVEREF(r);}} while(0)
#else
  #define __Pyx_RefNannyDeclarations
  #define __Pyx_RefNannySetupContext(name, acquire_gil)
  #define __Pyx_RefNannyFinishContext()
  #define __Pyx_INCREF(r) Py_INCREF(r)
  #define __Pyx_DECREF(r) Py_DECREF(r)
  #define __Pyx_GOTREF(r)
  #define __Pyx_GIVEREF(r)
  #define __Pyx_XINCREF(r) Py_XINCREF(r)
  #define __Pyx_XDECREF(r) Py_XDECREF(r)
  #define __Pyx_XGOTREF(r)
  #define __Pyx_XGIVEREF(r)
#endif
#define __Pyx_XDECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_XDECREF(tmp);\
    } while (0)
#define __Pyx_DECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_DECREF(tmp);\
    } while (0)
#define __Pyx_CLEAR(r)    do { PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);} while(0)
#define __Pyx_XCLEAR(r)   do { if((r) != NULL) {PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);}} while(0)

/* PyObjectGetAttrStr.proto */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GetAttrStr(o,n) PyObject_GetAttr(o,n)
#endif

/* GetBuiltinName.proto */
static PyObject *__Pyx_GetBuiltinName(PyObject *name);

/* PyObject_GenericGetAttrNoDict.proto */
#if CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP && PY_VERSION_HEX < 0x03070000
static CYTHON_INLINE PyObject* __Pyx_PyObject_GenericGetAttrNoDict(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GenericGetAttrNoDict PyObject_GenericGetAttr
#endif

/* PyObject_GenericGetAttr.proto */
#if CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP && PY_VERSION_HEX < 0x03070000
static PyObject* __Pyx_PyObject_GenericGetAttr(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GenericGetAttr PyObject_GenericGetAttr
#endif

/* SetVTable.proto */
static int __Pyx_SetVtable(PyObject *dict, void *vtable);

/* TypeImport.proto */
#ifndef __PYX_HAVE_RT_ImportType_proto_0_29_37
#define __PYX_HAVE_RT_ImportType_proto_0_29_37
#if __STDC_VERSION__ >= 201112L
#include <stdalign.h>
#endif
#if __STDC_VERSION__ >= 201112L || __cplusplus >= 201103L
#define __PYX_GET_STRUCT_ALIGNMENT_0_29_37(s) alignof(s)
#else
#define __PYX_GET_STRUCT_ALIGNMENT_0_29_37(s) sizeof(void*)
#endif
enum __Pyx_ImportType_CheckSize_0_29_37 {
   __Pyx_ImportType_CheckSize_Error_0_29_37 = 0,
   __Pyx_ImportType_CheckSize_Warn_0_29_37 = 1,
   __Pyx_ImportType_CheckSize_Ignore_0_29_37 = 2
};
static PyTypeObject *__Pyx_ImportType_0_29_37(PyObject* module, const char *module_name, const char *class_name, size_t size, size_t alignment, enum __Pyx_ImportType_CheckSize_0_29_37 check_size);
#endif

/* GetVTable.proto */
static void* __Pyx_GetVtable(PyObject *dict);

/* Import.proto */
static PyObject *__Pyx_Import(PyObject *name, PyObject *from_list, int level);

/* GetTopmostException.proto */
#if CYTHON_USE_EXC_INFO_STACK
static _PyErr_StackItem * __Pyx_PyErr_GetTopmostException(PyThreadState *tstate);
#endif

/* PyThreadStateGet.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyThreadState_declare  PyThreadState *__pyx_tstate;
#define __Pyx_PyThreadState_assign  __pyx_tstate = __Pyx_PyThreadState_Current;
#define __Pyx_PyErr_Occurred()  __pyx_tstate->curexc_type
#else
#define __Pyx_PyThreadState_declare
#define __Pyx_PyThreadState_assign
#define __Pyx_PyErr_Occurred()  PyErr_Occurred()
#endif

/* SaveResetException.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_ExceptionSave(type, value, tb)  __Pyx__ExceptionSave(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx__ExceptionSave(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#define __Pyx_ExceptionReset(type, value, tb)  __Pyx__ExceptionReset(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx__ExceptionReset(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);
#else
#define __Pyx_ExceptionSave(type, value, tb)   PyErr_GetExcInfo(type, value, tb)
#define __Pyx_ExceptionReset(type, value, tb)  PyErr_SetExcInfo(type, value, tb)
#endif

/* PyErrExceptionMatches.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_ExceptionMatches(err) __Pyx_PyErr_ExceptionMatchesInState(__pyx_tstate, err)
static CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err);
#else
#define __Pyx_PyErr_ExceptionMatches(err)  PyErr_ExceptionMatches(err)
#endif

/* PyErrFetchRestore.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_Clear() __Pyx_ErrRestore(NULL, NULL, NULL)
#define __Pyx_ErrRestoreWithState(type, value, tb)  __Pyx_ErrRestoreInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)    __Pyx_ErrFetchInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  __Pyx_ErrRestoreInState(__pyx_tstate, type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)    __Pyx_ErrFetchInState(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_PyErr_SetNone(exc) (Py_INCREF(exc), __Pyx_ErrRestore((exc), NULL, NULL))
#else
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#endif
#else
#define __Pyx_PyErr_Clear() PyErr_Clear()
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#define __Pyx_ErrRestoreWithState(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestoreInState(tstate, type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchInState(tstate, type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)  PyErr_Fetch(type, value, tb)
#endif

/* PyDictVersioning.proto */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
#define __PYX_DICT_VERSION_INIT  ((PY_UINT64_T) -1)
#define __PYX_GET_DICT_VERSION(dict)  (((PyDictObject*)(dict))->ma_version_tag)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)\
    (version_var) = __PYX_GET_DICT_VERSION(dict);\
    (cache_var) = (value);
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP) {\
    static PY_UINT64_T __pyx_dict_version = 0;\
    static PyObject *__pyx_dict_cached_value = NULL;\
    if (likely(__PYX_GET_DICT_VERSION(DICT) == __pyx_dict_version)) {\
        (VAR) = __pyx_dict_cached_value;\
    } else {\
        (VAR) = __pyx_dict_cached_value = (LOOKUP);\
        __pyx_dict_version = __PYX_GET_DICT_VERSION(DICT);\
    }\
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj);
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj);
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version);
#else
#define __PYX_GET_DICT_VERSION(dict)  (0)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP)  (VAR) = (LOOKUP);
#endif

/* CLineInTraceback.proto */
#ifdef CYTHON_CLINE_IN_TRACEBACK
#define __Pyx_CLineForTraceback(tstate, c_line)  (((CYTHON_CLINE_IN_TRACEBACK)) ? c_line : 0)
#else
static int __Pyx_CLineForTraceback(PyThreadState *tstate, int c_line);
#endif

/* CodeObjectCache.proto */
typedef struct {
    PyCodeObject* code_object;
    int code_line;
} __Pyx_CodeObjectCacheEntry;
struct __Pyx_CodeObjectCache {
    int count;
    int max_count;
    __Pyx_CodeObjectCacheEntry* entries;
};
static struct __Pyx_CodeObjectCache __pyx_code_cache = {0,0,NULL};
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line);
static PyCodeObject *__pyx_find_code_object(int code_line);
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object);

/* AddTraceback.proto */
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename);

/* GCCDiagnostics.proto */
#if defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 6))
#define __Pyx_HAS_GCC_DIAGNOSTIC
#endif

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_int(int value);

/* CIntFromPy.proto */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *);

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_int32_t(int32_t value);

/* CIntFromPy.proto */
static CYTHON_INLINE int32_t __Pyx_PyInt_As_int32_t(PyObject *);

/* CIntFromPy.proto */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *);

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value);

/* FastTypeChecks.proto */
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_TypeCheck(obj, type) __Pyx_IsSubtype(Py_TYPE(obj), (PyTypeObject *)type)
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject *type);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *type1, PyObject *type2);
#else
#define __Pyx_TypeCheck(obj, type) PyObject_TypeCheck(obj, (PyTypeObject *)type)
#define __Pyx_PyErr_GivenExceptionMatches(err, type) PyErr_GivenExceptionMatches(err, type)
#define __Pyx_PyErr_GivenExceptionMatches2(err, type1, type2) (PyErr_GivenExceptionMatches(err, type1) || PyErr_GivenExceptionMatches(err, type2))
#endif
#define __Pyx_PyException_Check(obj) __Pyx_TypeCheck(obj, PyExc_Exception)

/* CheckBinaryVersion.proto */
static int __Pyx_check_binary_version(void);

/* InitStrings.proto */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t);

static CYTHON_INLINE int __pyx_f_5thinc_8backends_6linalg_3Vec_arg_max(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_scores, int const __pyx_v_n_classes); /* proto*/
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_max(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_sum(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_norm(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_add(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_inc, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_add_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_inc, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_mul(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scal, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_mul_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scal, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_pow(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scal, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_pow_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t const __pyx_v_scal, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_div(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scal, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_div_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t const __pyx_v_scal, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_exp(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_exp_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_reciprocal_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_mean(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_X, int32_t __pyx_v_nr_dim); /* proto*/
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_variance(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_X, int32_t __pyx_v_nr_dim); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scale, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scale, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_batch_add_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scale, int32_t __pyx_v_nr, int32_t __pyx_v_nr_batch); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add_pow(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_power, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add_pow_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_power, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_mul(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_mul_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_6VecVec_dot(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, int32_t __pyx_v_nr); /* proto*/
static CYTHON_INLINE int __pyx_f_5thinc_8backends_6linalg_6VecVec_arg_max_if_true(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_scores, int const *__pyx_v_is_valid, int const __pyx_v_n_classes); /* proto*/
static CYTHON_INLINE int __pyx_f_5thinc_8backends_6linalg_6VecVec_arg_max_if_zero(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_scores, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_costs, int const __pyx_v_n_classes); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Mat_mean_row(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_Ex, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_mat, int32_t __pyx_v_nr_row, int32_t __pyx_v_nr_col); /* proto*/
static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Mat_var_row(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_Vx, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_mat, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_Ex, int32_t __pyx_v_nr_row, int32_t __pyx_v_nr_col, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_eps); /* proto*/

/* Module declarations from 'cython' */

/* Module declarations from 'cymem.cymem' */
static PyTypeObject *__pyx_ptype_5cymem_5cymem_PyMalloc = 0;
static PyTypeObject *__pyx_ptype_5cymem_5cymem_PyFree = 0;
static PyTypeObject *__pyx_ptype_5cymem_5cymem_Pool = 0;
static PyTypeObject *__pyx_ptype_5cymem_5cymem_Address = 0;

/* Module declarations from 'libc.stdint' */

/* Module declarations from 'libc.string' */

/* Module declarations from 'thinc.backends.linalg' */
static PyTypeObject *__pyx_ptype_5thinc_8backends_6linalg_Matrix = 0;
static PyTypeObject *__pyx_ptype_5thinc_8backends_6linalg_Vec = 0;
static PyTypeObject *__pyx_ptype_5thinc_8backends_6linalg_VecVec = 0;
static PyTypeObject *__pyx_ptype_5thinc_8backends_6linalg_Mat = 0;
#define __Pyx_MODULE_NAME "thinc.backends.linalg"
extern int __pyx_module_is_main_thinc__backends__linalg;
int __pyx_module_is_main_thinc__backends__linalg = 0;

/* Implementation of 'thinc.backends.linalg' */
static PyObject *__pyx_builtin_range;
static PyObject *__pyx_builtin_ImportError;
static const char __pyx_k_Mat[] = "Mat";
static const char __pyx_k_Vec[] = "Vec";
static const char __pyx_k_blis[] = "blis";
static const char __pyx_k_main[] = "__main__";
static const char __pyx_k_name[] = "__name__";
static const char __pyx_k_test[] = "__test__";
static const char __pyx_k_range[] = "range";
static const char __pyx_k_Matrix[] = "Matrix";
static const char __pyx_k_VecVec[] = "VecVec";
static const char __pyx_k_import[] = "__import__";
static const char __pyx_k_blis_py[] = "blis.py";
static const char __pyx_k_pyx_vtable[] = "__pyx_vtable__";
static const char __pyx_k_ImportError[] = "ImportError";
static const char __pyx_k_cline_in_traceback[] = "cline_in_traceback";
static PyObject *__pyx_n_s_ImportError;
static PyObject *__pyx_n_s_Mat;
static PyObject *__pyx_n_s_Matrix;
static PyObject *__pyx_n_s_Vec;
static PyObject *__pyx_n_s_VecVec;
static PyObject *__pyx_n_s_blis;
static PyObject *__pyx_n_s_blis_py;
static PyObject *__pyx_n_s_cline_in_traceback;
static PyObject *__pyx_n_s_import;
static PyObject *__pyx_n_s_main;
static PyObject *__pyx_n_s_name;
static PyObject *__pyx_n_s_pyx_vtable;
static PyObject *__pyx_n_s_range;
static PyObject *__pyx_n_s_test;
static PyObject *__pyx_tp_new_5thinc_8backends_6linalg_Matrix(PyTypeObject *t, PyObject *a, PyObject *k); /*proto*/
static PyObject *__pyx_tp_new_5thinc_8backends_6linalg_Vec(PyTypeObject *t, PyObject *a, PyObject *k); /*proto*/
static PyObject *__pyx_tp_new_5thinc_8backends_6linalg_VecVec(PyTypeObject *t, PyObject *a, PyObject *k); /*proto*/
static PyObject *__pyx_tp_new_5thinc_8backends_6linalg_Mat(PyTypeObject *t, PyObject *a, PyObject *k); /*proto*/
/* Late includes */

/* "thinc/backends/linalg.pxd":32
 * cdef class Vec:
 *     @staticmethod
 *     cdef inline int arg_max(const weight_t* scores, const int n_classes) nogil:             # <<<<<<<<<<<<<<
 *         if n_classes == 2:
 *             return 0 if scores[0] > scores[1] else 1
 */

static CYTHON_INLINE int __pyx_f_5thinc_8backends_6linalg_3Vec_arg_max(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_scores, int const __pyx_v_n_classes) {
  int __pyx_v_i;
  int __pyx_v_best;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_mode;
  int __pyx_r;
  int __pyx_t_1;
  int __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;

  /* "thinc/backends/linalg.pxd":33
 *     @staticmethod
 *     cdef inline int arg_max(const weight_t* scores, const int n_classes) nogil:
 *         if n_classes == 2:             # <<<<<<<<<<<<<<
 *             return 0 if scores[0] > scores[1] else 1
 *         cdef int i
 */
  __pyx_t_1 = ((__pyx_v_n_classes == 2) != 0);
  if (__pyx_t_1) {

    /* "thinc/backends/linalg.pxd":34
 *     cdef inline int arg_max(const weight_t* scores, const int n_classes) nogil:
 *         if n_classes == 2:
 *             return 0 if scores[0] > scores[1] else 1             # <<<<<<<<<<<<<<
 *         cdef int i
 *         cdef int best = 0
 */
    if ((((__pyx_v_scores[0]) > (__pyx_v_scores[1])) != 0)) {
      __pyx_t_2 = 0;
    } else {
      __pyx_t_2 = 1;
    }
    __pyx_r = __pyx_t_2;
    goto __pyx_L0;

    /* "thinc/backends/linalg.pxd":33
 *     @staticmethod
 *     cdef inline int arg_max(const weight_t* scores, const int n_classes) nogil:
 *         if n_classes == 2:             # <<<<<<<<<<<<<<
 *             return 0 if scores[0] > scores[1] else 1
 *         cdef int i
 */
  }

  /* "thinc/backends/linalg.pxd":36
 *             return 0 if scores[0] > scores[1] else 1
 *         cdef int i
 *         cdef int best = 0             # <<<<<<<<<<<<<<
 *         cdef weight_t mode = scores[0]
 *         for i in range(1, n_classes):
 */
  __pyx_v_best = 0;

  /* "thinc/backends/linalg.pxd":37
 *         cdef int i
 *         cdef int best = 0
 *         cdef weight_t mode = scores[0]             # <<<<<<<<<<<<<<
 *         for i in range(1, n_classes):
 *             if scores[i] > mode:
 */
  __pyx_v_mode = (__pyx_v_scores[0]);

  /* "thinc/backends/linalg.pxd":38
 *         cdef int best = 0
 *         cdef weight_t mode = scores[0]
 *         for i in range(1, n_classes):             # <<<<<<<<<<<<<<
 *             if scores[i] > mode:
 *                 mode = scores[i]
 */
  __pyx_t_2 = __pyx_v_n_classes;
  __pyx_t_3 = __pyx_t_2;
  for (__pyx_t_4 = 1; __pyx_t_4 < __pyx_t_3; __pyx_t_4+=1) {
    __pyx_v_i = __pyx_t_4;

    /* "thinc/backends/linalg.pxd":39
 *         cdef weight_t mode = scores[0]
 *         for i in range(1, n_classes):
 *             if scores[i] > mode:             # <<<<<<<<<<<<<<
 *                 mode = scores[i]
 *                 best = i
 */
    __pyx_t_1 = (((__pyx_v_scores[__pyx_v_i]) > __pyx_v_mode) != 0);
    if (__pyx_t_1) {

      /* "thinc/backends/linalg.pxd":40
 *         for i in range(1, n_classes):
 *             if scores[i] > mode:
 *                 mode = scores[i]             # <<<<<<<<<<<<<<
 *                 best = i
 *         return best
 */
      __pyx_v_mode = (__pyx_v_scores[__pyx_v_i]);

      /* "thinc/backends/linalg.pxd":41
 *             if scores[i] > mode:
 *                 mode = scores[i]
 *                 best = i             # <<<<<<<<<<<<<<
 *         return best
 * 
 */
      __pyx_v_best = __pyx_v_i;

      /* "thinc/backends/linalg.pxd":39
 *         cdef weight_t mode = scores[0]
 *         for i in range(1, n_classes):
 *             if scores[i] > mode:             # <<<<<<<<<<<<<<
 *                 mode = scores[i]
 *                 best = i
 */
    }
  }

  /* "thinc/backends/linalg.pxd":42
 *                 mode = scores[i]
 *                 best = i
 *         return best             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_r = __pyx_v_best;
  goto __pyx_L0;

  /* "thinc/backends/linalg.pxd":32
 * cdef class Vec:
 *     @staticmethod
 *     cdef inline int arg_max(const weight_t* scores, const int n_classes) nogil:             # <<<<<<<<<<<<<<
 *         if n_classes == 2:
 *             return 0 if scores[0] > scores[1] else 1
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "thinc/backends/linalg.pxd":45
 * 
 *     @staticmethod
 *     cdef inline weight_t max(const weight_t* x, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         if nr == 0:
 *             return 0
 */

static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_max(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_mode;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_r;
  int __pyx_t_1;
  int32_t __pyx_t_2;
  int32_t __pyx_t_3;
  int __pyx_t_4;

  /* "thinc/backends/linalg.pxd":46
 *     @staticmethod
 *     cdef inline weight_t max(const weight_t* x, int32_t nr) nogil:
 *         if nr == 0:             # <<<<<<<<<<<<<<
 *             return 0
 *         cdef int i
 */
  __pyx_t_1 = ((__pyx_v_nr == 0) != 0);
  if (__pyx_t_1) {

    /* "thinc/backends/linalg.pxd":47
 *     cdef inline weight_t max(const weight_t* x, int32_t nr) nogil:
 *         if nr == 0:
 *             return 0             # <<<<<<<<<<<<<<
 *         cdef int i
 *         cdef weight_t mode = x[0]
 */
    __pyx_r = 0.0;
    goto __pyx_L0;

    /* "thinc/backends/linalg.pxd":46
 *     @staticmethod
 *     cdef inline weight_t max(const weight_t* x, int32_t nr) nogil:
 *         if nr == 0:             # <<<<<<<<<<<<<<
 *             return 0
 *         cdef int i
 */
  }

  /* "thinc/backends/linalg.pxd":49
 *             return 0
 *         cdef int i
 *         cdef weight_t mode = x[0]             # <<<<<<<<<<<<<<
 *         for i in range(1, nr):
 *             if x[i] > mode:
 */
  __pyx_v_mode = (__pyx_v_x[0]);

  /* "thinc/backends/linalg.pxd":50
 *         cdef int i
 *         cdef weight_t mode = x[0]
 *         for i in range(1, nr):             # <<<<<<<<<<<<<<
 *             if x[i] > mode:
 *                 mode = x[i]
 */
  __pyx_t_2 = __pyx_v_nr;
  __pyx_t_3 = __pyx_t_2;
  for (__pyx_t_4 = 1; __pyx_t_4 < __pyx_t_3; __pyx_t_4+=1) {
    __pyx_v_i = __pyx_t_4;

    /* "thinc/backends/linalg.pxd":51
 *         cdef weight_t mode = x[0]
 *         for i in range(1, nr):
 *             if x[i] > mode:             # <<<<<<<<<<<<<<
 *                 mode = x[i]
 *         return mode
 */
    __pyx_t_1 = (((__pyx_v_x[__pyx_v_i]) > __pyx_v_mode) != 0);
    if (__pyx_t_1) {

      /* "thinc/backends/linalg.pxd":52
 *         for i in range(1, nr):
 *             if x[i] > mode:
 *                 mode = x[i]             # <<<<<<<<<<<<<<
 *         return mode
 * 
 */
      __pyx_v_mode = (__pyx_v_x[__pyx_v_i]);

      /* "thinc/backends/linalg.pxd":51
 *         cdef weight_t mode = x[0]
 *         for i in range(1, nr):
 *             if x[i] > mode:             # <<<<<<<<<<<<<<
 *                 mode = x[i]
 *         return mode
 */
    }
  }

  /* "thinc/backends/linalg.pxd":53
 *             if x[i] > mode:
 *                 mode = x[i]
 *         return mode             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_r = __pyx_v_mode;
  goto __pyx_L0;

  /* "thinc/backends/linalg.pxd":45
 * 
 *     @staticmethod
 *     cdef inline weight_t max(const weight_t* x, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         if nr == 0:
 *             return 0
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "thinc/backends/linalg.pxd":56
 * 
 *     @staticmethod
 *     cdef inline weight_t sum(const weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         cdef weight_t total = 0
 */

static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_sum(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_total;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_r;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;

  /* "thinc/backends/linalg.pxd":58
 *     cdef inline weight_t sum(const weight_t* vec, int32_t nr) nogil:
 *         cdef int i
 *         cdef weight_t total = 0             # <<<<<<<<<<<<<<
 *         for i in range(nr):
 *             total += vec[i]
 */
  __pyx_v_total = 0.0;

  /* "thinc/backends/linalg.pxd":59
 *         cdef int i
 *         cdef weight_t total = 0
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             total += vec[i]
 *         return total
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":60
 *         cdef weight_t total = 0
 *         for i in range(nr):
 *             total += vec[i]             # <<<<<<<<<<<<<<
 *         return total
 * 
 */
    __pyx_v_total = (__pyx_v_total + (__pyx_v_vec[__pyx_v_i]));
  }

  /* "thinc/backends/linalg.pxd":61
 *         for i in range(nr):
 *             total += vec[i]
 *         return total             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_r = __pyx_v_total;
  goto __pyx_L0;

  /* "thinc/backends/linalg.pxd":56
 * 
 *     @staticmethod
 *     cdef inline weight_t sum(const weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         cdef weight_t total = 0
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "thinc/backends/linalg.pxd":64
 * 
 *     @staticmethod
 *     cdef inline weight_t norm(const weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef weight_t total = 0
 *         for i in range(nr):
 */

static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_norm(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, int32_t __pyx_v_nr) {
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_total;
  int32_t __pyx_v_i;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_r;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int32_t __pyx_t_3;

  /* "thinc/backends/linalg.pxd":65
 *     @staticmethod
 *     cdef inline weight_t norm(const weight_t* vec, int32_t nr) nogil:
 *         cdef weight_t total = 0             # <<<<<<<<<<<<<<
 *         for i in range(nr):
 *             total += vec[i] ** 2
 */
  __pyx_v_total = 0.0;

  /* "thinc/backends/linalg.pxd":66
 *     cdef inline weight_t norm(const weight_t* vec, int32_t nr) nogil:
 *         cdef weight_t total = 0
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             total += vec[i] ** 2
 *         return sqrt(total)
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":67
 *         cdef weight_t total = 0
 *         for i in range(nr):
 *             total += vec[i] ** 2             # <<<<<<<<<<<<<<
 *         return sqrt(total)
 * 
 */
    __pyx_v_total = (__pyx_v_total + powf(((__pyx_t_5thinc_8backends_6linalg_weight_t)(__pyx_v_vec[__pyx_v_i])), 2.0));
  }

  /* "thinc/backends/linalg.pxd":68
 *         for i in range(nr):
 *             total += vec[i] ** 2
 *         return sqrt(total)             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_r = sqrt(__pyx_v_total);
  goto __pyx_L0;

  /* "thinc/backends/linalg.pxd":64
 * 
 *     @staticmethod
 *     cdef inline weight_t norm(const weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef weight_t total = 0
 *         for i in range(nr):
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "thinc/backends/linalg.pxd":71
 * 
 *     @staticmethod
 *     cdef inline void add(weight_t* output, const weight_t* x,             # <<<<<<<<<<<<<<
 *             weight_t inc, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_add(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_inc, int32_t __pyx_v_nr) {

  /* "thinc/backends/linalg.pxd":73
 *     cdef inline void add(weight_t* output, const weight_t* x,
 *             weight_t inc, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)             # <<<<<<<<<<<<<<
 *         Vec.add_i(output, inc, nr)
 * 
 */
  (void)(memcpy(__pyx_v_output, __pyx_v_x, ((sizeof((__pyx_v_output[0]))) * __pyx_v_nr)));

  /* "thinc/backends/linalg.pxd":74
 *             weight_t inc, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 *         Vec.add_i(output, inc, nr)             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_f_5thinc_8backends_6linalg_3Vec_add_i(__pyx_v_output, __pyx_v_inc, __pyx_v_nr);

  /* "thinc/backends/linalg.pxd":71
 * 
 *     @staticmethod
 *     cdef inline void add(weight_t* output, const weight_t* x,             # <<<<<<<<<<<<<<
 *             weight_t inc, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":77
 * 
 *     @staticmethod
 *     cdef inline void add_i(weight_t* vec, weight_t inc, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_add_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_inc, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;

  /* "thinc/backends/linalg.pxd":79
 *     cdef inline void add_i(weight_t* vec, weight_t inc, int32_t nr) nogil:
 *         cdef int i
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             vec[i] += inc
 * 
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":80
 *         cdef int i
 *         for i in range(nr):
 *             vec[i] += inc             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    __pyx_t_4 = __pyx_v_i;
    (__pyx_v_vec[__pyx_t_4]) = ((__pyx_v_vec[__pyx_t_4]) + __pyx_v_inc);
  }

  /* "thinc/backends/linalg.pxd":77
 * 
 *     @staticmethod
 *     cdef inline void add_i(weight_t* vec, weight_t inc, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":83
 * 
 *     @staticmethod
 *     cdef inline void mul(weight_t* output, const weight_t* vec, weight_t scal,             # <<<<<<<<<<<<<<
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_mul(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scal, int32_t __pyx_v_nr) {

  /* "thinc/backends/linalg.pxd":85
 *     cdef inline void mul(weight_t* output, const weight_t* vec, weight_t scal,
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)             # <<<<<<<<<<<<<<
 *         Vec.mul_i(output, scal, nr)
 * 
 */
  (void)(memcpy(__pyx_v_output, __pyx_v_vec, ((sizeof((__pyx_v_output[0]))) * __pyx_v_nr)));

  /* "thinc/backends/linalg.pxd":86
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 *         Vec.mul_i(output, scal, nr)             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_f_5thinc_8backends_6linalg_3Vec_mul_i(__pyx_v_output, __pyx_v_scal, __pyx_v_nr);

  /* "thinc/backends/linalg.pxd":83
 * 
 *     @staticmethod
 *     cdef inline void mul(weight_t* output, const weight_t* vec, weight_t scal,             # <<<<<<<<<<<<<<
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":89
 * 
 *     @staticmethod
 *     cdef inline void mul_i(weight_t* vec, weight_t scal, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         IF USE_BLAS:
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_mul_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scal, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;

  /* "thinc/backends/linalg.pxd":94
 *             blis.cy.scalv(BLIS_NO_CONJUGATE, nr, scal, vec, 1)
 *         ELSE:
 *             for i in range(nr):             # <<<<<<<<<<<<<<
 *                 vec[i] *= scal
 * 
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":95
 *         ELSE:
 *             for i in range(nr):
 *                 vec[i] *= scal             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    __pyx_t_4 = __pyx_v_i;
    (__pyx_v_vec[__pyx_t_4]) = ((__pyx_v_vec[__pyx_t_4]) * __pyx_v_scal);
  }

  /* "thinc/backends/linalg.pxd":89
 * 
 *     @staticmethod
 *     cdef inline void mul_i(weight_t* vec, weight_t scal, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         IF USE_BLAS:
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":98
 * 
 *     @staticmethod
 *     cdef inline void pow(weight_t* output, const weight_t* vec, weight_t scal,             # <<<<<<<<<<<<<<
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_pow(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scal, int32_t __pyx_v_nr) {

  /* "thinc/backends/linalg.pxd":100
 *     cdef inline void pow(weight_t* output, const weight_t* vec, weight_t scal,
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)             # <<<<<<<<<<<<<<
 *         Vec.pow_i(output, scal, nr)
 * 
 */
  (void)(memcpy(__pyx_v_output, __pyx_v_vec, ((sizeof((__pyx_v_output[0]))) * __pyx_v_nr)));

  /* "thinc/backends/linalg.pxd":101
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 *         Vec.pow_i(output, scal, nr)             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_f_5thinc_8backends_6linalg_3Vec_pow_i(__pyx_v_output, __pyx_v_scal, __pyx_v_nr);

  /* "thinc/backends/linalg.pxd":98
 * 
 *     @staticmethod
 *     cdef inline void pow(weight_t* output, const weight_t* vec, weight_t scal,             # <<<<<<<<<<<<<<
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":104
 * 
 *     @staticmethod
 *     cdef inline void pow_i(weight_t* vec, const weight_t scal, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_pow_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t const __pyx_v_scal, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;

  /* "thinc/backends/linalg.pxd":106
 *     cdef inline void pow_i(weight_t* vec, const weight_t scal, int32_t nr) nogil:
 *         cdef int i
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             vec[i] **= scal
 * 
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":107
 *         cdef int i
 *         for i in range(nr):
 *             vec[i] **= scal             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    __pyx_t_4 = __pyx_v_i;
    (__pyx_v_vec[__pyx_t_4]) = powf((__pyx_v_vec[__pyx_t_4]), ((__pyx_t_5thinc_8backends_6linalg_weight_t)__pyx_v_scal));
  }

  /* "thinc/backends/linalg.pxd":104
 * 
 *     @staticmethod
 *     cdef inline void pow_i(weight_t* vec, const weight_t scal, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":111
 *     @staticmethod
 *     @cython.cdivision(True)
 *     cdef inline void div(weight_t* output, const weight_t* vec, weight_t scal,             # <<<<<<<<<<<<<<
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_div(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scal, int32_t __pyx_v_nr) {

  /* "thinc/backends/linalg.pxd":113
 *     cdef inline void div(weight_t* output, const weight_t* vec, weight_t scal,
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)             # <<<<<<<<<<<<<<
 *         Vec.div_i(output, scal, nr)
 * 
 */
  (void)(memcpy(__pyx_v_output, __pyx_v_vec, ((sizeof((__pyx_v_output[0]))) * __pyx_v_nr)));

  /* "thinc/backends/linalg.pxd":114
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 *         Vec.div_i(output, scal, nr)             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_f_5thinc_8backends_6linalg_3Vec_div_i(__pyx_v_output, __pyx_v_scal, __pyx_v_nr);

  /* "thinc/backends/linalg.pxd":111
 *     @staticmethod
 *     @cython.cdivision(True)
 *     cdef inline void div(weight_t* output, const weight_t* vec, weight_t scal,             # <<<<<<<<<<<<<<
 *             int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":118
 *     @staticmethod
 *     @cython.cdivision(True)
 *     cdef inline void div_i(weight_t* vec, const weight_t scal, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_div_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, __pyx_t_5thinc_8backends_6linalg_weight_t const __pyx_v_scal, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;

  /* "thinc/backends/linalg.pxd":120
 *     cdef inline void div_i(weight_t* vec, const weight_t scal, int32_t nr) nogil:
 *         cdef int i
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             vec[i] /= scal
 * 
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":121
 *         cdef int i
 *         for i in range(nr):
 *             vec[i] /= scal             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    __pyx_t_4 = __pyx_v_i;
    (__pyx_v_vec[__pyx_t_4]) = ((__pyx_v_vec[__pyx_t_4]) / __pyx_v_scal);
  }

  /* "thinc/backends/linalg.pxd":118
 *     @staticmethod
 *     @cython.cdivision(True)
 *     cdef inline void div_i(weight_t* vec, const weight_t scal, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":124
 * 
 *     @staticmethod
 *     cdef inline void exp(weight_t* output, const weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 *         Vec.exp_i(output, nr)
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_exp(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_vec, int32_t __pyx_v_nr) {

  /* "thinc/backends/linalg.pxd":125
 *     @staticmethod
 *     cdef inline void exp(weight_t* output, const weight_t* vec, int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)             # <<<<<<<<<<<<<<
 *         Vec.exp_i(output, nr)
 * 
 */
  (void)(memcpy(__pyx_v_output, __pyx_v_vec, ((sizeof((__pyx_v_output[0]))) * __pyx_v_nr)));

  /* "thinc/backends/linalg.pxd":126
 *     cdef inline void exp(weight_t* output, const weight_t* vec, int32_t nr) nogil:
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 *         Vec.exp_i(output, nr)             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_f_5thinc_8backends_6linalg_3Vec_exp_i(__pyx_v_output, __pyx_v_nr);

  /* "thinc/backends/linalg.pxd":124
 * 
 *     @staticmethod
 *     cdef inline void exp(weight_t* output, const weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         memcpy(output, vec, sizeof(output[0]) * nr)
 *         Vec.exp_i(output, nr)
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":129
 * 
 *     @staticmethod
 *     cdef inline void exp_i(weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_exp_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;

  /* "thinc/backends/linalg.pxd":131
 *     cdef inline void exp_i(weight_t* vec, int32_t nr) nogil:
 *         cdef int i
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             vec[i] = exp(vec[i])
 * 
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":132
 *         cdef int i
 *         for i in range(nr):
 *             vec[i] = exp(vec[i])             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    (__pyx_v_vec[__pyx_v_i]) = exp((__pyx_v_vec[__pyx_v_i]));
  }

  /* "thinc/backends/linalg.pxd":129
 * 
 *     @staticmethod
 *     cdef inline void exp_i(weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":135
 * 
 *     @staticmethod
 *     cdef inline void reciprocal_i(weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Vec_reciprocal_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_vec, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;

  /* "thinc/backends/linalg.pxd":137
 *     cdef inline void reciprocal_i(weight_t* vec, int32_t nr) nogil:
 *         cdef int i
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             vec[i] = 1.0 / vec[i]
 * 
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":138
 *         cdef int i
 *         for i in range(nr):
 *             vec[i] = 1.0 / vec[i]             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    (__pyx_v_vec[__pyx_v_i]) = (1.0 / (__pyx_v_vec[__pyx_v_i]));
  }

  /* "thinc/backends/linalg.pxd":135
 * 
 *     @staticmethod
 *     cdef inline void reciprocal_i(weight_t* vec, int32_t nr) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i
 *         for i in range(nr):
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":141
 * 
 *     @staticmethod
 *     cdef inline weight_t mean(const weight_t* X, int32_t nr_dim) nogil:             # <<<<<<<<<<<<<<
 *         cdef weight_t mean = 0.
 *         for x in X[:nr_dim]:
 */

static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_mean(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_X, int32_t __pyx_v_nr_dim) {
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_mean;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_x;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_r;
  __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_t_1;
  __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_t_2;
  __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_t_3;

  /* "thinc/backends/linalg.pxd":142
 *     @staticmethod
 *     cdef inline weight_t mean(const weight_t* X, int32_t nr_dim) nogil:
 *         cdef weight_t mean = 0.             # <<<<<<<<<<<<<<
 *         for x in X[:nr_dim]:
 *             mean += x
 */
  __pyx_v_mean = 0.;

  /* "thinc/backends/linalg.pxd":143
 *     cdef inline weight_t mean(const weight_t* X, int32_t nr_dim) nogil:
 *         cdef weight_t mean = 0.
 *         for x in X[:nr_dim]:             # <<<<<<<<<<<<<<
 *             mean += x
 *         return mean / nr_dim
 */
  __pyx_t_2 = (__pyx_v_X + __pyx_v_nr_dim);
  for (__pyx_t_3 = __pyx_v_X; __pyx_t_3 < __pyx_t_2; __pyx_t_3++) {
    __pyx_t_1 = __pyx_t_3;
    __pyx_v_x = (__pyx_t_1[0]);

    /* "thinc/backends/linalg.pxd":144
 *         cdef weight_t mean = 0.
 *         for x in X[:nr_dim]:
 *             mean += x             # <<<<<<<<<<<<<<
 *         return mean / nr_dim
 * 
 */
    __pyx_v_mean = (__pyx_v_mean + __pyx_v_x);
  }

  /* "thinc/backends/linalg.pxd":145
 *         for x in X[:nr_dim]:
 *             mean += x
 *         return mean / nr_dim             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_r = (__pyx_v_mean / __pyx_v_nr_dim);
  goto __pyx_L0;

  /* "thinc/backends/linalg.pxd":141
 * 
 *     @staticmethod
 *     cdef inline weight_t mean(const weight_t* X, int32_t nr_dim) nogil:             # <<<<<<<<<<<<<<
 *         cdef weight_t mean = 0.
 *         for x in X[:nr_dim]:
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "thinc/backends/linalg.pxd":148
 * 
 *     @staticmethod
 *     cdef inline weight_t variance(const weight_t* X, int32_t nr_dim) nogil:             # <<<<<<<<<<<<<<
 *         # See https://www.johndcook.com/blog/standard_deviation/
 *         cdef double m = X[0]
 */

static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_3Vec_variance(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_X, int32_t __pyx_v_nr_dim) {
  double __pyx_v_m;
  double __pyx_v_v;
  long __pyx_v_i;
  double __pyx_v_diff;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_r;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  long __pyx_t_3;

  /* "thinc/backends/linalg.pxd":150
 *     cdef inline weight_t variance(const weight_t* X, int32_t nr_dim) nogil:
 *         # See https://www.johndcook.com/blog/standard_deviation/
 *         cdef double m = X[0]             # <<<<<<<<<<<<<<
 *         cdef double v = 0.
 *         for i in range(1, nr_dim):
 */
  __pyx_v_m = (__pyx_v_X[0]);

  /* "thinc/backends/linalg.pxd":151
 *         # See https://www.johndcook.com/blog/standard_deviation/
 *         cdef double m = X[0]
 *         cdef double v = 0.             # <<<<<<<<<<<<<<
 *         for i in range(1, nr_dim):
 *             diff = X[i]-m
 */
  __pyx_v_v = 0.;

  /* "thinc/backends/linalg.pxd":152
 *         cdef double m = X[0]
 *         cdef double v = 0.
 *         for i in range(1, nr_dim):             # <<<<<<<<<<<<<<
 *             diff = X[i]-m
 *             m += diff / (i+1)
 */
  __pyx_t_1 = __pyx_v_nr_dim;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 1; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":153
 *         cdef double v = 0.
 *         for i in range(1, nr_dim):
 *             diff = X[i]-m             # <<<<<<<<<<<<<<
 *             m += diff / (i+1)
 *             v += diff * (X[i] - m)
 */
    __pyx_v_diff = ((__pyx_v_X[__pyx_v_i]) - __pyx_v_m);

    /* "thinc/backends/linalg.pxd":154
 *         for i in range(1, nr_dim):
 *             diff = X[i]-m
 *             m += diff / (i+1)             # <<<<<<<<<<<<<<
 *             v += diff * (X[i] - m)
 *         return v / nr_dim
 */
    __pyx_v_m = (__pyx_v_m + (__pyx_v_diff / (__pyx_v_i + 1)));

    /* "thinc/backends/linalg.pxd":155
 *             diff = X[i]-m
 *             m += diff / (i+1)
 *             v += diff * (X[i] - m)             # <<<<<<<<<<<<<<
 *         return v / nr_dim
 * 
 */
    __pyx_v_v = (__pyx_v_v + (__pyx_v_diff * ((__pyx_v_X[__pyx_v_i]) - __pyx_v_m)));
  }

  /* "thinc/backends/linalg.pxd":156
 *             m += diff / (i+1)
 *             v += diff * (X[i] - m)
 *         return v / nr_dim             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_r = (__pyx_v_v / __pyx_v_nr_dim);
  goto __pyx_L0;

  /* "thinc/backends/linalg.pxd":148
 * 
 *     @staticmethod
 *     cdef inline weight_t variance(const weight_t* X, int32_t nr_dim) nogil:             # <<<<<<<<<<<<<<
 *         # See https://www.johndcook.com/blog/standard_deviation/
 *         cdef double m = X[0]
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "thinc/backends/linalg.pxd":161
 * cdef class VecVec:
 *     @staticmethod
 *     cdef inline void add(weight_t* output,             # <<<<<<<<<<<<<<
 *                          const weight_t* x,
 *                          const weight_t* y,
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scale, int32_t __pyx_v_nr) {

  /* "thinc/backends/linalg.pxd":166
 *                          weight_t scale,
 *                          int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)             # <<<<<<<<<<<<<<
 *         VecVec.add_i(output, y, scale, nr)
 * 
 */
  (void)(memcpy(__pyx_v_output, __pyx_v_x, ((sizeof((__pyx_v_output[0]))) * __pyx_v_nr)));

  /* "thinc/backends/linalg.pxd":167
 *                          int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 *         VecVec.add_i(output, y, scale, nr)             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_f_5thinc_8backends_6linalg_6VecVec_add_i(__pyx_v_output, __pyx_v_y, __pyx_v_scale, __pyx_v_nr);

  /* "thinc/backends/linalg.pxd":161
 * cdef class VecVec:
 *     @staticmethod
 *     cdef inline void add(weight_t* output,             # <<<<<<<<<<<<<<
 *                          const weight_t* x,
 *                          const weight_t* y,
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":170
 * 
 *     @staticmethod
 *     cdef inline void add_i(weight_t* x,             # <<<<<<<<<<<<<<
 *                            const weight_t* y,
 *                            weight_t scale,
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scale, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;

  /* "thinc/backends/linalg.pxd":178
 *             blis.cy.axpyv(BLIS_NO_CONJUGATE, nr, scale, y, 1, x, 1)
 *         ELSE:
 *             for i in range(nr):             # <<<<<<<<<<<<<<
 *                 x[i] += y[i] * scale
 * 
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":179
 *         ELSE:
 *             for i in range(nr):
 *                 x[i] += y[i] * scale             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    __pyx_t_4 = __pyx_v_i;
    (__pyx_v_x[__pyx_t_4]) = ((__pyx_v_x[__pyx_t_4]) + ((__pyx_v_y[__pyx_v_i]) * __pyx_v_scale));
  }

  /* "thinc/backends/linalg.pxd":170
 * 
 *     @staticmethod
 *     cdef inline void add_i(weight_t* x,             # <<<<<<<<<<<<<<
 *                            const weight_t* y,
 *                            weight_t scale,
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":182
 * 
 *     @staticmethod
 *     cdef inline void batch_add_i(weight_t* x,             # <<<<<<<<<<<<<<
 *                            const weight_t* y,
 *                            weight_t scale,
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_batch_add_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_scale, int32_t __pyx_v_nr, int32_t __pyx_v_nr_batch) {
  CYTHON_UNUSED int __pyx_v__;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;

  /* "thinc/backends/linalg.pxd":188
 *         # For fixed x, matrix of y
 *         cdef int i, _
 *         for _ in range(nr_batch):             # <<<<<<<<<<<<<<
 *             VecVec.add_i(x,
 *                 y, scale, nr)
 */
  __pyx_t_1 = __pyx_v_nr_batch;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v__ = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":189
 *         cdef int i, _
 *         for _ in range(nr_batch):
 *             VecVec.add_i(x,             # <<<<<<<<<<<<<<
 *                 y, scale, nr)
 *             y += nr
 */
    __pyx_f_5thinc_8backends_6linalg_6VecVec_add_i(__pyx_v_x, __pyx_v_y, __pyx_v_scale, __pyx_v_nr);

    /* "thinc/backends/linalg.pxd":191
 *             VecVec.add_i(x,
 *                 y, scale, nr)
 *             y += nr             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    __pyx_v_y = (__pyx_v_y + __pyx_v_nr);
  }

  /* "thinc/backends/linalg.pxd":182
 * 
 *     @staticmethod
 *     cdef inline void batch_add_i(weight_t* x,             # <<<<<<<<<<<<<<
 *                            const weight_t* y,
 *                            weight_t scale,
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":194
 * 
 *     @staticmethod
 *     cdef inline void add_pow(weight_t* output,             # <<<<<<<<<<<<<<
 *             const weight_t* x, const weight_t* y, weight_t power, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add_pow(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_power, int32_t __pyx_v_nr) {

  /* "thinc/backends/linalg.pxd":196
 *     cdef inline void add_pow(weight_t* output,
 *             const weight_t* x, const weight_t* y, weight_t power, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)             # <<<<<<<<<<<<<<
 *         VecVec.add_pow_i(output, y, power, nr)
 * 
 */
  (void)(memcpy(__pyx_v_output, __pyx_v_x, ((sizeof((__pyx_v_output[0]))) * __pyx_v_nr)));

  /* "thinc/backends/linalg.pxd":197
 *             const weight_t* x, const weight_t* y, weight_t power, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 *         VecVec.add_pow_i(output, y, power, nr)             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_f_5thinc_8backends_6linalg_6VecVec_add_pow_i(__pyx_v_output, __pyx_v_y, __pyx_v_power, __pyx_v_nr);

  /* "thinc/backends/linalg.pxd":194
 * 
 *     @staticmethod
 *     cdef inline void add_pow(weight_t* output,             # <<<<<<<<<<<<<<
 *             const weight_t* x, const weight_t* y, weight_t power, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":201
 * 
 *     @staticmethod
 *     cdef inline void add_pow_i(weight_t* x,             # <<<<<<<<<<<<<<
 *             const weight_t* y, weight_t power, int32_t nr) nogil:
 *         cdef int i
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_add_pow_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_power, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;

  /* "thinc/backends/linalg.pxd":204
 *             const weight_t* y, weight_t power, int32_t nr) nogil:
 *         cdef int i
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             x[i] += y[i] ** power
 * 
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":205
 *         cdef int i
 *         for i in range(nr):
 *             x[i] += y[i] ** power             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    __pyx_t_4 = __pyx_v_i;
    (__pyx_v_x[__pyx_t_4]) = ((__pyx_v_x[__pyx_t_4]) + powf(((__pyx_t_5thinc_8backends_6linalg_weight_t)(__pyx_v_y[__pyx_v_i])), __pyx_v_power));
  }

  /* "thinc/backends/linalg.pxd":201
 * 
 *     @staticmethod
 *     cdef inline void add_pow_i(weight_t* x,             # <<<<<<<<<<<<<<
 *             const weight_t* y, weight_t power, int32_t nr) nogil:
 *         cdef int i
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":208
 * 
 *     @staticmethod
 *     cdef inline void mul(weight_t* output,             # <<<<<<<<<<<<<<
 *             const weight_t* x, const weight_t* y, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_mul(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_output, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, int32_t __pyx_v_nr) {

  /* "thinc/backends/linalg.pxd":210
 *     cdef inline void mul(weight_t* output,
 *             const weight_t* x, const weight_t* y, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)             # <<<<<<<<<<<<<<
 *         VecVec.mul_i(output, y, nr)
 * 
 */
  (void)(memcpy(__pyx_v_output, __pyx_v_x, ((sizeof((__pyx_v_output[0]))) * __pyx_v_nr)));

  /* "thinc/backends/linalg.pxd":211
 *             const weight_t* x, const weight_t* y, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 *         VecVec.mul_i(output, y, nr)             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_f_5thinc_8backends_6linalg_6VecVec_mul_i(__pyx_v_output, __pyx_v_y, __pyx_v_nr);

  /* "thinc/backends/linalg.pxd":208
 * 
 *     @staticmethod
 *     cdef inline void mul(weight_t* output,             # <<<<<<<<<<<<<<
 *             const weight_t* x, const weight_t* y, int32_t nr) nogil:
 *         memcpy(output, x, sizeof(output[0]) * nr)
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":214
 * 
 *     @staticmethod
 *     cdef inline void mul_i(weight_t* x,             # <<<<<<<<<<<<<<
 *             const weight_t* y, int32_t nr) nogil:
 *         cdef int i
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_6VecVec_mul_i(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;

  /* "thinc/backends/linalg.pxd":217
 *             const weight_t* y, int32_t nr) nogil:
 *         cdef int i
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             x[i] *= y[i]
 * 
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":218
 *         cdef int i
 *         for i in range(nr):
 *             x[i] *= y[i]             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    __pyx_t_4 = __pyx_v_i;
    (__pyx_v_x[__pyx_t_4]) = ((__pyx_v_x[__pyx_t_4]) * (__pyx_v_y[__pyx_v_i]));
  }

  /* "thinc/backends/linalg.pxd":214
 * 
 *     @staticmethod
 *     cdef inline void mul_i(weight_t* x,             # <<<<<<<<<<<<<<
 *             const weight_t* y, int32_t nr) nogil:
 *         cdef int i
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":221
 * 
 *     @staticmethod
 *     cdef inline weight_t dot(             # <<<<<<<<<<<<<<
 *             const weight_t* x, const weight_t* y, int32_t nr) nogil:
 *         cdef int i
 */

static CYTHON_INLINE __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_f_5thinc_8backends_6linalg_6VecVec_dot(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_x, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_y, int32_t __pyx_v_nr) {
  int __pyx_v_i;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_total;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_r;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int __pyx_t_3;

  /* "thinc/backends/linalg.pxd":224
 *             const weight_t* x, const weight_t* y, int32_t nr) nogil:
 *         cdef int i
 *         cdef weight_t total = 0             # <<<<<<<<<<<<<<
 *         for i in range(nr):
 *             total += x[i] * y[i]
 */
  __pyx_v_total = 0.0;

  /* "thinc/backends/linalg.pxd":225
 *         cdef int i
 *         cdef weight_t total = 0
 *         for i in range(nr):             # <<<<<<<<<<<<<<
 *             total += x[i] * y[i]
 *         return total
 */
  __pyx_t_1 = __pyx_v_nr;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":226
 *         cdef weight_t total = 0
 *         for i in range(nr):
 *             total += x[i] * y[i]             # <<<<<<<<<<<<<<
 *         return total
 * 
 */
    __pyx_v_total = (__pyx_v_total + ((__pyx_v_x[__pyx_v_i]) * (__pyx_v_y[__pyx_v_i])));
  }

  /* "thinc/backends/linalg.pxd":227
 *         for i in range(nr):
 *             total += x[i] * y[i]
 *         return total             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_r = __pyx_v_total;
  goto __pyx_L0;

  /* "thinc/backends/linalg.pxd":221
 * 
 *     @staticmethod
 *     cdef inline weight_t dot(             # <<<<<<<<<<<<<<
 *             const weight_t* x, const weight_t* y, int32_t nr) nogil:
 *         cdef int i
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "thinc/backends/linalg.pxd":230
 * 
 *     @staticmethod
 *     cdef inline int arg_max_if_true(             # <<<<<<<<<<<<<<
 *             const weight_t* scores, const int* is_valid, const int n_classes) nogil:
 *         cdef int i
 */

static CYTHON_INLINE int __pyx_f_5thinc_8backends_6linalg_6VecVec_arg_max_if_true(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_scores, int const *__pyx_v_is_valid, int const __pyx_v_n_classes) {
  int __pyx_v_i;
  int __pyx_v_best;
  int __pyx_r;
  int __pyx_t_1;
  int __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;
  int __pyx_t_5;

  /* "thinc/backends/linalg.pxd":233
 *             const weight_t* scores, const int* is_valid, const int n_classes) nogil:
 *         cdef int i
 *         cdef int best = -1             # <<<<<<<<<<<<<<
 *         for i in range(n_classes):
 *             if is_valid[i] and (best == -1 or scores[i] > scores[best]):
 */
  __pyx_v_best = -1;

  /* "thinc/backends/linalg.pxd":234
 *         cdef int i
 *         cdef int best = -1
 *         for i in range(n_classes):             # <<<<<<<<<<<<<<
 *             if is_valid[i] and (best == -1 or scores[i] > scores[best]):
 *                 best = i
 */
  __pyx_t_1 = __pyx_v_n_classes;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":235
 *         cdef int best = -1
 *         for i in range(n_classes):
 *             if is_valid[i] and (best == -1 or scores[i] > scores[best]):             # <<<<<<<<<<<<<<
 *                 best = i
 *         return best
 */
    __pyx_t_5 = ((__pyx_v_is_valid[__pyx_v_i]) != 0);
    if (__pyx_t_5) {
    } else {
      __pyx_t_4 = __pyx_t_5;
      goto __pyx_L6_bool_binop_done;
    }
    __pyx_t_5 = ((__pyx_v_best == -1L) != 0);
    if (!__pyx_t_5) {
    } else {
      __pyx_t_4 = __pyx_t_5;
      goto __pyx_L6_bool_binop_done;
    }
    __pyx_t_5 = (((__pyx_v_scores[__pyx_v_i]) > (__pyx_v_scores[__pyx_v_best])) != 0);
    __pyx_t_4 = __pyx_t_5;
    __pyx_L6_bool_binop_done:;
    if (__pyx_t_4) {

      /* "thinc/backends/linalg.pxd":236
 *         for i in range(n_classes):
 *             if is_valid[i] and (best == -1 or scores[i] > scores[best]):
 *                 best = i             # <<<<<<<<<<<<<<
 *         return best
 * 
 */
      __pyx_v_best = __pyx_v_i;

      /* "thinc/backends/linalg.pxd":235
 *         cdef int best = -1
 *         for i in range(n_classes):
 *             if is_valid[i] and (best == -1 or scores[i] > scores[best]):             # <<<<<<<<<<<<<<
 *                 best = i
 *         return best
 */
    }
  }

  /* "thinc/backends/linalg.pxd":237
 *             if is_valid[i] and (best == -1 or scores[i] > scores[best]):
 *                 best = i
 *         return best             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_r = __pyx_v_best;
  goto __pyx_L0;

  /* "thinc/backends/linalg.pxd":230
 * 
 *     @staticmethod
 *     cdef inline int arg_max_if_true(             # <<<<<<<<<<<<<<
 *             const weight_t* scores, const int* is_valid, const int n_classes) nogil:
 *         cdef int i
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "thinc/backends/linalg.pxd":240
 * 
 *     @staticmethod
 *     cdef inline int arg_max_if_zero(             # <<<<<<<<<<<<<<
 *             const weight_t* scores, const weight_t* costs, const int n_classes) nogil:
 *         cdef int i
 */

static CYTHON_INLINE int __pyx_f_5thinc_8backends_6linalg_6VecVec_arg_max_if_zero(__pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_scores, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_costs, int const __pyx_v_n_classes) {
  int __pyx_v_i;
  int __pyx_v_best;
  int __pyx_r;
  int __pyx_t_1;
  int __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;
  int __pyx_t_5;

  /* "thinc/backends/linalg.pxd":243
 *             const weight_t* scores, const weight_t* costs, const int n_classes) nogil:
 *         cdef int i
 *         cdef int best = -1             # <<<<<<<<<<<<<<
 *         for i in range(n_classes):
 *             if costs[i] == 0 and (best == -1 or scores[i] > scores[best]):
 */
  __pyx_v_best = -1;

  /* "thinc/backends/linalg.pxd":244
 *         cdef int i
 *         cdef int best = -1
 *         for i in range(n_classes):             # <<<<<<<<<<<<<<
 *             if costs[i] == 0 and (best == -1 or scores[i] > scores[best]):
 *                 best = i
 */
  __pyx_t_1 = __pyx_v_n_classes;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":245
 *         cdef int best = -1
 *         for i in range(n_classes):
 *             if costs[i] == 0 and (best == -1 or scores[i] > scores[best]):             # <<<<<<<<<<<<<<
 *                 best = i
 *         return best
 */
    __pyx_t_5 = (((__pyx_v_costs[__pyx_v_i]) == 0.0) != 0);
    if (__pyx_t_5) {
    } else {
      __pyx_t_4 = __pyx_t_5;
      goto __pyx_L6_bool_binop_done;
    }
    __pyx_t_5 = ((__pyx_v_best == -1L) != 0);
    if (!__pyx_t_5) {
    } else {
      __pyx_t_4 = __pyx_t_5;
      goto __pyx_L6_bool_binop_done;
    }
    __pyx_t_5 = (((__pyx_v_scores[__pyx_v_i]) > (__pyx_v_scores[__pyx_v_best])) != 0);
    __pyx_t_4 = __pyx_t_5;
    __pyx_L6_bool_binop_done:;
    if (__pyx_t_4) {

      /* "thinc/backends/linalg.pxd":246
 *         for i in range(n_classes):
 *             if costs[i] == 0 and (best == -1 or scores[i] > scores[best]):
 *                 best = i             # <<<<<<<<<<<<<<
 *         return best
 * 
 */
      __pyx_v_best = __pyx_v_i;

      /* "thinc/backends/linalg.pxd":245
 *         cdef int best = -1
 *         for i in range(n_classes):
 *             if costs[i] == 0 and (best == -1 or scores[i] > scores[best]):             # <<<<<<<<<<<<<<
 *                 best = i
 *         return best
 */
    }
  }

  /* "thinc/backends/linalg.pxd":247
 *             if costs[i] == 0 and (best == -1 or scores[i] > scores[best]):
 *                 best = i
 *         return best             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_r = __pyx_v_best;
  goto __pyx_L0;

  /* "thinc/backends/linalg.pxd":240
 * 
 *     @staticmethod
 *     cdef inline int arg_max_if_zero(             # <<<<<<<<<<<<<<
 *             const weight_t* scores, const weight_t* costs, const int n_classes) nogil:
 *         cdef int i
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "thinc/backends/linalg.pxd":252
 * cdef class Mat:
 *     @staticmethod
 *     cdef inline void mean_row(weight_t* Ex,             # <<<<<<<<<<<<<<
 *             const weight_t* mat, int32_t nr_row, int32_t nr_col) nogil:
 *         memset(Ex, 0, sizeof(Ex[0]) * nr_col)
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Mat_mean_row(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_Ex, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_mat, int32_t __pyx_v_nr_row, int32_t __pyx_v_nr_col) {
  int32_t __pyx_v_i;
  int32_t __pyx_t_1;
  int32_t __pyx_t_2;
  int32_t __pyx_t_3;

  /* "thinc/backends/linalg.pxd":254
 *     cdef inline void mean_row(weight_t* Ex,
 *             const weight_t* mat, int32_t nr_row, int32_t nr_col) nogil:
 *         memset(Ex, 0, sizeof(Ex[0]) * nr_col)             # <<<<<<<<<<<<<<
 *         for i in range(nr_row):
 *             VecVec.add_i(Ex, &mat[i * nr_col], 1.0, nr_col)
 */
  (void)(memset(__pyx_v_Ex, 0, ((sizeof((__pyx_v_Ex[0]))) * __pyx_v_nr_col)));

  /* "thinc/backends/linalg.pxd":255
 *             const weight_t* mat, int32_t nr_row, int32_t nr_col) nogil:
 *         memset(Ex, 0, sizeof(Ex[0]) * nr_col)
 *         for i in range(nr_row):             # <<<<<<<<<<<<<<
 *             VecVec.add_i(Ex, &mat[i * nr_col], 1.0, nr_col)
 *         Vec.mul_i(Ex, 1.0 / nr_row, nr_col)
 */
  __pyx_t_1 = __pyx_v_nr_row;
  __pyx_t_2 = __pyx_t_1;
  for (__pyx_t_3 = 0; __pyx_t_3 < __pyx_t_2; __pyx_t_3+=1) {
    __pyx_v_i = __pyx_t_3;

    /* "thinc/backends/linalg.pxd":256
 *         memset(Ex, 0, sizeof(Ex[0]) * nr_col)
 *         for i in range(nr_row):
 *             VecVec.add_i(Ex, &mat[i * nr_col], 1.0, nr_col)             # <<<<<<<<<<<<<<
 *         Vec.mul_i(Ex, 1.0 / nr_row, nr_col)
 * 
 */
    __pyx_f_5thinc_8backends_6linalg_6VecVec_add_i(__pyx_v_Ex, (&(__pyx_v_mat[(__pyx_v_i * __pyx_v_nr_col)])), 1.0, __pyx_v_nr_col);
  }

  /* "thinc/backends/linalg.pxd":257
 *         for i in range(nr_row):
 *             VecVec.add_i(Ex, &mat[i * nr_col], 1.0, nr_col)
 *         Vec.mul_i(Ex, 1.0 / nr_row, nr_col)             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __pyx_f_5thinc_8backends_6linalg_3Vec_mul_i(__pyx_v_Ex, (1.0 / __pyx_v_nr_row), __pyx_v_nr_col);

  /* "thinc/backends/linalg.pxd":252
 * cdef class Mat:
 *     @staticmethod
 *     cdef inline void mean_row(weight_t* Ex,             # <<<<<<<<<<<<<<
 *             const weight_t* mat, int32_t nr_row, int32_t nr_col) nogil:
 *         memset(Ex, 0, sizeof(Ex[0]) * nr_col)
 */

  /* function exit code */
}

/* "thinc/backends/linalg.pxd":260
 * 
 *     @staticmethod
 *     cdef inline void var_row(weight_t* Vx,             # <<<<<<<<<<<<<<
 *             const weight_t* mat, const weight_t* Ex,
 *             int32_t nr_row, int32_t nr_col, weight_t eps) nogil:
 */

static CYTHON_INLINE void __pyx_f_5thinc_8backends_6linalg_3Mat_var_row(__pyx_t_5thinc_8backends_6linalg_weight_t *__pyx_v_Vx, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_mat, __pyx_t_5thinc_8backends_6linalg_weight_t const *__pyx_v_Ex, int32_t __pyx_v_nr_row, int32_t __pyx_v_nr_col, __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_eps) {
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_sum_;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_sum2;
  int32_t __pyx_v_i;
  int32_t __pyx_v_j;
  __pyx_t_5thinc_8backends_6linalg_weight_t __pyx_v_x;
  int __pyx_t_1;
  int __pyx_t_2;
  int32_t __pyx_t_3;
  int32_t __pyx_t_4;
  int32_t __pyx_t_5;
  int32_t __pyx_t_6;
  int32_t __pyx_t_7;
  int32_t __pyx_t_8;

  /* "thinc/backends/linalg.pxd":264
 *             int32_t nr_row, int32_t nr_col, weight_t eps) nogil:
 *         # From https://en.wikipedia.org/wiki/Algorithms_for_calculating_variance
 *         if nr_row == 0 or nr_col == 0:             # <<<<<<<<<<<<<<
 *             return
 *         cdef weight_t sum_, sum2
 */
  __pyx_t_2 = ((__pyx_v_nr_row == 0) != 0);
  if (!__pyx_t_2) {
  } else {
    __pyx_t_1 = __pyx_t_2;
    goto __pyx_L4_bool_binop_done;
  }
  __pyx_t_2 = ((__pyx_v_nr_col == 0) != 0);
  __pyx_t_1 = __pyx_t_2;
  __pyx_L4_bool_binop_done:;
  if (__pyx_t_1) {

    /* "thinc/backends/linalg.pxd":265
 *         # From https://en.wikipedia.org/wiki/Algorithms_for_calculating_variance
 *         if nr_row == 0 or nr_col == 0:
 *             return             # <<<<<<<<<<<<<<
 *         cdef weight_t sum_, sum2
 *         for i in range(nr_col):
 */
    goto __pyx_L0;

    /* "thinc/backends/linalg.pxd":264
 *             int32_t nr_row, int32_t nr_col, weight_t eps) nogil:
 *         # From https://en.wikipedia.org/wiki/Algorithms_for_calculating_variance
 *         if nr_row == 0 or nr_col == 0:             # <<<<<<<<<<<<<<
 *             return
 *         cdef weight_t sum_, sum2
 */
  }

  /* "thinc/backends/linalg.pxd":267
 *             return
 *         cdef weight_t sum_, sum2
 *         for i in range(nr_col):             # <<<<<<<<<<<<<<
 *             sum_ = 0.0
 *             sum2 = 0.0
 */
  __pyx_t_3 = __pyx_v_nr_col;
  __pyx_t_4 = __pyx_t_3;
  for (__pyx_t_5 = 0; __pyx_t_5 < __pyx_t_4; __pyx_t_5+=1) {
    __pyx_v_i = __pyx_t_5;

    /* "thinc/backends/linalg.pxd":268
 *         cdef weight_t sum_, sum2
 *         for i in range(nr_col):
 *             sum_ = 0.0             # <<<<<<<<<<<<<<
 *             sum2 = 0.0
 *             for j in range(nr_row):
 */
    __pyx_v_sum_ = 0.0;

    /* "thinc/backends/linalg.pxd":269
 *         for i in range(nr_col):
 *             sum_ = 0.0
 *             sum2 = 0.0             # <<<<<<<<<<<<<<
 *             for j in range(nr_row):
 *                 x = mat[j * nr_col + i]
 */
    __pyx_v_sum2 = 0.0;

    /* "thinc/backends/linalg.pxd":270
 *             sum_ = 0.0
 *             sum2 = 0.0
 *             for j in range(nr_row):             # <<<<<<<<<<<<<<
 *                 x = mat[j * nr_col + i]
 *                 sum2 += (x - Ex[i]) ** 2
 */
    __pyx_t_6 = __pyx_v_nr_row;
    __pyx_t_7 = __pyx_t_6;
    for (__pyx_t_8 = 0; __pyx_t_8 < __pyx_t_7; __pyx_t_8+=1) {
      __pyx_v_j = __pyx_t_8;

      /* "thinc/backends/linalg.pxd":271
 *             sum2 = 0.0
 *             for j in range(nr_row):
 *                 x = mat[j * nr_col + i]             # <<<<<<<<<<<<<<
 *                 sum2 += (x - Ex[i]) ** 2
 *                 sum_ += x - Ex[i]
 */
      __pyx_v_x = (__pyx_v_mat[((__pyx_v_j * __pyx_v_nr_col) + __pyx_v_i)]);

      /* "thinc/backends/linalg.pxd":272
 *             for j in range(nr_row):
 *                 x = mat[j * nr_col + i]
 *                 sum2 += (x - Ex[i]) ** 2             # <<<<<<<<<<<<<<
 *                 sum_ += x - Ex[i]
 *             Vx[i] = (sum2 - sum_**2 / nr_row) / nr_row
 */
      __pyx_v_sum2 = (__pyx_v_sum2 + powf((__pyx_v_x - (__pyx_v_Ex[__pyx_v_i])), 2.0));

      /* "thinc/backends/linalg.pxd":273
 *                 x = mat[j * nr_col + i]
 *                 sum2 += (x - Ex[i]) ** 2
 *                 sum_ += x - Ex[i]             # <<<<<<<<<<<<<<
 *             Vx[i] = (sum2 - sum_**2 / nr_row) / nr_row
 *             Vx[i] += eps
 */
      __pyx_v_sum_ = (__pyx_v_sum_ + (__pyx_v_x - (__pyx_v_Ex[__pyx_v_i])));
    }

    /* "thinc/backends/linalg.pxd":274
 *                 sum2 += (x - Ex[i]) ** 2
 *                 sum_ += x - Ex[i]
 *             Vx[i] = (sum2 - sum_**2 / nr_row) / nr_row             # <<<<<<<<<<<<<<
 *             Vx[i] += eps
 */
    (__pyx_v_Vx[__pyx_v_i]) = ((__pyx_v_sum2 - (powf(__pyx_v_sum_, 2.0) / __pyx_v_nr_row)) / __pyx_v_nr_row);

    /* "thinc/backends/linalg.pxd":275
 *                 sum_ += x - Ex[i]
 *             Vx[i] = (sum2 - sum_**2 / nr_row) / nr_row
 *             Vx[i] += eps             # <<<<<<<<<<<<<<
 */
    __pyx_t_6 = __pyx_v_i;
    (__pyx_v_Vx[__pyx_t_6]) = ((__pyx_v_Vx[__pyx_t_6]) + __pyx_v_eps);
  }

  /* "thinc/backends/linalg.pxd":260
 * 
 *     @staticmethod
 *     cdef inline void var_row(weight_t* Vx,             # <<<<<<<<<<<<<<
 *             const weight_t* mat, const weight_t* Ex,
 *             int32_t nr_row, int32_t nr_col, weight_t eps) nogil:
 */

  /* function exit code */
  __pyx_L0:;
}

static PyObject *__pyx_tp_new_5thinc_8backends_6linalg_Matrix(PyTypeObject *t, CYTHON_UNUSED PyObject *a, CYTHON_UNUSED PyObject *k) {
  struct __pyx_obj_5thinc_8backends_6linalg_Matrix *p;
  PyObject *o;
  if (likely((t->tp_flags & Py_TPFLAGS_IS_ABSTRACT) == 0)) {
    o = (*t->tp_alloc)(t, 0);
  } else {
    o = (PyObject *) PyBaseObject_Type.tp_new(t, __pyx_empty_tuple, 0);
  }
  if (unlikely(!o)) return 0;
  p = ((struct __pyx_obj_5thinc_8backends_6linalg_Matrix *)o);
  p->mem = ((struct __pyx_obj_5cymem_5cymem_Pool *)Py_None); Py_INCREF(Py_None);
  return o;
}

static void __pyx_tp_dealloc_5thinc_8backends_6linalg_Matrix(PyObject *o) {
  struct __pyx_obj_5thinc_8backends_6linalg_Matrix *p = (struct __pyx_obj_5thinc_8backends_6linalg_Matrix *)o;
  #if CYTHON_USE_TP_FINALIZE
  if (unlikely(PyType_HasFeature(Py_TYPE(o), Py_TPFLAGS_HAVE_FINALIZE) && Py_TYPE(o)->tp_finalize) && !__Pyx_PyObject_GC_IsFinalized(o)) {
    if (PyObject_CallFinalizerFromDealloc(o)) return;
  }
  #endif
  PyObject_GC_UnTrack(o);
  Py_CLEAR(p->mem);
  (*Py_TYPE(o)->tp_free)(o);
}

static int __pyx_tp_traverse_5thinc_8backends_6linalg_Matrix(PyObject *o, visitproc v, void *a) {
  int e;
  struct __pyx_obj_5thinc_8backends_6linalg_Matrix *p = (struct __pyx_obj_5thinc_8backends_6linalg_Matrix *)o;
  if (p->mem) {
    e = (*v)(((PyObject *)p->mem), a); if (e) return e;
  }
  return 0;
}

static int __pyx_tp_clear_5thinc_8backends_6linalg_Matrix(PyObject *o) {
  PyObject* tmp;
  struct __pyx_obj_5thinc_8backends_6linalg_Matrix *p = (struct __pyx_obj_5thinc_8backends_6linalg_Matrix *)o;
  tmp = ((PyObject*)p->mem);
  p->mem = ((struct __pyx_obj_5cymem_5cymem_Pool *)Py_None); Py_INCREF(Py_None);
  Py_XDECREF(tmp);
  return 0;
}

static PyTypeObject __pyx_type_5thinc_8backends_6linalg_Matrix = {
  PyVarObject_HEAD_INIT(0, 0)
  "thinc.backends.linalg.Matrix", /*tp_name*/
  sizeof(struct __pyx_obj_5thinc_8backends_6linalg_Matrix), /*tp_basicsize*/
  0, /*tp_itemsize*/
  __pyx_tp_dealloc_5thinc_8backends_6linalg_Matrix, /*tp_dealloc*/
  #if PY_VERSION_HEX < 0x030800b4
  0, /*tp_print*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b4
  0, /*tp_vectorcall_offset*/
  #endif
  0, /*tp_getattr*/
  0, /*tp_setattr*/
  #if PY_MAJOR_VERSION < 3
  0, /*tp_compare*/
  #endif
  #if PY_MAJOR_VERSION >= 3
  0, /*tp_as_async*/
  #endif
  0, /*tp_repr*/
  0, /*tp_as_number*/
  0, /*tp_as_sequence*/
  0, /*tp_as_mapping*/
  0, /*tp_hash*/
  0, /*tp_call*/
  0, /*tp_str*/
  0, /*tp_getattro*/
  0, /*tp_setattro*/
  0, /*tp_as_buffer*/
  Py_TPFLAGS_DEFAULT|Py_TPFLAGS_HAVE_VERSION_TAG|Py_TPFLAGS_CHECKTYPES|Py_TPFLAGS_HAVE_NEWBUFFER|Py_TPFLAGS_BASETYPE|Py_TPFLAGS_HAVE_GC, /*tp_flags*/
  0, /*tp_doc*/
  __pyx_tp_traverse_5thinc_8backends_6linalg_Matrix, /*tp_traverse*/
  __pyx_tp_clear_5thinc_8backends_6linalg_Matrix, /*tp_clear*/
  0, /*tp_richcompare*/
  0, /*tp_weaklistoffset*/
  0, /*tp_iter*/
  0, /*tp_iternext*/
  0, /*tp_methods*/
  0, /*tp_members*/
  0, /*tp_getset*/
  0, /*tp_base*/
  0, /*tp_dict*/
  0, /*tp_descr_get*/
  0, /*tp_descr_set*/
  0, /*tp_dictoffset*/
  0, /*tp_init*/
  0, /*tp_alloc*/
  __pyx_tp_new_5thinc_8backends_6linalg_Matrix, /*tp_new*/
  0, /*tp_free*/
  0, /*tp_is_gc*/
  0, /*tp_bases*/
  0, /*tp_mro*/
  0, /*tp_cache*/
  0, /*tp_subclasses*/
  0, /*tp_weaklist*/
  0, /*tp_del*/
  0, /*tp_version_tag*/
  #if PY_VERSION_HEX >= 0x030400a1
  0, /*tp_finalize*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b1 && (!CYTHON_COMPILING_IN_PYPY || PYPY_VERSION_NUM >= 0x07030800)
  0, /*tp_vectorcall*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b4 && PY_VERSION_HEX < 0x03090000
  0, /*tp_print*/
  #endif
  #if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX >= 0x03090000 && PY_VERSION_HEX < 0x030a0000
  0, /*tp_pypy_flags*/
  #endif
};
static struct __pyx_vtabstruct_5thinc_8backends_6linalg_Vec __pyx_vtable_5thinc_8backends_6linalg_Vec;

static PyObject *__pyx_tp_new_5thinc_8backends_6linalg_Vec(PyTypeObject *t, CYTHON_UNUSED PyObject *a, CYTHON_UNUSED PyObject *k) {
  struct __pyx_obj_5thinc_8backends_6linalg_Vec *p;
  PyObject *o;
  if (likely((t->tp_flags & Py_TPFLAGS_IS_ABSTRACT) == 0)) {
    o = (*t->tp_alloc)(t, 0);
  } else {
    o = (PyObject *) PyBaseObject_Type.tp_new(t, __pyx_empty_tuple, 0);
  }
  if (unlikely(!o)) return 0;
  p = ((struct __pyx_obj_5thinc_8backends_6linalg_Vec *)o);
  p->__pyx_vtab = __pyx_vtabptr_5thinc_8backends_6linalg_Vec;
  return o;
}

static void __pyx_tp_dealloc_5thinc_8backends_6linalg_Vec(PyObject *o) {
  #if CYTHON_USE_TP_FINALIZE
  if (unlikely(PyType_HasFeature(Py_TYPE(o), Py_TPFLAGS_HAVE_FINALIZE) && Py_TYPE(o)->tp_finalize) && (!PyType_IS_GC(Py_TYPE(o)) || !__Pyx_PyObject_GC_IsFinalized(o))) {
    if (PyObject_CallFinalizerFromDealloc(o)) return;
  }
  #endif
  (*Py_TYPE(o)->tp_free)(o);
}

static PyTypeObject __pyx_type_5thinc_8backends_6linalg_Vec = {
  PyVarObject_HEAD_INIT(0, 0)
  "thinc.backends.linalg.Vec", /*tp_name*/
  sizeof(struct __pyx_obj_5thinc_8backends_6linalg_Vec), /*tp_basicsize*/
  0, /*tp_itemsize*/
  __pyx_tp_dealloc_5thinc_8backends_6linalg_Vec, /*tp_dealloc*/
  #if PY_VERSION_HEX < 0x030800b4
  0, /*tp_print*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b4
  0, /*tp_vectorcall_offset*/
  #endif
  0, /*tp_getattr*/
  0, /*tp_setattr*/
  #if PY_MAJOR_VERSION < 3
  0, /*tp_compare*/
  #endif
  #if PY_MAJOR_VERSION >= 3
  0, /*tp_as_async*/
  #endif
  0, /*tp_repr*/
  0, /*tp_as_number*/
  0, /*tp_as_sequence*/
  0, /*tp_as_mapping*/
  0, /*tp_hash*/
  0, /*tp_call*/
  0, /*tp_str*/
  0, /*tp_getattro*/
  0, /*tp_setattro*/
  0, /*tp_as_buffer*/
  Py_TPFLAGS_DEFAULT|Py_TPFLAGS_HAVE_VERSION_TAG|Py_TPFLAGS_CHECKTYPES|Py_TPFLAGS_HAVE_NEWBUFFER|Py_TPFLAGS_BASETYPE, /*tp_flags*/
  0, /*tp_doc*/
  0, /*tp_traverse*/
  0, /*tp_clear*/
  0, /*tp_richcompare*/
  0, /*tp_weaklistoffset*/
  0, /*tp_iter*/
  0, /*tp_iternext*/
  0, /*tp_methods*/
  0, /*tp_members*/
  0, /*tp_getset*/
  0, /*tp_base*/
  0, /*tp_dict*/
  0, /*tp_descr_get*/
  0, /*tp_descr_set*/
  0, /*tp_dictoffset*/
  0, /*tp_init*/
  0, /*tp_alloc*/
  __pyx_tp_new_5thinc_8backends_6linalg_Vec, /*tp_new*/
  0, /*tp_free*/
  0, /*tp_is_gc*/
  0, /*tp_bases*/
  0, /*tp_mro*/
  0, /*tp_cache*/
  0, /*tp_subclasses*/
  0, /*tp_weaklist*/
  0, /*tp_del*/
  0, /*tp_version_tag*/
  #if PY_VERSION_HEX >= 0x030400a1
  0, /*tp_finalize*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b1 && (!CYTHON_COMPILING_IN_PYPY || PYPY_VERSION_NUM >= 0x07030800)
  0, /*tp_vectorcall*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b4 && PY_VERSION_HEX < 0x03090000
  0, /*tp_print*/
  #endif
  #if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX >= 0x03090000 && PY_VERSION_HEX < 0x030a0000
  0, /*tp_pypy_flags*/
  #endif
};
static struct __pyx_vtabstruct_5thinc_8backends_6linalg_VecVec __pyx_vtable_5thinc_8backends_6linalg_VecVec;

static PyObject *__pyx_tp_new_5thinc_8backends_6linalg_VecVec(PyTypeObject *t, CYTHON_UNUSED PyObject *a, CYTHON_UNUSED PyObject *k) {
  struct __pyx_obj_5thinc_8backends_6linalg_VecVec *p;
  PyObject *o;
  if (likely((t->tp_flags & Py_TPFLAGS_IS_ABSTRACT) == 0)) {
    o = (*t->tp_alloc)(t, 0);
  } else {
    o = (PyObject *) PyBaseObject_Type.tp_new(t, __pyx_empty_tuple, 0);
  }
  if (unlikely(!o)) return 0;
  p = ((struct __pyx_obj_5thinc_8backends_6linalg_VecVec *)o);
  p->__pyx_vtab = __pyx_vtabptr_5thinc_8backends_6linalg_VecVec;
  return o;
}

static void __pyx_tp_dealloc_5thinc_8backends_6linalg_VecVec(PyObject *o) {
  #if CYTHON_USE_TP_FINALIZE
  if (unlikely(PyType_HasFeature(Py_TYPE(o), Py_TPFLAGS_HAVE_FINALIZE) && Py_TYPE(o)->tp_finalize) && (!PyType_IS_GC(Py_TYPE(o)) || !__Pyx_PyObject_GC_IsFinalized(o))) {
    if (PyObject_CallFinalizerFromDealloc(o)) return;
  }
  #endif
  (*Py_TYPE(o)->tp_free)(o);
}

static PyTypeObject __pyx_type_5thinc_8backends_6linalg_VecVec = {
  PyVarObject_HEAD_INIT(0, 0)
  "thinc.backends.linalg.VecVec", /*tp_name*/
  sizeof(struct __pyx_obj_5thinc_8backends_6linalg_VecVec), /*tp_basicsize*/
  0, /*tp_itemsize*/
  __pyx_tp_dealloc_5thinc_8backends_6linalg_VecVec, /*tp_dealloc*/
  #if PY_VERSION_HEX < 0x030800b4
  0, /*tp_print*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b4
  0, /*tp_vectorcall_offset*/
  #endif
  0, /*tp_getattr*/
  0, /*tp_setattr*/
  #if PY_MAJOR_VERSION < 3
  0, /*tp_compare*/
  #endif
  #if PY_MAJOR_VERSION >= 3
  0, /*tp_as_async*/
  #endif
  0, /*tp_repr*/
  0, /*tp_as_number*/
  0, /*tp_as_sequence*/
  0, /*tp_as_mapping*/
  0, /*tp_hash*/
  0, /*tp_call*/
  0, /*tp_str*/
  0, /*tp_getattro*/
  0, /*tp_setattro*/
  0, /*tp_as_buffer*/
  Py_TPFLAGS_DEFAULT|Py_TPFLAGS_HAVE_VERSION_TAG|Py_TPFLAGS_CHECKTYPES|Py_TPFLAGS_HAVE_NEWBUFFER|Py_TPFLAGS_BASETYPE, /*tp_flags*/
  0, /*tp_doc*/
  0, /*tp_traverse*/
  0, /*tp_clear*/
  0, /*tp_richcompare*/
  0, /*tp_weaklistoffset*/
  0, /*tp_iter*/
  0, /*tp_iternext*/
  0, /*tp_methods*/
  0, /*tp_members*/
  0, /*tp_getset*/
  0, /*tp_base*/
  0, /*tp_dict*/
  0, /*tp_descr_get*/
  0, /*tp_descr_set*/
  0, /*tp_dictoffset*/
  0, /*tp_init*/
  0, /*tp_alloc*/
  __pyx_tp_new_5thinc_8backends_6linalg_VecVec, /*tp_new*/
  0, /*tp_free*/
  0, /*tp_is_gc*/
  0, /*tp_bases*/
  0, /*tp_mro*/
  0, /*tp_cache*/
  0, /*tp_subclasses*/
  0, /*tp_weaklist*/
  0, /*tp_del*/
  0, /*tp_version_tag*/
  #if PY_VERSION_HEX >= 0x030400a1
  0, /*tp_finalize*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b1 && (!CYTHON_COMPILING_IN_PYPY || PYPY_VERSION_NUM >= 0x07030800)
  0, /*tp_vectorcall*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b4 && PY_VERSION_HEX < 0x03090000
  0, /*tp_print*/
  #endif
  #if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX >= 0x03090000 && PY_VERSION_HEX < 0x030a0000
  0, /*tp_pypy_flags*/
  #endif
};
static struct __pyx_vtabstruct_5thinc_8backends_6linalg_Mat __pyx_vtable_5thinc_8backends_6linalg_Mat;

static PyObject *__pyx_tp_new_5thinc_8backends_6linalg_Mat(PyTypeObject *t, CYTHON_UNUSED PyObject *a, CYTHON_UNUSED PyObject *k) {
  struct __pyx_obj_5thinc_8backends_6linalg_Mat *p;
  PyObject *o;
  if (likely((t->tp_flags & Py_TPFLAGS_IS_ABSTRACT) == 0)) {
    o = (*t->tp_alloc)(t, 0);
  } else {
    o = (PyObject *) PyBaseObject_Type.tp_new(t, __pyx_empty_tuple, 0);
  }
  if (unlikely(!o)) return 0;
  p = ((struct __pyx_obj_5thinc_8backends_6linalg_Mat *)o);
  p->__pyx_vtab = __pyx_vtabptr_5thinc_8backends_6linalg_Mat;
  return o;
}

static void __pyx_tp_dealloc_5thinc_8backends_6linalg_Mat(PyObject *o) {
  #if CYTHON_USE_TP_FINALIZE
  if (unlikely(PyType_HasFeature(Py_TYPE(o), Py_TPFLAGS_HAVE_FINALIZE) && Py_TYPE(o)->tp_finalize) && (!PyType_IS_GC(Py_TYPE(o)) || !__Pyx_PyObject_GC_IsFinalized(o))) {
    if (PyObject_CallFinalizerFromDealloc(o)) return;
  }
  #endif
  (*Py_TYPE(o)->tp_free)(o);
}

static PyTypeObject __pyx_type_5thinc_8backends_6linalg_Mat = {
  PyVarObject_HEAD_INIT(0, 0)
  "thinc.backends.linalg.Mat", /*tp_name*/
  sizeof(struct __pyx_obj_5thinc_8backends_6linalg_Mat), /*tp_basicsize*/
  0, /*tp_itemsize*/
  __pyx_tp_dealloc_5thinc_8backends_6linalg_Mat, /*tp_dealloc*/
  #if PY_VERSION_HEX < 0x030800b4
  0, /*tp_print*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b4
  0, /*tp_vectorcall_offset*/
  #endif
  0, /*tp_getattr*/
  0, /*tp_setattr*/
  #if PY_MAJOR_VERSION < 3
  0, /*tp_compare*/
  #endif
  #if PY_MAJOR_VERSION >= 3
  0, /*tp_as_async*/
  #endif
  0, /*tp_repr*/
  0, /*tp_as_number*/
  0, /*tp_as_sequence*/
  0, /*tp_as_mapping*/
  0, /*tp_hash*/
  0, /*tp_call*/
  0, /*tp_str*/
  0, /*tp_getattro*/
  0, /*tp_setattro*/
  0, /*tp_as_buffer*/
  Py_TPFLAGS_DEFAULT|Py_TPFLAGS_HAVE_VERSION_TAG|Py_TPFLAGS_CHECKTYPES|Py_TPFLAGS_HAVE_NEWBUFFER|Py_TPFLAGS_BASETYPE, /*tp_flags*/
  0, /*tp_doc*/
  0, /*tp_traverse*/
  0, /*tp_clear*/
  0, /*tp_richcompare*/
  0, /*tp_weaklistoffset*/
  0, /*tp_iter*/
  0, /*tp_iternext*/
  0, /*tp_methods*/
  0, /*tp_members*/
  0, /*tp_getset*/
  0, /*tp_base*/
  0, /*tp_dict*/
  0, /*tp_descr_get*/
  0, /*tp_descr_set*/
  0, /*tp_dictoffset*/
  0, /*tp_init*/
  0, /*tp_alloc*/
  __pyx_tp_new_5thinc_8backends_6linalg_Mat, /*tp_new*/
  0, /*tp_free*/
  0, /*tp_is_gc*/
  0, /*tp_bases*/
  0, /*tp_mro*/
  0, /*tp_cache*/
  0, /*tp_subclasses*/
  0, /*tp_weaklist*/
  0, /*tp_del*/
  0, /*tp_version_tag*/
  #if PY_VERSION_HEX >= 0x030400a1
  0, /*tp_finalize*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b1 && (!CYTHON_COMPILING_IN_PYPY || PYPY_VERSION_NUM >= 0x07030800)
  0, /*tp_vectorcall*/
  #endif
  #if PY_VERSION_HEX >= 0x030800b4 && PY_VERSION_HEX < 0x03090000
  0, /*tp_print*/
  #endif
  #if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX >= 0x03090000 && PY_VERSION_HEX < 0x030a0000
  0, /*tp_pypy_flags*/
  #endif
};

static PyMethodDef __pyx_methods[] = {
  {0, 0, 0, 0}
};

#if PY_MAJOR_VERSION >= 3
#if CYTHON_PEP489_MULTI_PHASE_INIT
static PyObject* __pyx_pymod_create(PyObject *spec, PyModuleDef *def); /*proto*/
static int __pyx_pymod_exec_linalg(PyObject* module); /*proto*/
static PyModuleDef_Slot __pyx_moduledef_slots[] = {
  {Py_mod_create, (void*)__pyx_pymod_create},
  {Py_mod_exec, (void*)__pyx_pymod_exec_linalg},
  {0, NULL}
};
#endif

static struct PyModuleDef __pyx_moduledef = {
    PyModuleDef_HEAD_INIT,
    "linalg",
    0, /* m_doc */
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    0, /* m_size */
  #else
    -1, /* m_size */
  #endif
    __pyx_methods /* m_methods */,
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    __pyx_moduledef_slots, /* m_slots */
  #else
    NULL, /* m_reload */
  #endif
    NULL, /* m_traverse */
    NULL, /* m_clear */
    NULL /* m_free */
};
#endif
#ifndef CYTHON_SMALL_CODE
#if defined(__clang__)
    #define CYTHON_SMALL_CODE
#elif defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 3))
    #define CYTHON_SMALL_CODE __attribute__((cold))
#else
    #define CYTHON_SMALL_CODE
#endif
#endif

static __Pyx_StringTabEntry __pyx_string_tab[] = {
  {&__pyx_n_s_ImportError, __pyx_k_ImportError, sizeof(__pyx_k_ImportError), 0, 0, 1, 1},
  {&__pyx_n_s_Mat, __pyx_k_Mat, sizeof(__pyx_k_Mat), 0, 0, 1, 1},
  {&__pyx_n_s_Matrix, __pyx_k_Matrix, sizeof(__pyx_k_Matrix), 0, 0, 1, 1},
  {&__pyx_n_s_Vec, __pyx_k_Vec, sizeof(__pyx_k_Vec), 0, 0, 1, 1},
  {&__pyx_n_s_VecVec, __pyx_k_VecVec, sizeof(__pyx_k_VecVec), 0, 0, 1, 1},
  {&__pyx_n_s_blis, __pyx_k_blis, sizeof(__pyx_k_blis), 0, 0, 1, 1},
  {&__pyx_n_s_blis_py, __pyx_k_blis_py, sizeof(__pyx_k_blis_py), 0, 0, 1, 1},
  {&__pyx_n_s_cline_in_traceback, __pyx_k_cline_in_traceback, sizeof(__pyx_k_cline_in_traceback), 0, 0, 1, 1},
  {&__pyx_n_s_import, __pyx_k_import, sizeof(__pyx_k_import), 0, 0, 1, 1},
  {&__pyx_n_s_main, __pyx_k_main, sizeof(__pyx_k_main), 0, 0, 1, 1},
  {&__pyx_n_s_name, __pyx_k_name, sizeof(__pyx_k_name), 0, 0, 1, 1},
  {&__pyx_n_s_pyx_vtable, __pyx_k_pyx_vtable, sizeof(__pyx_k_pyx_vtable), 0, 0, 1, 1},
  {&__pyx_n_s_range, __pyx_k_range, sizeof(__pyx_k_range), 0, 0, 1, 1},
  {&__pyx_n_s_test, __pyx_k_test, sizeof(__pyx_k_test), 0, 0, 1, 1},
  {0, 0, 0, 0, 0, 0, 0}
};
static CYTHON_SMALL_CODE int __Pyx_InitCachedBuiltins(void) {
  __pyx_builtin_range = __Pyx_GetBuiltinName(__pyx_n_s_range); if (!__pyx_builtin_range) __PYX_ERR(0, 38, __pyx_L1_error)
  __pyx_builtin_ImportError = __Pyx_GetBuiltinName(__pyx_n_s_ImportError); if (!__pyx_builtin_ImportError) __PYX_ERR(1, 4, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_InitCachedConstants(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_InitCachedConstants", 0);
  __Pyx_RefNannyFinishContext();
  return 0;
}

static CYTHON_SMALL_CODE int __Pyx_InitGlobals(void) {
  if (__Pyx_InitStrings(__pyx_string_tab) < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_modinit_global_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_import_code(void); /*proto*/

static int __Pyx_modinit_global_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_global_init_code", 0);
  /*--- Global init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_export_code", 0);
  /*--- Variable export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_export_code", 0);
  /*--- Function export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_init_code(void) {
  __Pyx_RefNannyDeclarations
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_init_code", 0);
  /*--- Type init code ---*/
  if (PyType_Ready(&__pyx_type_5thinc_8backends_6linalg_Matrix) < 0) __PYX_ERR(0, 23, __pyx_L1_error)
  #if PY_VERSION_HEX < 0x030800B1
  __pyx_type_5thinc_8backends_6linalg_Matrix.tp_print = 0;
  #endif
  if ((CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP) && likely(!__pyx_type_5thinc_8backends_6linalg_Matrix.tp_dictoffset && __pyx_type_5thinc_8backends_6linalg_Matrix.tp_getattro == PyObject_GenericGetAttr)) {
    __pyx_type_5thinc_8backends_6linalg_Matrix.tp_getattro = __Pyx_PyObject_GenericGetAttr;
  }
  if (PyObject_SetAttr(__pyx_m, __pyx_n_s_Matrix, (PyObject *)&__pyx_type_5thinc_8backends_6linalg_Matrix) < 0) __PYX_ERR(0, 23, __pyx_L1_error)
  __pyx_ptype_5thinc_8backends_6linalg_Matrix = &__pyx_type_5thinc_8backends_6linalg_Matrix;
  __pyx_vtabptr_5thinc_8backends_6linalg_Vec = &__pyx_vtable_5thinc_8backends_6linalg_Vec;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.arg_max = (int (*)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int const ))__pyx_f_5thinc_8backends_6linalg_3Vec_arg_max;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.max = (__pyx_t_5thinc_8backends_6linalg_weight_t (*)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_max;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.sum = (__pyx_t_5thinc_8backends_6linalg_weight_t (*)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_sum;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.norm = (__pyx_t_5thinc_8backends_6linalg_weight_t (*)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_norm;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.add = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_add;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.add_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_add_i;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.mul = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_mul;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.mul_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_mul_i;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.pow = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_pow;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.pow_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const , int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_pow_i;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.div = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_div;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.div_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const , int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_div_i;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.exp = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_exp;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.exp_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_exp_i;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.reciprocal_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_reciprocal_i;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.mean = (__pyx_t_5thinc_8backends_6linalg_weight_t (*)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_mean;
  __pyx_vtable_5thinc_8backends_6linalg_Vec.variance = (__pyx_t_5thinc_8backends_6linalg_weight_t (*)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t))__pyx_f_5thinc_8backends_6linalg_3Vec_variance;
  if (PyType_Ready(&__pyx_type_5thinc_8backends_6linalg_Vec) < 0) __PYX_ERR(0, 30, __pyx_L1_error)
  #if PY_VERSION_HEX < 0x030800B1
  __pyx_type_5thinc_8backends_6linalg_Vec.tp_print = 0;
  #endif
  if ((CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP) && likely(!__pyx_type_5thinc_8backends_6linalg_Vec.tp_dictoffset && __pyx_type_5thinc_8backends_6linalg_Vec.tp_getattro == PyObject_GenericGetAttr)) {
    __pyx_type_5thinc_8backends_6linalg_Vec.tp_getattro = __Pyx_PyObject_GenericGetAttr;
  }
  if (__Pyx_SetVtable(__pyx_type_5thinc_8backends_6linalg_Vec.tp_dict, __pyx_vtabptr_5thinc_8backends_6linalg_Vec) < 0) __PYX_ERR(0, 30, __pyx_L1_error)
  if (PyObject_SetAttr(__pyx_m, __pyx_n_s_Vec, (PyObject *)&__pyx_type_5thinc_8backends_6linalg_Vec) < 0) __PYX_ERR(0, 30, __pyx_L1_error)
  __pyx_ptype_5thinc_8backends_6linalg_Vec = &__pyx_type_5thinc_8backends_6linalg_Vec;
  __pyx_vtabptr_5thinc_8backends_6linalg_VecVec = &__pyx_vtable_5thinc_8backends_6linalg_VecVec;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.add = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_6VecVec_add;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.add_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_6VecVec_add_i;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.batch_add_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t, int32_t))__pyx_f_5thinc_8backends_6linalg_6VecVec_batch_add_i;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.add_pow = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_6VecVec_add_pow;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.add_pow_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t, int32_t))__pyx_f_5thinc_8backends_6linalg_6VecVec_add_pow_i;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.mul = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t))__pyx_f_5thinc_8backends_6linalg_6VecVec_mul;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.mul_i = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t))__pyx_f_5thinc_8backends_6linalg_6VecVec_mul_i;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.dot = (__pyx_t_5thinc_8backends_6linalg_weight_t (*)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t))__pyx_f_5thinc_8backends_6linalg_6VecVec_dot;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.arg_max_if_true = (int (*)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, int const *, int const ))__pyx_f_5thinc_8backends_6linalg_6VecVec_arg_max_if_true;
  __pyx_vtable_5thinc_8backends_6linalg_VecVec.arg_max_if_zero = (int (*)(__pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int const ))__pyx_f_5thinc_8backends_6linalg_6VecVec_arg_max_if_zero;
  if (PyType_Ready(&__pyx_type_5thinc_8backends_6linalg_VecVec) < 0) __PYX_ERR(0, 159, __pyx_L1_error)
  #if PY_VERSION_HEX < 0x030800B1
  __pyx_type_5thinc_8backends_6linalg_VecVec.tp_print = 0;
  #endif
  if ((CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP) && likely(!__pyx_type_5thinc_8backends_6linalg_VecVec.tp_dictoffset && __pyx_type_5thinc_8backends_6linalg_VecVec.tp_getattro == PyObject_GenericGetAttr)) {
    __pyx_type_5thinc_8backends_6linalg_VecVec.tp_getattro = __Pyx_PyObject_GenericGetAttr;
  }
  if (__Pyx_SetVtable(__pyx_type_5thinc_8backends_6linalg_VecVec.tp_dict, __pyx_vtabptr_5thinc_8backends_6linalg_VecVec) < 0) __PYX_ERR(0, 159, __pyx_L1_error)
  if (PyObject_SetAttr(__pyx_m, __pyx_n_s_VecVec, (PyObject *)&__pyx_type_5thinc_8backends_6linalg_VecVec) < 0) __PYX_ERR(0, 159, __pyx_L1_error)
  __pyx_ptype_5thinc_8backends_6linalg_VecVec = &__pyx_type_5thinc_8backends_6linalg_VecVec;
  __pyx_vtabptr_5thinc_8backends_6linalg_Mat = &__pyx_vtable_5thinc_8backends_6linalg_Mat;
  __pyx_vtable_5thinc_8backends_6linalg_Mat.mean_row = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t, int32_t))__pyx_f_5thinc_8backends_6linalg_3Mat_mean_row;
  __pyx_vtable_5thinc_8backends_6linalg_Mat.var_row = (void (*)(__pyx_t_5thinc_8backends_6linalg_weight_t *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, __pyx_t_5thinc_8backends_6linalg_weight_t const *, int32_t, int32_t, __pyx_t_5thinc_8backends_6linalg_weight_t))__pyx_f_5thinc_8backends_6linalg_3Mat_var_row;
  if (PyType_Ready(&__pyx_type_5thinc_8backends_6linalg_Mat) < 0) __PYX_ERR(0, 250, __pyx_L1_error)
  #if PY_VERSION_HEX < 0x030800B1
  __pyx_type_5thinc_8backends_6linalg_Mat.tp_print = 0;
  #endif
  if ((CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP) && likely(!__pyx_type_5thinc_8backends_6linalg_Mat.tp_dictoffset && __pyx_type_5thinc_8backends_6linalg_Mat.tp_getattro == PyObject_GenericGetAttr)) {
    __pyx_type_5thinc_8backends_6linalg_Mat.tp_getattro = __Pyx_PyObject_GenericGetAttr;
  }
  if (__Pyx_SetVtable(__pyx_type_5thinc_8backends_6linalg_Mat.tp_dict, __pyx_vtabptr_5thinc_8backends_6linalg_Mat) < 0) __PYX_ERR(0, 250, __pyx_L1_error)
  if (PyObject_SetAttr(__pyx_m, __pyx_n_s_Mat, (PyObject *)&__pyx_type_5thinc_8backends_6linalg_Mat) < 0) __PYX_ERR(0, 250, __pyx_L1_error)
  __pyx_ptype_5thinc_8backends_6linalg_Mat = &__pyx_type_5thinc_8backends_6linalg_Mat;
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_RefNannyFinishContext();
  return -1;
}

static int __Pyx_modinit_type_import_code(void) {
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_import_code", 0);
  /*--- Type import code ---*/
  __pyx_t_1 = PyImport_ImportModule("cymem.cymem"); if (unlikely(!__pyx_t_1)) __PYX_ERR(2, 4, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_ptype_5cymem_5cymem_PyMalloc = __Pyx_ImportType_0_29_37(__pyx_t_1, "cymem.cymem", "PyMalloc", sizeof(struct __pyx_obj_5cymem_5cymem_PyMalloc), __PYX_GET_STRUCT_ALIGNMENT_0_29_37(struct __pyx_obj_5cymem_5cymem_PyMalloc),__Pyx_ImportType_CheckSize_Warn_0_29_37); if (!__pyx_ptype_5cymem_5cymem_PyMalloc) __PYX_ERR(2, 4, __pyx_L1_error)
  __pyx_vtabptr_5cymem_5cymem_PyMalloc = (struct __pyx_vtabstruct_5cymem_5cymem_PyMalloc*)__Pyx_GetVtable(__pyx_ptype_5cymem_5cymem_PyMalloc->tp_dict); if (unlikely(!__pyx_vtabptr_5cymem_5cymem_PyMalloc)) __PYX_ERR(2, 4, __pyx_L1_error)
  __pyx_ptype_5cymem_5cymem_PyFree = __Pyx_ImportType_0_29_37(__pyx_t_1, "cymem.cymem", "PyFree", sizeof(struct __pyx_obj_5cymem_5cymem_PyFree), __PYX_GET_STRUCT_ALIGNMENT_0_29_37(struct __pyx_obj_5cymem_5cymem_PyFree),__Pyx_ImportType_CheckSize_Warn_0_29_37); if (!__pyx_ptype_5cymem_5cymem_PyFree) __PYX_ERR(2, 10, __pyx_L1_error)
  __pyx_vtabptr_5cymem_5cymem_PyFree = (struct __pyx_vtabstruct_5cymem_5cymem_PyFree*)__Pyx_GetVtable(__pyx_ptype_5cymem_5cymem_PyFree->tp_dict); if (unlikely(!__pyx_vtabptr_5cymem_5cymem_PyFree)) __PYX_ERR(2, 10, __pyx_L1_error)
  __pyx_ptype_5cymem_5cymem_Pool = __Pyx_ImportType_0_29_37(__pyx_t_1, "cymem.cymem", "Pool", sizeof(struct __pyx_obj_5cymem_5cymem_Pool), __PYX_GET_STRUCT_ALIGNMENT_0_29_37(struct __pyx_obj_5cymem_5cymem_Pool),__Pyx_ImportType_CheckSize_Warn_0_29_37); if (!__pyx_ptype_5cymem_5cymem_Pool) __PYX_ERR(2, 16, __pyx_L1_error)
  __pyx_vtabptr_5cymem_5cymem_Pool = (struct __pyx_vtabstruct_5cymem_5cymem_Pool*)__Pyx_GetVtable(__pyx_ptype_5cymem_5cymem_Pool->tp_dict); if (unlikely(!__pyx_vtabptr_5cymem_5cymem_Pool)) __PYX_ERR(2, 16, __pyx_L1_error)
  __pyx_ptype_5cymem_5cymem_Address = __Pyx_ImportType_0_29_37(__pyx_t_1, "cymem.cymem", "Address", sizeof(struct __pyx_obj_5cymem_5cymem_Address), __PYX_GET_STRUCT_ALIGNMENT_0_29_37(struct __pyx_obj_5cymem_5cymem_Address),__Pyx_ImportType_CheckSize_Warn_0_29_37); if (!__pyx_ptype_5cymem_5cymem_Address) __PYX_ERR(2, 28, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_RefNannyFinishContext();
  return -1;
}

static int __Pyx_modinit_variable_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_import_code", 0);
  /*--- Variable import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_import_code", 0);
  /*--- Function import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}


#ifndef CYTHON_NO_PYINIT_EXPORT
#define __Pyx_PyMODINIT_FUNC PyMODINIT_FUNC
#elif PY_MAJOR_VERSION < 3
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" void
#else
#define __Pyx_PyMODINIT_FUNC void
#endif
#else
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" PyObject *
#else
#define __Pyx_PyMODINIT_FUNC PyObject *
#endif
#endif


#if PY_MAJOR_VERSION < 3
__Pyx_PyMODINIT_FUNC initlinalg(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC initlinalg(void)
#else
__Pyx_PyMODINIT_FUNC PyInit_linalg(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC PyInit_linalg(void)
#if CYTHON_PEP489_MULTI_PHASE_INIT
{
  return PyModuleDef_Init(&__pyx_moduledef);
}
static CYTHON_SMALL_CODE int __Pyx_check_single_interpreter(void) {
    #if PY_VERSION_HEX >= 0x030700A1
    static PY_INT64_T main_interpreter_id = -1;
    PY_INT64_T current_id = PyInterpreterState_GetID(PyThreadState_Get()->interp);
    if (main_interpreter_id == -1) {
        main_interpreter_id = current_id;
        return (unlikely(current_id == -1)) ? -1 : 0;
    } else if (unlikely(main_interpreter_id != current_id))
    #else
    static PyInterpreterState *main_interpreter = NULL;
    PyInterpreterState *current_interpreter = PyThreadState_Get()->interp;
    if (!main_interpreter) {
        main_interpreter = current_interpreter;
    } else if (unlikely(main_interpreter != current_interpreter))
    #endif
    {
        PyErr_SetString(
            PyExc_ImportError,
            "Interpreter change detected - this module can only be loaded into one interpreter per process.");
        return -1;
    }
    return 0;
}
static CYTHON_SMALL_CODE int __Pyx_copy_spec_to_module(PyObject *spec, PyObject *moddict, const char* from_name, const char* to_name, int allow_none) {
    PyObject *value = PyObject_GetAttrString(spec, from_name);
    int result = 0;
    if (likely(value)) {
        if (allow_none || value != Py_None) {
            result = PyDict_SetItemString(moddict, to_name, value);
        }
        Py_DECREF(value);
    } else if (PyErr_ExceptionMatches(PyExc_AttributeError)) {
        PyErr_Clear();
    } else {
        result = -1;
    }
    return result;
}
static CYTHON_SMALL_CODE PyObject* __pyx_pymod_create(PyObject *spec, CYTHON_UNUSED PyModuleDef *def) {
    PyObject *module = NULL, *moddict, *modname;
    if (__Pyx_check_single_interpreter())
        return NULL;
    if (__pyx_m)
        return __Pyx_NewRef(__pyx_m);
    modname = PyObject_GetAttrString(spec, "name");
    if (unlikely(!modname)) goto bad;
    module = PyModule_NewObject(modname);
    Py_DECREF(modname);
    if (unlikely(!module)) goto bad;
    moddict = PyModule_GetDict(module);
    if (unlikely(!moddict)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "loader", "__loader__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "origin", "__file__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "parent", "__package__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "submodule_search_locations", "__path__", 0) < 0)) goto bad;
    return module;
bad:
    Py_XDECREF(module);
    return NULL;
}


static CYTHON_SMALL_CODE int __pyx_pymod_exec_linalg(PyObject *__pyx_pyinit_module)
#endif
#endif
{
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  int __pyx_t_5;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannyDeclarations
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  if (__pyx_m) {
    if (__pyx_m == __pyx_pyinit_module) return 0;
    PyErr_SetString(PyExc_RuntimeError, "Module 'linalg' has already been imported. Re-initialisation is not supported.");
    return -1;
  }
  #elif PY_MAJOR_VERSION >= 3
  if (__pyx_m) return __Pyx_NewRef(__pyx_m);
  #endif
  #if CYTHON_REFNANNY
__Pyx_RefNanny = __Pyx_RefNannyImportAPI("refnanny");
if (!__Pyx_RefNanny) {
  PyErr_Clear();
  __Pyx_RefNanny = __Pyx_RefNannyImportAPI("Cython.Runtime.refnanny");
  if (!__Pyx_RefNanny)
      Py_FatalError("failed to import 'refnanny' module");
}
#endif
  __Pyx_RefNannySetupContext("__Pyx_PyMODINIT_FUNC PyInit_linalg(void)", 0);
  if (__Pyx_check_binary_version() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #ifdef __Pxy_PyFrame_Initialize_Offsets
  __Pxy_PyFrame_Initialize_Offsets();
  #endif
  __pyx_empty_tuple = PyTuple_New(0); if (unlikely(!__pyx_empty_tuple)) __PYX_ERR(1, 1, __pyx_L1_error)
  __pyx_empty_bytes = PyBytes_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_bytes)) __PYX_ERR(1, 1, __pyx_L1_error)
  __pyx_empty_unicode = PyUnicode_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_unicode)) __PYX_ERR(1, 1, __pyx_L1_error)
  #ifdef __Pyx_CyFunction_USED
  if (__pyx_CyFunction_init() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_FusedFunction_USED
  if (__pyx_FusedFunction_init() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Coroutine_USED
  if (__pyx_Coroutine_init() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Generator_USED
  if (__pyx_Generator_init() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_AsyncGen_USED
  if (__pyx_AsyncGen_init() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_StopAsyncIteration_USED
  if (__pyx_StopAsyncIteration_init() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #endif
  /*--- Library function declarations ---*/
  /*--- Threads initialization code ---*/
  #if defined(WITH_THREAD) && PY_VERSION_HEX < 0x030700F0 && defined(__PYX_FORCE_INIT_THREADS) && __PYX_FORCE_INIT_THREADS
  PyEval_InitThreads();
  #endif
  /*--- Module creation code ---*/
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  __pyx_m = __pyx_pyinit_module;
  Py_INCREF(__pyx_m);
  #else
  #if PY_MAJOR_VERSION < 3
  __pyx_m = Py_InitModule4("linalg", __pyx_methods, 0, 0, PYTHON_API_VERSION); Py_XINCREF(__pyx_m);
  #else
  __pyx_m = PyModule_Create(&__pyx_moduledef);
  #endif
  if (unlikely(!__pyx_m)) __PYX_ERR(1, 1, __pyx_L1_error)
  #endif
  __pyx_d = PyModule_GetDict(__pyx_m); if (unlikely(!__pyx_d)) __PYX_ERR(1, 1, __pyx_L1_error)
  Py_INCREF(__pyx_d);
  __pyx_b = PyImport_AddModule(__Pyx_BUILTIN_MODULE_NAME); if (unlikely(!__pyx_b)) __PYX_ERR(1, 1, __pyx_L1_error)
  Py_INCREF(__pyx_b);
  __pyx_cython_runtime = PyImport_AddModule((char *) "cython_runtime"); if (unlikely(!__pyx_cython_runtime)) __PYX_ERR(1, 1, __pyx_L1_error)
  Py_INCREF(__pyx_cython_runtime);
  if (PyObject_SetAttrString(__pyx_m, "__builtins__", __pyx_b) < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  /*--- Initialize various global constants etc. ---*/
  if (__Pyx_InitGlobals() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #if PY_MAJOR_VERSION < 3 && (__PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT)
  if (__Pyx_init_sys_getdefaultencoding_params() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #endif
  if (__pyx_module_is_main_thinc__backends__linalg) {
    if (PyObject_SetAttr(__pyx_m, __pyx_n_s_name, __pyx_n_s_main) < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  }
  #if PY_MAJOR_VERSION >= 3
  {
    PyObject *modules = PyImport_GetModuleDict(); if (unlikely(!modules)) __PYX_ERR(1, 1, __pyx_L1_error)
    if (!PyDict_GetItemString(modules, "thinc.backends.linalg")) {
      if (unlikely(PyDict_SetItemString(modules, "thinc.backends.linalg", __pyx_m) < 0)) __PYX_ERR(1, 1, __pyx_L1_error)
    }
  }
  #endif
  /*--- Builtin init code ---*/
  if (__Pyx_InitCachedBuiltins() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  /*--- Constants init code ---*/
  if (__Pyx_InitCachedConstants() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  /*--- Global type/function init code ---*/
  (void)__Pyx_modinit_global_init_code();
  (void)__Pyx_modinit_variable_export_code();
  (void)__Pyx_modinit_function_export_code();
  if (unlikely(__Pyx_modinit_type_init_code() < 0)) __PYX_ERR(1, 1, __pyx_L1_error)
  if (unlikely(__Pyx_modinit_type_import_code() < 0)) __PYX_ERR(1, 1, __pyx_L1_error)
  (void)__Pyx_modinit_variable_import_code();
  (void)__Pyx_modinit_function_import_code();
  /*--- Execution code ---*/
  #if defined(__Pyx_Generator_USED) || defined(__Pyx_Coroutine_USED)
  if (__Pyx_patch_abc() < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  #endif

  /* "thinc/backends/linalg.pyx":2
 * # cython: profile=False
 * try:             # <<<<<<<<<<<<<<
 *     import blis.py
 * except ImportError:
 */
  {
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ExceptionSave(&__pyx_t_1, &__pyx_t_2, &__pyx_t_3);
    __Pyx_XGOTREF(__pyx_t_1);
    __Pyx_XGOTREF(__pyx_t_2);
    __Pyx_XGOTREF(__pyx_t_3);
    /*try:*/ {

      /* "thinc/backends/linalg.pyx":3
 * # cython: profile=False
 * try:
 *     import blis.py             # <<<<<<<<<<<<<<
 * except ImportError:
 *     pass
 */
      __pyx_t_4 = __Pyx_Import(__pyx_n_s_blis_py, 0, -1); if (unlikely(!__pyx_t_4)) __PYX_ERR(1, 3, __pyx_L2_error)
      __Pyx_GOTREF(__pyx_t_4);
      if (PyDict_SetItem(__pyx_d, __pyx_n_s_blis, __pyx_t_4) < 0) __PYX_ERR(1, 3, __pyx_L2_error)
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;

      /* "thinc/backends/linalg.pyx":2
 * # cython: profile=False
 * try:             # <<<<<<<<<<<<<<
 *     import blis.py
 * except ImportError:
 */
    }
    __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;
    __Pyx_XDECREF(__pyx_t_2); __pyx_t_2 = 0;
    __Pyx_XDECREF(__pyx_t_3); __pyx_t_3 = 0;
    goto __pyx_L7_try_end;
    __pyx_L2_error:;
    __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;

    /* "thinc/backends/linalg.pyx":4
 * try:
 *     import blis.py
 * except ImportError:             # <<<<<<<<<<<<<<
 *     pass
 */
    __pyx_t_5 = __Pyx_PyErr_ExceptionMatches(__pyx_builtin_ImportError);
    if (__pyx_t_5) {
      __Pyx_ErrRestore(0,0,0);
      goto __pyx_L3_exception_handled;
    }
    goto __pyx_L4_except_error;
    __pyx_L4_except_error:;

    /* "thinc/backends/linalg.pyx":2
 * # cython: profile=False
 * try:             # <<<<<<<<<<<<<<
 *     import blis.py
 * except ImportError:
 */
    __Pyx_XGIVEREF(__pyx_t_1);
    __Pyx_XGIVEREF(__pyx_t_2);
    __Pyx_XGIVEREF(__pyx_t_3);
    __Pyx_ExceptionReset(__pyx_t_1, __pyx_t_2, __pyx_t_3);
    goto __pyx_L1_error;
    __pyx_L3_exception_handled:;
    __Pyx_XGIVEREF(__pyx_t_1);
    __Pyx_XGIVEREF(__pyx_t_2);
    __Pyx_XGIVEREF(__pyx_t_3);
    __Pyx_ExceptionReset(__pyx_t_1, __pyx_t_2, __pyx_t_3);
    __pyx_L7_try_end:;
  }

  /* "thinc/backends/linalg.pyx":1
 * # cython: profile=False             # <<<<<<<<<<<<<<
 * try:
 *     import blis.py
 */
  __pyx_t_4 = __Pyx_PyDict_NewPresized(0); if (unlikely(!__pyx_t_4)) __PYX_ERR(1, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_test, __pyx_t_4) < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;

  /* "thinc/backends/linalg.pxd":260
 * 
 *     @staticmethod
 *     cdef inline void var_row(weight_t* Vx,             # <<<<<<<<<<<<<<
 *             const weight_t* mat, const weight_t* Ex,
 *             int32_t nr_row, int32_t nr_col, weight_t eps) nogil:
 */

  /*--- Wrapped vars code ---*/

  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_4);
  if (__pyx_m) {
    if (__pyx_d) {
      __Pyx_AddTraceback("init thinc.backends.linalg", __pyx_clineno, __pyx_lineno, __pyx_filename);
    }
    Py_CLEAR(__pyx_m);
  } else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_ImportError, "init thinc.backends.linalg");
  }
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  return (__pyx_m != NULL) ? 0 : -1;
  #elif PY_MAJOR_VERSION >= 3
  return __pyx_m;
  #else
  return;
  #endif
}

/* --- Runtime support code --- */
/* Refnanny */
#if CYTHON_REFNANNY
static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname) {
    PyObject *m = NULL, *p = NULL;
    void *r = NULL;
    m = PyImport_ImportModule(modname);
    if (!m) goto end;
    p = PyObject_GetAttrString(m, "RefNannyAPI");
    if (!p) goto end;
    r = PyLong_AsVoidPtr(p);
end:
    Py_XDECREF(p);
    Py_XDECREF(m);
    return (__Pyx_RefNannyAPIStruct *)r;
}
#endif

/* PyObjectGetAttrStr */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name) {
    PyTypeObject* tp = Py_TYPE(obj);
    if (likely(tp->tp_getattro))
        return tp->tp_getattro(obj, attr_name);
#if PY_MAJOR_VERSION < 3
    if (likely(tp->tp_getattr))
        return tp->tp_getattr(obj, PyString_AS_STRING(attr_name));
#endif
    return PyObject_GetAttr(obj, attr_name);
}
#endif

/* GetBuiltinName */
static PyObject *__Pyx_GetBuiltinName(PyObject *name) {
    PyObject* result = __Pyx_PyObject_GetAttrStr(__pyx_b, name);
    if (unlikely(!result)) {
        PyErr_Format(PyExc_NameError,
#if PY_MAJOR_VERSION >= 3
            "name '%U' is not defined", name);
#else
            "name '%.200s' is not defined", PyString_AS_STRING(name));
#endif
    }
    return result;
}

/* PyObject_GenericGetAttrNoDict */
#if CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP && PY_VERSION_HEX < 0x03070000
static PyObject *__Pyx_RaiseGenericGetAttributeError(PyTypeObject *tp, PyObject *attr_name) {
    PyErr_Format(PyExc_AttributeError,
#if PY_MAJOR_VERSION >= 3
                 "'%.50s' object has no attribute '%U'",
                 tp->tp_name, attr_name);
#else
                 "'%.50s' object has no attribute '%.400s'",
                 tp->tp_name, PyString_AS_STRING(attr_name));
#endif
    return NULL;
}
static CYTHON_INLINE PyObject* __Pyx_PyObject_GenericGetAttrNoDict(PyObject* obj, PyObject* attr_name) {
    PyObject *descr;
    PyTypeObject *tp = Py_TYPE(obj);
    if (unlikely(!PyString_Check(attr_name))) {
        return PyObject_GenericGetAttr(obj, attr_name);
    }
    assert(!tp->tp_dictoffset);
    descr = _PyType_Lookup(tp, attr_name);
    if (unlikely(!descr)) {
        return __Pyx_RaiseGenericGetAttributeError(tp, attr_name);
    }
    Py_INCREF(descr);
    #if PY_MAJOR_VERSION < 3
    if (likely(PyType_HasFeature(Py_TYPE(descr), Py_TPFLAGS_HAVE_CLASS)))
    #endif
    {
        descrgetfunc f = Py_TYPE(descr)->tp_descr_get;
        if (unlikely(f)) {
            PyObject *res = f(descr, obj, (PyObject *)tp);
            Py_DECREF(descr);
            return res;
        }
    }
    return descr;
}
#endif

/* PyObject_GenericGetAttr */
#if CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP && PY_VERSION_HEX < 0x03070000
static PyObject* __Pyx_PyObject_GenericGetAttr(PyObject* obj, PyObject* attr_name) {
    if (unlikely(Py_TYPE(obj)->tp_dictoffset)) {
        return PyObject_GenericGetAttr(obj, attr_name);
    }
    return __Pyx_PyObject_GenericGetAttrNoDict(obj, attr_name);
}
#endif

/* SetVTable */
static int __Pyx_SetVtable(PyObject *dict, void *vtable) {
#if PY_VERSION_HEX >= 0x02070000
    PyObject *ob = PyCapsule_New(vtable, 0, 0);
#else
    PyObject *ob = PyCObject_FromVoidPtr(vtable, 0);
#endif
    if (!ob)
        goto bad;
    if (PyDict_SetItem(dict, __pyx_n_s_pyx_vtable, ob) < 0)
        goto bad;
    Py_DECREF(ob);
    return 0;
bad:
    Py_XDECREF(ob);
    return -1;
}

/* TypeImport */
#ifndef __PYX_HAVE_RT_ImportType_0_29_37
#define __PYX_HAVE_RT_ImportType_0_29_37
static PyTypeObject *__Pyx_ImportType_0_29_37(PyObject *module, const char *module_name, const char *class_name,
    size_t size, size_t alignment, enum __Pyx_ImportType_CheckSize_0_29_37 check_size)
{
    PyObject *result = 0;
    char warning[200];
    Py_ssize_t basicsize;
    Py_ssize_t itemsize;
#ifdef Py_LIMITED_API
    PyObject *py_basicsize;
    PyObject *py_itemsize;
#endif
    result = PyObject_GetAttrString(module, class_name);
    if (!result)
        goto bad;
    if (!PyType_Check(result)) {
        PyErr_Format(PyExc_TypeError,
            "%.200s.%.200s is not a type object",
            module_name, class_name);
        goto bad;
    }
#ifndef Py_LIMITED_API
    basicsize = ((PyTypeObject *)result)->tp_basicsize;
    itemsize = ((PyTypeObject *)result)->tp_itemsize;
#else
    py_basicsize = PyObject_GetAttrString(result, "__basicsize__");
    if (!py_basicsize)
        goto bad;
    basicsize = PyLong_AsSsize_t(py_basicsize);
    Py_DECREF(py_basicsize);
    py_basicsize = 0;
    if (basicsize == (Py_ssize_t)-1 && PyErr_Occurred())
        goto bad;
    py_itemsize = PyObject_GetAttrString(result, "__itemsize__");
    if (!py_itemsize)
        goto bad;
    itemsize = PyLong_AsSsize_t(py_itemsize);
    Py_DECREF(py_itemsize);
    py_itemsize = 0;
    if (itemsize == (Py_ssize_t)-1 && PyErr_Occurred())
        goto bad;
#endif
    if (itemsize) {
        if (size % alignment) {
            alignment = size % alignment;
        }
        if (itemsize < (Py_ssize_t)alignment)
            itemsize = (Py_ssize_t)alignment;
    }
    if ((size_t)(basicsize + itemsize) < size) {
        PyErr_Format(PyExc_ValueError,
            "%.200s.%.200s size changed, may indicate binary incompatibility. "
            "Expected %zd from C header, got %zd from PyObject",
            module_name, class_name, size, basicsize);
        goto bad;
    }
    if (check_size == __Pyx_ImportType_CheckSize_Error_0_29_37 && (size_t)basicsize != size) {
        PyErr_Format(PyExc_ValueError,
            "%.200s.%.200s size changed, may indicate binary incompatibility. "
            "Expected %zd from C header, got %zd from PyObject",
            module_name, class_name, size, basicsize);
        goto bad;
    }
    else if (check_size == __Pyx_ImportType_CheckSize_Warn_0_29_37 && (size_t)basicsize > size) {
        PyOS_snprintf(warning, sizeof(warning),
            "%s.%s size changed, may indicate binary incompatibility. "
            "Expected %zd from C header, got %zd from PyObject",
            module_name, class_name, size, basicsize);
        if (PyErr_WarnEx(NULL, warning, 0) < 0) goto bad;
    }
    return (PyTypeObject *)result;
bad:
    Py_XDECREF(result);
    return NULL;
}
#endif

/* GetVTable */
static void* __Pyx_GetVtable(PyObject *dict) {
    void* ptr;
    PyObject *ob = PyObject_GetItem(dict, __pyx_n_s_pyx_vtable);
    if (!ob)
        goto bad;
#if PY_VERSION_HEX >= 0x02070000
    ptr = PyCapsule_GetPointer(ob, 0);
#else
    ptr = PyCObject_AsVoidPtr(ob);
#endif
    if (!ptr && !PyErr_Occurred())
        PyErr_SetString(PyExc_RuntimeError, "invalid vtable found for imported type");
    Py_DECREF(ob);
    return ptr;
bad:
    Py_XDECREF(ob);
    return NULL;
}

/* Import */
static PyObject *__Pyx_Import(PyObject *name, PyObject *from_list, int level) {
    PyObject *empty_list = 0;
    PyObject *module = 0;
    PyObject *global_dict = 0;
    PyObject *empty_dict = 0;
    PyObject *list;
    #if PY_MAJOR_VERSION < 3
    PyObject *py_import;
    py_import = __Pyx_PyObject_GetAttrStr(__pyx_b, __pyx_n_s_import);
    if (!py_import)
        goto bad;
    #endif
    if (from_list)
        list = from_list;
    else {
        empty_list = PyList_New(0);
        if (!empty_list)
            goto bad;
        list = empty_list;
    }
    global_dict = PyModule_GetDict(__pyx_m);
    if (!global_dict)
        goto bad;
    empty_dict = PyDict_New();
    if (!empty_dict)
        goto bad;
    {
        #if PY_MAJOR_VERSION >= 3
        if (level == -1) {
            if ((1) && (strchr(__Pyx_MODULE_NAME, '.'))) {
                module = PyImport_ImportModuleLevelObject(
                    name, global_dict, empty_dict, list, 1);
                if (!module) {
                    if (!PyErr_ExceptionMatches(PyExc_ImportError))
                        goto bad;
                    PyErr_Clear();
                }
            }
            level = 0;
        }
        #endif
        if (!module) {
            #if PY_MAJOR_VERSION < 3
            PyObject *py_level = PyInt_FromLong(level);
            if (!py_level)
                goto bad;
            module = PyObject_CallFunctionObjArgs(py_import,
                name, global_dict, empty_dict, list, py_level, (PyObject *)NULL);
            Py_DECREF(py_level);
            #else
            module = PyImport_ImportModuleLevelObject(
                name, global_dict, empty_dict, list, level);
            #endif
        }
    }
bad:
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(py_import);
    #endif
    Py_XDECREF(empty_list);
    Py_XDECREF(empty_dict);
    return module;
}

/* GetTopmostException */
#if CYTHON_USE_EXC_INFO_STACK
static _PyErr_StackItem *
__Pyx_PyErr_GetTopmostException(PyThreadState *tstate)
{
    _PyErr_StackItem *exc_info = tstate->exc_info;
    while ((exc_info->exc_type == NULL || exc_info->exc_type == Py_None) &&
           exc_info->previous_item != NULL)
    {
        exc_info = exc_info->previous_item;
    }
    return exc_info;
}
#endif

/* SaveResetException */
#if CYTHON_FAST_THREAD_STATE
static CYTHON_INLINE void __Pyx__ExceptionSave(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {
    #if CYTHON_USE_EXC_INFO_STACK
    _PyErr_StackItem *exc_info = __Pyx_PyErr_GetTopmostException(tstate);
    *type = exc_info->exc_type;
    *value = exc_info->exc_value;
    *tb = exc_info->exc_traceback;
    #else
    *type = tstate->exc_type;
    *value = tstate->exc_value;
    *tb = tstate->exc_traceback;
    #endif
    Py_XINCREF(*type);
    Py_XINCREF(*value);
    Py_XINCREF(*tb);
}
static CYTHON_INLINE void __Pyx__ExceptionReset(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    #if CYTHON_USE_EXC_INFO_STACK
    _PyErr_StackItem *exc_info = tstate->exc_info;
    tmp_type = exc_info->exc_type;
    tmp_value = exc_info->exc_value;
    tmp_tb = exc_info->exc_traceback;
    exc_info->exc_type = type;
    exc_info->exc_value = value;
    exc_info->exc_traceback = tb;
    #else
    tmp_type = tstate->exc_type;
    tmp_value = tstate->exc_value;
    tmp_tb = tstate->exc_traceback;
    tstate->exc_type = type;
    tstate->exc_value = value;
    tstate->exc_traceback = tb;
    #endif
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
}
#endif

/* PyErrExceptionMatches */
#if CYTHON_FAST_THREAD_STATE
static int __Pyx_PyErr_ExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        if (__Pyx_PyErr_GivenExceptionMatches(exc_type, PyTuple_GET_ITEM(tuple, i))) return 1;
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err) {
    PyObject *exc_type = tstate->curexc_type;
    if (exc_type == err) return 1;
    if (unlikely(!exc_type)) return 0;
    if (unlikely(PyTuple_Check(err)))
        return __Pyx_PyErr_ExceptionMatchesTuple(exc_type, err);
    return __Pyx_PyErr_GivenExceptionMatches(exc_type, err);
}
#endif

/* PyErrFetchRestore */
#if CYTHON_FAST_THREAD_STATE
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    tmp_type = tstate->curexc_type;
    tmp_value = tstate->curexc_value;
    tmp_tb = tstate->curexc_traceback;
    tstate->curexc_type = type;
    tstate->curexc_value = value;
    tstate->curexc_traceback = tb;
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
}
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {
    *type = tstate->curexc_type;
    *value = tstate->curexc_value;
    *tb = tstate->curexc_traceback;
    tstate->curexc_type = 0;
    tstate->curexc_value = 0;
    tstate->curexc_traceback = 0;
}
#endif

/* PyDictVersioning */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    return likely(dict) ? __PYX_GET_DICT_VERSION(dict) : 0;
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj) {
    PyObject **dictptr = NULL;
    Py_ssize_t offset = Py_TYPE(obj)->tp_dictoffset;
    if (offset) {
#if CYTHON_COMPILING_IN_CPYTHON
        dictptr = (likely(offset > 0)) ? (PyObject **) ((char *)obj + offset) : _PyObject_GetDictPtr(obj);
#else
        dictptr = _PyObject_GetDictPtr(obj);
#endif
    }
    return (dictptr && *dictptr) ? __PYX_GET_DICT_VERSION(*dictptr) : 0;
}
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    if (unlikely(!dict) || unlikely(tp_dict_version != __PYX_GET_DICT_VERSION(dict)))
        return 0;
    return obj_dict_version == __Pyx_get_object_dict_version(obj);
}
#endif

/* CLineInTraceback */
#ifndef CYTHON_CLINE_IN_TRACEBACK
static int __Pyx_CLineForTraceback(CYTHON_UNUSED PyThreadState *tstate, int c_line) {
    PyObject *use_cline;
    PyObject *ptype, *pvalue, *ptraceback;
#if CYTHON_COMPILING_IN_CPYTHON
    PyObject **cython_runtime_dict;
#endif
    if (unlikely(!__pyx_cython_runtime)) {
        return c_line;
    }
    __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
#if CYTHON_COMPILING_IN_CPYTHON
    cython_runtime_dict = _PyObject_GetDictPtr(__pyx_cython_runtime);
    if (likely(cython_runtime_dict)) {
        __PYX_PY_DICT_LOOKUP_IF_MODIFIED(
            use_cline, *cython_runtime_dict,
            __Pyx_PyDict_GetItemStr(*cython_runtime_dict, __pyx_n_s_cline_in_traceback))
    } else
#endif
    {
      PyObject *use_cline_obj = __Pyx_PyObject_GetAttrStr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback);
      if (use_cline_obj) {
        use_cline = PyObject_Not(use_cline_obj) ? Py_False : Py_True;
        Py_DECREF(use_cline_obj);
      } else {
        PyErr_Clear();
        use_cline = NULL;
      }
    }
    if (!use_cline) {
        c_line = 0;
        (void) PyObject_SetAttr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback, Py_False);
    }
    else if (use_cline == Py_False || (use_cline != Py_True && PyObject_Not(use_cline) != 0)) {
        c_line = 0;
    }
    __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
    return c_line;
}
#endif

/* CodeObjectCache */
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line) {
    int start = 0, mid = 0, end = count - 1;
    if (end >= 0 && code_line > entries[end].code_line) {
        return count;
    }
    while (start < end) {
        mid = start + (end - start) / 2;
        if (code_line < entries[mid].code_line) {
            end = mid;
        } else if (code_line > entries[mid].code_line) {
             start = mid + 1;
        } else {
            return mid;
        }
    }
    if (code_line <= entries[mid].code_line) {
        return mid;
    } else {
        return mid + 1;
    }
}
static PyCodeObject *__pyx_find_code_object(int code_line) {
    PyCodeObject* code_object;
    int pos;
    if (unlikely(!code_line) || unlikely(!__pyx_code_cache.entries)) {
        return NULL;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if (unlikely(pos >= __pyx_code_cache.count) || unlikely(__pyx_code_cache.entries[pos].code_line != code_line)) {
        return NULL;
    }
    code_object = __pyx_code_cache.entries[pos].code_object;
    Py_INCREF(code_object);
    return code_object;
}
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object) {
    int pos, i;
    __Pyx_CodeObjectCacheEntry* entries = __pyx_code_cache.entries;
    if (unlikely(!code_line)) {
        return;
    }
    if (unlikely(!entries)) {
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Malloc(64*sizeof(__Pyx_CodeObjectCacheEntry));
        if (likely(entries)) {
            __pyx_code_cache.entries = entries;
            __pyx_code_cache.max_count = 64;
            __pyx_code_cache.count = 1;
            entries[0].code_line = code_line;
            entries[0].code_object = code_object;
            Py_INCREF(code_object);
        }
        return;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if ((pos < __pyx_code_cache.count) && unlikely(__pyx_code_cache.entries[pos].code_line == code_line)) {
        PyCodeObject* tmp = entries[pos].code_object;
        entries[pos].code_object = code_object;
        Py_DECREF(tmp);
        return;
    }
    if (__pyx_code_cache.count == __pyx_code_cache.max_count) {
        int new_max = __pyx_code_cache.max_count + 64;
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Realloc(
            __pyx_code_cache.entries, ((size_t)new_max) * sizeof(__Pyx_CodeObjectCacheEntry));
        if (unlikely(!entries)) {
            return;
        }
        __pyx_code_cache.entries = entries;
        __pyx_code_cache.max_count = new_max;
    }
    for (i=__pyx_code_cache.count; i>pos; i--) {
        entries[i] = entries[i-1];
    }
    entries[pos].code_line = code_line;
    entries[pos].code_object = code_object;
    __pyx_code_cache.count++;
    Py_INCREF(code_object);
}

/* AddTraceback */
#include "compile.h"
#include "frameobject.h"
#include "traceback.h"
#if PY_VERSION_HEX >= 0x030b00a6
  #ifndef Py_BUILD_CORE
    #define Py_BUILD_CORE 1
  #endif
  #include "internal/pycore_frame.h"
#endif
static PyCodeObject* __Pyx_CreateCodeObjectForTraceback(
            const char *funcname, int c_line,
            int py_line, const char *filename) {
    PyCodeObject *py_code = NULL;
    PyObject *py_funcname = NULL;
    #if PY_MAJOR_VERSION < 3
    PyObject *py_srcfile = NULL;
    py_srcfile = PyString_FromString(filename);
    if (!py_srcfile) goto bad;
    #endif
    if (c_line) {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        #else
        py_funcname = PyUnicode_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        funcname = PyUnicode_AsUTF8(py_funcname);
        if (!funcname) goto bad;
        #endif
    }
    else {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromString(funcname);
        if (!py_funcname) goto bad;
        #endif
    }
    #if PY_MAJOR_VERSION < 3
    py_code = __Pyx_PyCode_New(
        0,
        0,
        0,
        0,
        0,
        __pyx_empty_bytes, /*PyObject *code,*/
        __pyx_empty_tuple, /*PyObject *consts,*/
        __pyx_empty_tuple, /*PyObject *names,*/
        __pyx_empty_tuple, /*PyObject *varnames,*/
        __pyx_empty_tuple, /*PyObject *freevars,*/
        __pyx_empty_tuple, /*PyObject *cellvars,*/
        py_srcfile,   /*PyObject *filename,*/
        py_funcname,  /*PyObject *name,*/
        py_line,
        __pyx_empty_bytes  /*PyObject *lnotab*/
    );
    Py_DECREF(py_srcfile);
    #else
    py_code = PyCode_NewEmpty(filename, funcname, py_line);
    #endif
    Py_XDECREF(py_funcname);  // XDECREF since it's only set on Py3 if cline
    return py_code;
bad:
    Py_XDECREF(py_funcname);
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(py_srcfile);
    #endif
    return NULL;
}
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename) {
    PyCodeObject *py_code = 0;
    PyFrameObject *py_frame = 0;
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject *ptype, *pvalue, *ptraceback;
    if (c_line) {
        c_line = __Pyx_CLineForTraceback(tstate, c_line);
    }
    py_code = __pyx_find_code_object(c_line ? -c_line : py_line);
    if (!py_code) {
        __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
        py_code = __Pyx_CreateCodeObjectForTraceback(
            funcname, c_line, py_line, filename);
        if (!py_code) {
            /* If the code object creation fails, then we should clear the
               fetched exception references and propagate the new exception */
            Py_XDECREF(ptype);
            Py_XDECREF(pvalue);
            Py_XDECREF(ptraceback);
            goto bad;
        }
        __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
        __pyx_insert_code_object(c_line ? -c_line : py_line, py_code);
    }
    py_frame = PyFrame_New(
        tstate,            /*PyThreadState *tstate,*/
        py_code,           /*PyCodeObject *code,*/
        __pyx_d,    /*PyObject *globals,*/
        0                  /*PyObject *locals*/
    );
    if (!py_frame) goto bad;
    __Pyx_PyFrame_SetLineNumber(py_frame, py_line);
    PyTraceBack_Here(py_frame);
bad:
    Py_XDECREF(py_code);
    Py_XDECREF(py_frame);
}

/* CIntFromPyVerify */
#define __PYX_VERIFY_RETURN_INT(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 0)
#define __PYX_VERIFY_RETURN_INT_EXC(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 1)
#define __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, exc)\
    {\
        func_type value = func_value;\
        if (sizeof(target_type) < sizeof(func_type)) {\
            if (unlikely(value != (func_type) (target_type) value)) {\
                func_type zero = 0;\
                if (exc && unlikely(value == (func_type)-1 && PyErr_Occurred()))\
                    return (target_type) -1;\
                if (is_unsigned && unlikely(value < zero))\
                    goto raise_neg_overflow;\
                else\
                    goto raise_overflow;\
            }\
        }\
        return (target_type) value;\
    }

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_int(int value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const int neg_one = (int) -1, const_zero = (int) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(int) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(int) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(int) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(int) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(int) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(int),
                                     little, !is_unsigned);
    }
}

/* CIntFromPy */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const int neg_one = (int) -1, const_zero = (int) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(int) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(int, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (int) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case  1: __PYX_VERIFY_RETURN_INT(int, digit, digits[0])
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 2 * PyLong_SHIFT) {
                            return (int) (((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 3 * PyLong_SHIFT) {
                            return (int) (((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 4 * PyLong_SHIFT) {
                            return (int) (((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (int) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(int) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case -1: __PYX_VERIFY_RETURN_INT(int, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(int,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(int) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) ((((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) ((((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) ((((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(int) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            int val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (int) -1;
        }
    } else {
        int val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (int) -1;
        val = __Pyx_PyInt_As_int(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to int");
    return (int) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to int");
    return (int) -1;
}

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_int32_t(int32_t value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const int32_t neg_one = (int32_t) -1, const_zero = (int32_t) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(int32_t) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(int32_t) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(int32_t) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(int32_t) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(int32_t) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(int32_t),
                                     little, !is_unsigned);
    }
}

/* CIntFromPy */
static CYTHON_INLINE int32_t __Pyx_PyInt_As_int32_t(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const int32_t neg_one = (int32_t) -1, const_zero = (int32_t) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(int32_t) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(int32_t, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (int32_t) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int32_t) 0;
                case  1: __PYX_VERIFY_RETURN_INT(int32_t, digit, digits[0])
                case 2:
                    if (8 * sizeof(int32_t) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int32_t, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int32_t) >= 2 * PyLong_SHIFT) {
                            return (int32_t) (((((int32_t)digits[1]) << PyLong_SHIFT) | (int32_t)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int32_t) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int32_t, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int32_t) >= 3 * PyLong_SHIFT) {
                            return (int32_t) (((((((int32_t)digits[2]) << PyLong_SHIFT) | (int32_t)digits[1]) << PyLong_SHIFT) | (int32_t)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int32_t) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int32_t, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int32_t) >= 4 * PyLong_SHIFT) {
                            return (int32_t) (((((((((int32_t)digits[3]) << PyLong_SHIFT) | (int32_t)digits[2]) << PyLong_SHIFT) | (int32_t)digits[1]) << PyLong_SHIFT) | (int32_t)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (int32_t) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(int32_t) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int32_t, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int32_t) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int32_t, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int32_t) 0;
                case -1: __PYX_VERIFY_RETURN_INT(int32_t, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(int32_t,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(int32_t) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int32_t, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int32_t) - 1 > 2 * PyLong_SHIFT) {
                            return (int32_t) (((int32_t)-1)*(((((int32_t)digits[1]) << PyLong_SHIFT) | (int32_t)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(int32_t) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int32_t, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int32_t) - 1 > 2 * PyLong_SHIFT) {
                            return (int32_t) ((((((int32_t)digits[1]) << PyLong_SHIFT) | (int32_t)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(int32_t) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int32_t, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int32_t) - 1 > 3 * PyLong_SHIFT) {
                            return (int32_t) (((int32_t)-1)*(((((((int32_t)digits[2]) << PyLong_SHIFT) | (int32_t)digits[1]) << PyLong_SHIFT) | (int32_t)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int32_t) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int32_t, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int32_t) - 1 > 3 * PyLong_SHIFT) {
                            return (int32_t) ((((((((int32_t)digits[2]) << PyLong_SHIFT) | (int32_t)digits[1]) << PyLong_SHIFT) | (int32_t)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(int32_t) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int32_t, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int32_t) - 1 > 4 * PyLong_SHIFT) {
                            return (int32_t) (((int32_t)-1)*(((((((((int32_t)digits[3]) << PyLong_SHIFT) | (int32_t)digits[2]) << PyLong_SHIFT) | (int32_t)digits[1]) << PyLong_SHIFT) | (int32_t)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int32_t) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int32_t, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int32_t) - 1 > 4 * PyLong_SHIFT) {
                            return (int32_t) ((((((((((int32_t)digits[3]) << PyLong_SHIFT) | (int32_t)digits[2]) << PyLong_SHIFT) | (int32_t)digits[1]) << PyLong_SHIFT) | (int32_t)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(int32_t) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int32_t, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int32_t) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int32_t, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            int32_t val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (int32_t) -1;
        }
    } else {
        int32_t val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (int32_t) -1;
        val = __Pyx_PyInt_As_int32_t(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to int32_t");
    return (int32_t) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to int32_t");
    return (int32_t) -1;
}

/* CIntFromPy */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(long) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(long, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (long) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case  1: __PYX_VERIFY_RETURN_INT(long, digit, digits[0])
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 2 * PyLong_SHIFT) {
                            return (long) (((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 3 * PyLong_SHIFT) {
                            return (long) (((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 4 * PyLong_SHIFT) {
                            return (long) (((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (long) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(long) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case -1: __PYX_VERIFY_RETURN_INT(long, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(long,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(long) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) ((((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) ((((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) ((((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(long) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            long val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (long) -1;
        }
    } else {
        long val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (long) -1;
        val = __Pyx_PyInt_As_long(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to long");
    return (long) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to long");
    return (long) -1;
}

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(long) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(long) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(long) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(long),
                                     little, !is_unsigned);
    }
}

/* FastTypeChecks */
#if CYTHON_COMPILING_IN_CPYTHON
static int __Pyx_InBases(PyTypeObject *a, PyTypeObject *b) {
    while (a) {
        a = a->tp_base;
        if (a == b)
            return 1;
    }
    return b == &PyBaseObject_Type;
}
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b) {
    PyObject *mro;
    if (a == b) return 1;
    mro = a->tp_mro;
    if (likely(mro)) {
        Py_ssize_t i, n;
        n = PyTuple_GET_SIZE(mro);
        for (i = 0; i < n; i++) {
            if (PyTuple_GET_ITEM(mro, i) == (PyObject *)b)
                return 1;
        }
        return 0;
    }
    return __Pyx_InBases(a, b);
}
#if PY_MAJOR_VERSION == 2
static int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject* exc_type2) {
    PyObject *exception, *value, *tb;
    int res;
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ErrFetch(&exception, &value, &tb);
    res = exc_type1 ? PyObject_IsSubclass(err, exc_type1) : 0;
    if (unlikely(res == -1)) {
        PyErr_WriteUnraisable(err);
        res = 0;
    }
    if (!res) {
        res = PyObject_IsSubclass(err, exc_type2);
        if (unlikely(res == -1)) {
            PyErr_WriteUnraisable(err);
            res = 0;
        }
    }
    __Pyx_ErrRestore(exception, value, tb);
    return res;
}
#else
static CYTHON_INLINE int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject *exc_type2) {
    int res = exc_type1 ? __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type1) : 0;
    if (!res) {
        res = __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type2);
    }
    return res;
}
#endif
static int __Pyx_PyErr_GivenExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    assert(PyExceptionClass_Check(exc_type));
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        PyObject *t = PyTuple_GET_ITEM(tuple, i);
        #if PY_MAJOR_VERSION < 3
        if (likely(exc_type == t)) return 1;
        #endif
        if (likely(PyExceptionClass_Check(t))) {
            if (__Pyx_inner_PyErr_GivenExceptionMatches2(exc_type, NULL, t)) return 1;
        } else {
        }
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject* exc_type) {
    if (likely(err == exc_type)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        if (likely(PyExceptionClass_Check(exc_type))) {
            return __Pyx_inner_PyErr_GivenExceptionMatches2(err, NULL, exc_type);
        } else if (likely(PyTuple_Check(exc_type))) {
            return __Pyx_PyErr_GivenExceptionMatchesTuple(err, exc_type);
        } else {
        }
    }
    return PyErr_GivenExceptionMatches(err, exc_type);
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *exc_type1, PyObject *exc_type2) {
    assert(PyExceptionClass_Check(exc_type1));
    assert(PyExceptionClass_Check(exc_type2));
    if (likely(err == exc_type1 || err == exc_type2)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        return __Pyx_inner_PyErr_GivenExceptionMatches2(err, exc_type1, exc_type2);
    }
    return (PyErr_GivenExceptionMatches(err, exc_type1) || PyErr_GivenExceptionMatches(err, exc_type2));
}
#endif

/* CheckBinaryVersion */
static int __Pyx_check_binary_version(void) {
    char ctversion[5];
    int same=1, i, found_dot;
    const char* rt_from_call = Py_GetVersion();
    PyOS_snprintf(ctversion, 5, "%d.%d", PY_MAJOR_VERSION, PY_MINOR_VERSION);
    found_dot = 0;
    for (i = 0; i < 4; i++) {
        if (!ctversion[i]) {
            same = (rt_from_call[i] < '0' || rt_from_call[i] > '9');
            break;
        }
        if (rt_from_call[i] != ctversion[i]) {
            same = 0;
            break;
        }
    }
    if (!same) {
        char rtversion[5] = {'\0'};
        char message[200];
        for (i=0; i<4; ++i) {
            if (rt_from_call[i] == '.') {
                if (found_dot) break;
                found_dot = 1;
            } else if (rt_from_call[i] < '0' || rt_from_call[i] > '9') {
                break;
            }
            rtversion[i] = rt_from_call[i];
        }
        PyOS_snprintf(message, sizeof(message),
                      "compiletime version %s of module '%.100s' "
                      "does not match runtime version %s",
                      ctversion, __Pyx_MODULE_NAME, rtversion);
        return PyErr_WarnEx(NULL, message, 1);
    }
    return 0;
}

/* InitStrings */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t) {
    while (t->p) {
        #if PY_MAJOR_VERSION < 3
        if (t->is_unicode) {
            *t->p = PyUnicode_DecodeUTF8(t->s, t->n - 1, NULL);
        } else if (t->intern) {
            *t->p = PyString_InternFromString(t->s);
        } else {
            *t->p = PyString_FromStringAndSize(t->s, t->n - 1);
        }
        #else
        if (t->is_unicode | t->is_str) {
            if (t->intern) {
                *t->p = PyUnicode_InternFromString(t->s);
            } else if (t->encoding) {
                *t->p = PyUnicode_Decode(t->s, t->n - 1, t->encoding, NULL);
            } else {
                *t->p = PyUnicode_FromStringAndSize(t->s, t->n - 1);
            }
        } else {
            *t->p = PyBytes_FromStringAndSize(t->s, t->n - 1);
        }
        #endif
        if (!*t->p)
            return -1;
        if (PyObject_Hash(*t->p) == -1)
            return -1;
        ++t;
    }
    return 0;
}

static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char* c_str) {
    return __Pyx_PyUnicode_FromStringAndSize(c_str, (Py_ssize_t)strlen(c_str));
}
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject* o) {
    Py_ssize_t ignore;
    return __Pyx_PyObject_AsStringAndSize(o, &ignore);
}
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
#if !CYTHON_PEP393_ENABLED
static const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    char* defenc_c;
    PyObject* defenc = _PyUnicode_AsDefaultEncodedString(o, NULL);
    if (!defenc) return NULL;
    defenc_c = PyBytes_AS_STRING(defenc);
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    {
        char* end = defenc_c + PyBytes_GET_SIZE(defenc);
        char* c;
        for (c = defenc_c; c < end; c++) {
            if ((unsigned char) (*c) >= 128) {
                PyUnicode_AsASCIIString(o);
                return NULL;
            }
        }
    }
#endif
    *length = PyBytes_GET_SIZE(defenc);
    return defenc_c;
}
#else
static CYTHON_INLINE const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    if (unlikely(__Pyx_PyUnicode_READY(o) == -1)) return NULL;
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    if (likely(PyUnicode_IS_ASCII(o))) {
        *length = PyUnicode_GET_LENGTH(o);
        return PyUnicode_AsUTF8(o);
    } else {
        PyUnicode_AsASCIIString(o);
        return NULL;
    }
#else
    return PyUnicode_AsUTF8AndSize(o, length);
#endif
}
#endif
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
    if (
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
            __Pyx_sys_getdefaultencoding_not_ascii &&
#endif
            PyUnicode_Check(o)) {
        return __Pyx_PyUnicode_AsStringAndSize(o, length);
    } else
#endif
#if (!CYTHON_COMPILING_IN_PYPY) || (defined(PyByteArray_AS_STRING) && defined(PyByteArray_GET_SIZE))
    if (PyByteArray_Check(o)) {
        *length = PyByteArray_GET_SIZE(o);
        return PyByteArray_AS_STRING(o);
    } else
#endif
    {
        char* result;
        int r = PyBytes_AsStringAndSize(o, &result, length);
        if (unlikely(r < 0)) {
            return NULL;
        } else {
            return result;
        }
    }
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject* x) {
   int is_true = x == Py_True;
   if (is_true | (x == Py_False) | (x == Py_None)) return is_true;
   else return PyObject_IsTrue(x);
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject* x) {
    int retval;
    if (unlikely(!x)) return -1;
    retval = __Pyx_PyObject_IsTrue(x);
    Py_DECREF(x);
    return retval;
}
static PyObject* __Pyx_PyNumber_IntOrLongWrongResultType(PyObject* result, const char* type_name) {
#if PY_MAJOR_VERSION >= 3
    if (PyLong_Check(result)) {
        if (PyErr_WarnFormat(PyExc_DeprecationWarning, 1,
                "__int__ returned non-int (type %.200s).  "
                "The ability to return an instance of a strict subclass of int "
                "is deprecated, and may be removed in a future version of Python.",
                Py_TYPE(result)->tp_name)) {
            Py_DECREF(result);
            return NULL;
        }
        return result;
    }
#endif
    PyErr_Format(PyExc_TypeError,
                 "__%.4s__ returned non-%.4s (type %.200s)",
                 type_name, type_name, Py_TYPE(result)->tp_name);
    Py_DECREF(result);
    return NULL;
}
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x) {
#if CYTHON_USE_TYPE_SLOTS
  PyNumberMethods *m;
#endif
  const char *name = NULL;
  PyObject *res = NULL;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_Check(x) || PyLong_Check(x)))
#else
  if (likely(PyLong_Check(x)))
#endif
    return __Pyx_NewRef(x);
#if CYTHON_USE_TYPE_SLOTS
  m = Py_TYPE(x)->tp_as_number;
  #if PY_MAJOR_VERSION < 3
  if (m && m->nb_int) {
    name = "int";
    res = m->nb_int(x);
  }
  else if (m && m->nb_long) {
    name = "long";
    res = m->nb_long(x);
  }
  #else
  if (likely(m && m->nb_int)) {
    name = "int";
    res = m->nb_int(x);
  }
  #endif
#else
  if (!PyBytes_CheckExact(x) && !PyUnicode_CheckExact(x)) {
    res = PyNumber_Int(x);
  }
#endif
  if (likely(res)) {
#if PY_MAJOR_VERSION < 3
    if (unlikely(!PyInt_Check(res) && !PyLong_Check(res))) {
#else
    if (unlikely(!PyLong_CheckExact(res))) {
#endif
        return __Pyx_PyNumber_IntOrLongWrongResultType(res, name);
    }
  }
  else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_TypeError,
                    "an integer is required");
  }
  return res;
}
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject* b) {
  Py_ssize_t ival;
  PyObject *x;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_CheckExact(b))) {
    if (sizeof(Py_ssize_t) >= sizeof(long))
        return PyInt_AS_LONG(b);
    else
        return PyInt_AsSsize_t(b);
  }
#endif
  if (likely(PyLong_CheckExact(b))) {
    #if CYTHON_USE_PYLONG_INTERNALS
    const digit* digits = ((PyLongObject*)b)->ob_digit;
    const Py_ssize_t size = Py_SIZE(b);
    if (likely(__Pyx_sst_abs(size) <= 1)) {
        ival = likely(size) ? digits[0] : 0;
        if (size == -1) ival = -ival;
        return ival;
    } else {
      switch (size) {
         case 2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
      }
    }
    #endif
    return PyLong_AsSsize_t(b);
  }
  x = PyNumber_Index(b);
  if (!x) return -1;
  ival = PyInt_AsSsize_t(x);
  Py_DECREF(x);
  return ival;
}
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject* o) {
  if (sizeof(Py_hash_t) == sizeof(Py_ssize_t)) {
    return (Py_hash_t) __Pyx_PyIndex_AsSsize_t(o);
#if PY_MAJOR_VERSION < 3
  } else if (likely(PyInt_CheckExact(o))) {
    return PyInt_AS_LONG(o);
#endif
  } else {
    Py_ssize_t ival;
    PyObject *x;
    x = PyNumber_Index(o);
    if (!x) return -1;
    ival = PyInt_AsLong(x);
    Py_DECREF(x);
    return ival;
  }
}
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b) {
  return b ? __Pyx_NewRef(Py_True) : __Pyx_NewRef(Py_False);
}
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t ival) {
    return PyInt_FromSize_t(ival);
}


#endif /* Py_PYTHON_H */
