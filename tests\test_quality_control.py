"""
Tests for Advanced Quality Control System
"""

import pytest
from unittest.mock import Mock, patch

from quizaigen.generators.base import Question
from quizaigen.models.base_model import ModelTier
from quizaigen.quality import (
    DifficultyAssessor, DifficultyLevel, DifficultyAssessment,
    ContentFilter, ContentIssue, ContentFilterResult,
    BloomClassifier, BloomLevel, BloomClassification,
    DuplicateDetector, SimilarityMethod, DuplicateMatch, DuplicateGroup
)


class TestDifficultyAssessor:
    """Test difficulty assessment functionality."""
    
    def test_difficulty_assessor_initialization(self):
        """Test difficulty assessor initialization."""
        assessor = DifficultyAssessor(ModelTier.FREE)
        assert assessor.tier == ModelTier.FREE
        assert isinstance(assessor.difficulty_thresholds, dict)
        assert len(assessor.difficulty_thresholds) == 5  # 5 difficulty levels
    
    def test_assess_simple_question(self):
        """Test assessment of a simple question."""
        assessor = DifficultyAssessor(ModelTier.FREE)
        question = Question(
            question="What is the capital of France?",
            type="mcq",
            options=["Paris", "London", "Berlin", "Madrid"],
            answer="Paris"
        )
        
        assessment = assessor.assess_question(question)
        
        assert isinstance(assessment, DifficultyAssessment)
        assert isinstance(assessment.level, DifficultyLevel)
        assert 0.0 <= assessment.score <= 1.0
        assert 0.0 <= assessment.confidence <= 1.0
        assert isinstance(assessment.factors, dict)
        assert isinstance(assessment.reasoning, str)
    
    def test_assess_complex_question(self):
        """Test assessment of a complex question."""
        assessor = DifficultyAssessor(ModelTier.PREMIUM)
        question = Question(
            question="Analyze the socioeconomic implications of implementing a comprehensive universal basic income system, considering both macroeconomic effects and individual behavioral modifications.",
            type="essay",
            answer="Complex analysis required"
        )
        
        assessment = assessor.assess_question(question)
        
        # Complex question should have higher difficulty
        assert assessment.level in [DifficultyLevel.ADVANCED, DifficultyLevel.EXPERT]
        assert assessment.score > 0.5
    
    def test_batch_assessment(self):
        """Test batch assessment of multiple questions."""
        assessor = DifficultyAssessor(ModelTier.FREE)
        questions = [
            Question("What is 2+2?", "mcq", answer="4", options=["3", "4", "5"]),
            Question("Explain quantum mechanics", "essay", answer="Complex explanation"),
            Question("Define photosynthesis", "short_answer", answer="Process description")
        ]
        
        assessments = assessor.assess_questions_batch(questions)
        
        assert len(assessments) == 3
        assert all(isinstance(a, DifficultyAssessment) for a in assessments)
    
    def test_difficulty_distribution(self):
        """Test difficulty distribution calculation."""
        assessor = DifficultyAssessor(ModelTier.FREE)
        questions = [
            Question("What is 1+1?", "mcq", answer="2", options=["1", "2", "3"]),
            Question("What is 2+2?", "mcq", answer="4", options=["3", "4", "5"]),
            Question("Solve complex equation", "essay", answer="Solution")
        ]
        
        distribution = assessor.get_difficulty_distribution(questions)
        
        assert isinstance(distribution, dict)
        assert all(level in [l.value for l in DifficultyLevel] for level in distribution.keys())
        assert sum(distribution.values()) == len(questions)


class TestContentFilter:
    """Test content filtering functionality."""
    
    def test_content_filter_initialization(self):
        """Test content filter initialization."""
        filter_obj = ContentFilter(ModelTier.FREE)
        assert filter_obj.tier == ModelTier.FREE
        assert isinstance(filter_obj.inappropriate_patterns, dict)
        assert isinstance(filter_obj.severity_weights, dict)
    
    def test_filter_appropriate_question(self):
        """Test filtering of appropriate question."""
        filter_obj = ContentFilter(ModelTier.FREE)
        question = Question(
            question="What is the process of photosynthesis?",
            type="short_answer",
            answer="Process by which plants convert light energy"
        )
        
        result = filter_obj.filter_question(question)
        
        assert isinstance(result, ContentFilterResult)
        assert result.is_appropriate is True
        assert len(result.issues) == 0
        assert result.severity_score == 0.0
    
    def test_filter_inappropriate_question(self):
        """Test filtering of inappropriate question."""
        filter_obj = ContentFilter(ModelTier.FREE)
        question = Question(
            question="This is a stupid question about violence and killing",
            type="mcq",
            options=["A", "B", "C"],
            answer="A"
        )
        
        result = filter_obj.filter_question(question)
        
        assert isinstance(result, ContentFilterResult)
        assert result.is_appropriate is False
        assert len(result.issues) > 0
        assert result.severity_score > 0.0
    
    def test_batch_filtering(self):
        """Test batch filtering of multiple questions."""
        filter_obj = ContentFilter(ModelTier.FREE)
        questions = [
            Question("What is science?", "short_answer", answer="Study of nature"),
            Question("Inappropriate content here", "mcq", answer="A", options=["A", "B"]),
            Question("Normal educational question", "boolean", answer="True")
        ]
        
        results = filter_obj.filter_questions_batch(questions)
        
        assert len(results) == 3
        assert all(isinstance(r, ContentFilterResult) for r in results)
    
    def test_get_appropriate_questions(self):
        """Test getting only appropriate questions."""
        filter_obj = ContentFilter(ModelTier.FREE)
        questions = [
            Question("What is mathematics?", "short_answer", answer="Study of numbers"),
            Question("Educational question", "mcq", answer="A", options=["A", "B"])
        ]
        
        appropriate = filter_obj.get_appropriate_questions(questions)
        
        assert len(appropriate) <= len(questions)
        assert all(hasattr(q, 'metadata') for q in appropriate)


class TestBloomClassifier:
    """Test Bloom's taxonomy classification functionality."""
    
    def test_bloom_classifier_initialization(self):
        """Test Bloom classifier initialization."""
        classifier = BloomClassifier(ModelTier.FREE)
        assert classifier.tier == ModelTier.FREE
        assert isinstance(classifier.bloom_keywords, dict)
        assert len(classifier.bloom_keywords) == 6  # 6 Bloom levels
    
    def test_classify_remember_question(self):
        """Test classification of remember-level question."""
        classifier = BloomClassifier(ModelTier.FREE)
        question = Question(
            question="What is the capital of France?",
            type="mcq",
            options=["Paris", "London", "Berlin"],
            answer="Paris"
        )
        
        classification = classifier.classify_question(question)
        
        assert isinstance(classification, BloomClassification)
        assert isinstance(classification.level, BloomLevel)
        assert 0.0 <= classification.confidence <= 1.0
        assert isinstance(classification.keywords_found, list)
    
    def test_classify_analyze_question(self):
        """Test classification of analyze-level question."""
        classifier = BloomClassifier(ModelTier.PREMIUM)
        question = Question(
            question="Analyze the differences between photosynthesis and cellular respiration",
            type="essay",
            answer="Detailed analysis required"
        )
        
        classification = classifier.classify_question(question)
        
        # Should classify as analyze level due to "analyze" keyword
        assert classification.level == BloomLevel.ANALYZE
        assert "analyze" in classification.keywords_found
    
    def test_batch_classification(self):
        """Test batch classification of multiple questions."""
        classifier = BloomClassifier(ModelTier.FREE)
        questions = [
            Question("Define photosynthesis", "short_answer", answer="Definition"),
            Question("Apply the formula to solve", "mcq", answer="A", options=["A", "B"]),
            Question("Evaluate the effectiveness", "essay", answer="Evaluation")
        ]
        
        classifications = classifier.classify_questions_batch(questions)
        
        assert len(classifications) == 3
        assert all(isinstance(c, BloomClassification) for c in classifications)
    
    def test_bloom_distribution(self):
        """Test Bloom level distribution calculation."""
        classifier = BloomClassifier(ModelTier.FREE)
        questions = [
            Question("What is X?", "mcq", answer="A", options=["A", "B"]),
            Question("Explain Y", "short_answer", answer="Explanation"),
            Question("Apply Z", "essay", answer="Application")
        ]
        
        distribution = classifier.get_bloom_distribution(questions)
        
        assert isinstance(distribution, dict)
        assert all(level in [l.value for l in BloomLevel] for level in distribution.keys())
        assert sum(distribution.values()) == len(questions)


class TestDuplicateDetector:
    """Test duplicate detection functionality."""
    
    def test_duplicate_detector_initialization(self):
        """Test duplicate detector initialization."""
        detector = DuplicateDetector(ModelTier.FREE, similarity_threshold=0.8)
        assert detector.tier == ModelTier.FREE
        assert detector.similarity_threshold == 0.8
        assert isinstance(detector.method_thresholds, dict)
    
    def test_detect_exact_duplicates(self):
        """Test detection of exact duplicate questions."""
        detector = DuplicateDetector(ModelTier.FREE)
        questions = [
            Question("What is 2+2?", "mcq", answer="4", options=["3", "4", "5"]),
            Question("What is 2+2?", "mcq", answer="4", options=["3", "4", "5"]),  # Exact duplicate
            Question("What is 3+3?", "mcq", answer="6", options=["5", "6", "7"])
        ]
        
        duplicates = detector.detect_duplicates(questions)
        
        assert len(duplicates) >= 1  # Should find at least one duplicate pair
        assert all(isinstance(d, DuplicateMatch) for d in duplicates)
    
    def test_detect_fuzzy_duplicates(self):
        """Test detection of fuzzy duplicate questions."""
        detector = DuplicateDetector(ModelTier.FREE, similarity_threshold=0.7)
        questions = [
            Question("What is the capital of France?", "mcq", answer="Paris", options=["Paris", "London"]),
            Question("What's the capital city of France?", "mcq", answer="Paris", options=["Paris", "London"]),
            Question("What is the capital of Germany?", "mcq", answer="Berlin", options=["Berlin", "Munich"])
        ]
        
        duplicates = detector.detect_duplicates(questions)
        
        # Should detect similarity between first two questions
        assert len(duplicates) >= 1
    
    def test_group_duplicates(self):
        """Test grouping of duplicate questions."""
        detector = DuplicateDetector(ModelTier.FREE, similarity_threshold=0.8)
        questions = [
            Question("What is 2+2?", "mcq", answer="4", options=["4"]),
            Question("What is 2+2?", "mcq", answer="4", options=["4"]),  # Duplicate
            Question("What is 3+3?", "mcq", answer="6", options=["6"]),
            Question("What is 3+3?", "mcq", answer="6", options=["6"])   # Duplicate
        ]
        
        groups = detector.group_duplicates(questions)
        
        assert len(groups) >= 1  # Should find at least one group
        assert all(isinstance(g, DuplicateGroup) for g in groups)
        assert all(len(g.question_indices) >= 2 for g in groups)
    
    def test_remove_duplicates(self):
        """Test removal of duplicate questions."""
        detector = DuplicateDetector(ModelTier.FREE)
        questions = [
            Question("What is 2+2?", "mcq", answer="4", options=["4"]),
            Question("What is 2+2?", "mcq", answer="4", options=["4"]),  # Duplicate
            Question("What is 3+3?", "mcq", answer="6", options=["6"])
        ]
        
        unique_questions = detector.remove_duplicates(questions)
        
        assert len(unique_questions) <= len(questions)
        assert all(hasattr(q, 'metadata') for q in unique_questions)
    
    def test_duplicate_statistics(self):
        """Test duplicate detection statistics."""
        detector = DuplicateDetector(ModelTier.FREE)
        questions = [
            Question("Question 1", "mcq", answer="A", options=["A"]),
            Question("Question 1", "mcq", answer="A", options=["A"]),  # Duplicate
            Question("Question 2", "mcq", answer="B", options=["B"])
        ]
        
        stats = detector.get_duplicate_statistics(questions)
        
        assert isinstance(stats, dict)
        assert 'total_questions' in stats
        assert 'duplicate_pairs' in stats
        assert 'duplication_rate' in stats
        assert stats['total_questions'] == len(questions)


def test_quality_control_integration():
    """Test integration of all quality control components."""
    # Test that all components can work together
    questions = [
        Question("What is photosynthesis?", "short_answer", answer="Process description"),
        Question("Analyze the economic impact", "essay", answer="Analysis required"),
        Question("What is photosynthesis?", "short_answer", answer="Process description")  # Duplicate
    ]
    
    # Initialize all components
    difficulty_assessor = DifficultyAssessor(ModelTier.FREE)
    content_filter = ContentFilter(ModelTier.FREE)
    bloom_classifier = BloomClassifier(ModelTier.FREE)
    duplicate_detector = DuplicateDetector(ModelTier.FREE)
    
    # Test that all components can process the same questions
    difficulty_results = difficulty_assessor.assess_questions_batch(questions)
    content_results = content_filter.filter_questions_batch(questions)
    bloom_results = bloom_classifier.classify_questions_batch(questions)
    duplicate_results = duplicate_detector.detect_duplicates(questions)
    
    assert len(difficulty_results) == len(questions)
    assert len(content_results) == len(questions)
    assert len(bloom_results) == len(questions)
    assert isinstance(duplicate_results, list)


if __name__ == "__main__":
    pytest.main([__file__])
