"""
Comprehensive test suite for Advanced Question Generators.

Tests Short Answer, Fill-in-blank, and Paraphrasing generators with:
- Basic functionality
- AI model integration
- Quality control integration
- Edge cases and error handling
"""

import pytest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from quizaigen.generators.short_answer_generator import FAQGenerator
from quizaigen.generators.fill_blank_generator import FillBlankGenerator
from quizaigen.generators.question_paraphraser import QuestionParaphraser
from quizaigen.generators.base import Question
from quizaigen.core.config import Config
from quizaigen.models.base_model import ModelTier


class TestShortAnswerGenerator:
    """Test suite for Short Answer (FAQ) Generator."""
    
    @pytest.fixture
    def generator(self):
        """Create FAQ generator instance."""
        config = Config()
        return FAQGenerator(config)
    
    @pytest.fixture
    def sample_text(self):
        """Sample text for testing."""
        return """
        Python is a high-level programming language created by <PERSON> in 1991.
        It emphasizes code readability and simplicity. Python supports multiple programming
        paradigms including procedural, object-oriented, and functional programming.
        The language is widely used for web development, data science, artificial intelligence,
        and automation. Python's extensive standard library and third-party packages make it
        versatile for various applications.
        """
    
    def test_basic_generation(self, generator, sample_text):
        """Test basic short answer question generation."""
        questions = generator.generate_questions(sample_text, num_questions=3)
        
        assert len(questions) > 0, "Should generate at least one question"
        assert len(questions) <= 3, "Should not exceed requested number"
        
        for question in questions:
            assert isinstance(question, Question), "Should return Question objects"
            assert question.type == 'short_answer', "Should have correct type"
            assert question.question, "Should have question text"
            assert question.answer, "Should have answer text"
            assert 0 <= question.confidence <= 1, "Confidence should be between 0 and 1"
    
    def test_question_strategies(self, generator, sample_text):
        """Test different question generation strategies."""
        questions = generator.generate_questions(sample_text, num_questions=5)

        # Check for variety in question types - but be realistic about what's possible
        question_types = set()
        for question in questions:
            if question.metadata and 'question_type' in question.metadata:
                question_types.add(question.metadata['question_type'])

        # Should have at least one question type identified
        assert len(question_types) >= 1, "Should identify question strategies"
    
    def test_entity_extraction(self, generator, sample_text):
        """Test named entity extraction and usage."""
        questions = generator.generate_questions(sample_text, num_questions=5)
        
        # Check if entities are properly extracted and used
        found_entities = False
        for question in questions:
            if 'Python' in question.question or 'Guido van Rossum' in question.question:
                found_entities = True
                break
        
        assert found_entities, "Should extract and use named entities"
    
    def test_empty_text_handling(self, generator):
        """Test handling of empty or invalid text."""
        # Test empty text - should raise ValidationError
        with pytest.raises(Exception):  # ValidationError
            generator.generate_questions("", num_questions=3)

        # Test very short text - should raise ValidationError
        with pytest.raises(Exception):  # ValidationError
            generator.generate_questions("Short.", num_questions=3)
    
    def test_metadata_inclusion(self, generator, sample_text):
        """Test that questions include proper metadata."""
        questions = generator.generate_questions(sample_text, num_questions=2)
        
        for question in questions:
            assert question.metadata is not None, "Should include metadata"
            assert 'question_type' in question.metadata, "Should include question type"
            assert question.source_text, "Should include source text"


class TestFillBlankGenerator:
    """Test suite for Fill-in-Blank Generator."""
    
    @pytest.fixture
    def generator(self):
        """Create fill-blank generator instance."""
        config = Config()
        return FillBlankGenerator(config)
    
    @pytest.fixture
    def sample_text(self):
        """Sample text for testing."""
        return """
        Machine learning is a subset of artificial intelligence that enables computers
        to learn and make decisions without being explicitly programmed. It uses
        algorithms to analyze data, identify patterns, and make predictions or decisions.
        Common applications include image recognition, natural language processing,
        and recommendation systems. Deep learning is a specialized form of machine
        learning that uses neural networks with multiple layers.
        """
    
    def test_basic_generation(self, generator, sample_text):
        """Test basic fill-in-blank question generation."""
        questions = generator.generate_questions(sample_text, num_questions=3)
        
        assert len(questions) > 0, "Should generate at least one question"
        assert len(questions) <= 3, "Should not exceed requested number"
        
        for question in questions:
            assert isinstance(question, Question), "Should return Question objects"
            assert question.type == 'fill_blank', "Should have correct type"
            assert '______' in question.question, "Should contain blank placeholder"
            assert question.answer, "Should have answer text"
            assert 0 <= question.confidence <= 1, "Confidence should be between 0 and 1"
    
    def test_blank_candidate_selection(self, generator, sample_text):
        """Test intelligent blank candidate selection."""
        questions = generator.generate_questions(sample_text, num_questions=5)
        
        # Check that blanks are meaningful words (not articles, prepositions, etc.)
        for question in questions:
            answer = question.answer.lower()
            # Should not be common stop words
            assert answer not in ['the', 'a', 'an', 'is', 'are', 'and', 'or', 'but']
            # Should be substantial words
            assert len(answer) >= 3, "Answer should be substantial word"
    
    def test_blank_position_metadata(self, generator, sample_text):
        """Test that blank position metadata is included."""
        questions = generator.generate_questions(sample_text, num_questions=2)
        
        for question in questions:
            assert question.metadata is not None, "Should include metadata"
            assert 'blank_position' in question.metadata, "Should include blank position"
            assert 'original_sentence' in question.metadata, "Should include original sentence"
            assert 'difficulty' in question.metadata, "Should include difficulty assessment"
    
    def test_sentence_filtering(self, generator):
        """Test sentence length and quality filtering."""
        # Test with very short sentences
        short_text = "AI is good. ML works. Data helps."
        questions = generator.generate_questions(short_text, num_questions=3)
        assert len(questions) == 0, "Should filter out too short sentences"
        
        # Test with very long sentences
        long_text = "This is an extremely long sentence that goes on and on and on and contains way too many words to be suitable for a fill-in-the-blank question because it would be too confusing and difficult for students to understand and process effectively in an educational context where clarity and conciseness are important factors."
        questions = generator.generate_questions(long_text, num_questions=1)
        # Should either filter out or handle appropriately
        assert len(questions) <= 1, "Should handle long sentences appropriately"
    
    def test_confidence_calculation(self, generator, sample_text):
        """Test confidence score calculation."""
        questions = generator.generate_questions(sample_text, num_questions=3)
        
        for question in questions:
            # Confidence should be reasonable
            assert 0.1 <= question.confidence <= 0.9, "Confidence should be in reasonable range"
    
    def test_difficulty_assessment(self, generator, sample_text):
        """Test difficulty level assessment."""
        questions = generator.generate_questions(sample_text, num_questions=5)
        
        difficulties = set()
        for question in questions:
            if question.metadata and 'difficulty' in question.metadata:
                difficulties.add(question.metadata['difficulty'])
        
        # Should have some variety in difficulty levels
        assert len(difficulties) >= 1, "Should assess difficulty levels"


class TestQuestionParaphraser:
    """Test suite for Question Paraphraser."""
    
    @pytest.fixture
    def paraphraser(self):
        """Create question paraphraser instance."""
        config = Config()
        return QuestionParaphraser(config)
    
    @pytest.fixture
    def sample_questions(self):
        """Sample questions for paraphrasing."""
        return [
            Question(
                question="What is machine learning?",
                answer="Machine learning is a subset of AI that enables computers to learn without explicit programming.",
                type='short_answer',
                confidence=0.8
            ),
            Question(
                question="Python is a programming language. True or False?",
                answer="True",
                type='boolean',
                confidence=0.9
            ),
            Question(
                question="Which of the following is a Python framework? A) Django B) React C) Angular D) Vue",
                answer="A",
                options=["Django", "React", "Angular", "Vue"],
                type='mcq',
                confidence=0.7
            )
        ]
    
    def test_basic_paraphrasing(self, paraphraser, sample_questions):
        """Test basic question paraphrasing."""
        paraphrased = paraphraser.paraphrase_questions(sample_questions, num_variations=1)
        
        assert len(paraphrased) > 0, "Should generate paraphrased questions"
        
        for question in paraphrased:
            assert isinstance(question, Question), "Should return Question objects"
            assert question.question, "Should have question text"
            assert question.answer, "Should preserve answer"
            assert 'paraphrased_from' in question.metadata, "Should include paraphrase metadata"
    
    def test_question_type_preservation(self, paraphraser, sample_questions):
        """Test that question types are preserved during paraphrasing."""
        paraphrased = paraphraser.paraphrase_questions(sample_questions, num_variations=1)
        
        original_types = {q.type for q in sample_questions}
        paraphrased_types = {q.type for q in paraphrased}
        
        # Types should be preserved
        assert paraphrased_types.issubset(original_types), "Should preserve question types"
    
    def test_answer_preservation(self, paraphraser, sample_questions):
        """Test that answers are preserved during paraphrasing."""
        paraphrased = paraphraser.paraphrase_questions(sample_questions, num_variations=1)
        
        # Create mapping of original questions to their paraphrases
        for original in sample_questions:
            matching_paraphrases = [
                p for p in paraphrased 
                if p.metadata and p.metadata.get('paraphrased_from') == original.question
            ]
            
            for paraphrase in matching_paraphrases:
                assert paraphrase.answer == original.answer, "Should preserve answers"
                if original.options:
                    assert paraphrase.options == original.options, "Should preserve options"
    
    def test_multiple_variations(self, paraphraser, sample_questions):
        """Test generation of multiple paraphrase variations."""
        paraphrased = paraphraser.paraphrase_questions(sample_questions[:1], num_variations=3)
        
        # Should generate multiple variations
        assert len(paraphrased) <= 3, "Should not exceed requested variations"
        
        # Variations should be different
        question_texts = [q.question for q in paraphrased]
        unique_texts = set(question_texts)
        assert len(unique_texts) == len(question_texts), "Variations should be unique"
    
    def test_confidence_adjustment(self, paraphraser, sample_questions):
        """Test that confidence is appropriately adjusted for paraphrases."""
        paraphrased = paraphraser.paraphrase_questions(sample_questions, num_variations=1)
        
        for original in sample_questions:
            matching_paraphrases = [
                p for p in paraphrased 
                if p.metadata and p.metadata.get('paraphrased_from') == original.question
            ]
            
            for paraphrase in matching_paraphrases:
                # Paraphrased questions should have slightly lower confidence
                assert paraphrase.confidence <= original.confidence, "Paraphrase confidence should be adjusted"


class TestGeneratorIntegration:
    """Test integration between generators and other systems."""
    
    def test_license_tier_integration(self):
        """Test that generators respect license tiers."""
        config = Config()

        # Test with different license tiers
        for tier in [ModelTier.FREE, ModelTier.PREMIUM, ModelTier.ENTERPRISE]:
            generator = FAQGenerator(config)
            # Should initialize without error for all tiers
            assert generator is not None
    
    def test_error_handling(self):
        """Test error handling across generators."""
        config = Config()

        generators = [
            FAQGenerator(config),
            FillBlankGenerator(config),
            QuestionParaphraser(config)
        ]

        for generator in generators:
            # Test with invalid input - should raise ValidationError
            with pytest.raises(Exception):  # ValidationError expected
                generator.generate_questions("", num_questions=1)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
