"""
Tests for AI Model Integration

Tests the AI model components including T5, BERT, and quality enhancement.
Includes both unit tests and integration tests with fallback handling.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from quizaigen.models.base_model import ModelTier, ModelConfig, ModelOutput, LightweightModel
from quizaigen.models.model_cache import ModelCache, CacheEntry
from quizaigen.generators.base import Question


class TestBaseModel(unittest.TestCase):
    """Test base model functionality."""
    
    def test_model_config_creation(self):
        """Test ModelConfig creation and validation."""
        config = ModelConfig(
            model_name="test-model",
            tier=ModelTier.PREMIUM,
            max_length=256
        )
        
        self.assertEqual(config.model_name, "test-model")
        self.assertEqual(config.tier, ModelTier.PREMIUM)
        self.assertEqual(config.max_length, 256)
        self.assertEqual(config.device, "cpu")  # Default
    
    def test_model_output_creation(self):
        """Test ModelOutput creation."""
        output = ModelOutput(
            text="Test output",
            confidence=0.85,
            metadata={"test": True},
            processing_time=1.5,
            model_name="test-model"
        )
        
        self.assertEqual(output.text, "Test output")
        self.assertEqual(output.confidence, 0.85)
        self.assertEqual(output.metadata["test"], True)
        self.assertEqual(output.processing_time, 1.5)
    
    def test_lightweight_model(self):
        """Test lightweight model functionality."""
        config = ModelConfig(model_name="lightweight", tier=ModelTier.FREE)
        model = LightweightModel(config)
        
        # Test loading
        model.load_model()
        self.assertTrue(model.is_loaded)
        
        # Test prediction
        result = model.predict("Test input")
        self.assertIsInstance(result, ModelOutput)
        self.assertEqual(result.text, "Test input")
        self.assertGreater(result.confidence, 0)
        
        # Test unloading
        model.unload_model()
        self.assertFalse(model.is_loaded)


class TestModelCache(unittest.TestCase):
    """Test model cache functionality."""
    
    def setUp(self):
        """Set up test cache."""
        self.cache = ModelCache(
            cache_dir=None,  # Use memory only
            max_memory_entries=10,
            enable_disk_cache=False
        )
    
    def test_cache_put_get(self):
        """Test basic cache put and get operations."""
        output = ModelOutput(
            text="Cached output",
            confidence=0.9,
            metadata={},
            processing_time=1.0,
            model_name="test-model"
        )
        
        # Put in cache
        self.cache.put("test input", "test-model", output, ModelTier.PREMIUM)
        
        # Get from cache
        cached_output = self.cache.get("test input", "test-model")
        
        self.assertIsNotNone(cached_output)
        self.assertEqual(cached_output.text, "Cached output")
        self.assertEqual(cached_output.confidence, 0.9)
    
    def test_cache_miss(self):
        """Test cache miss scenario."""
        result = self.cache.get("nonexistent input", "test-model")
        self.assertIsNone(result)
    
    def test_cache_stats(self):
        """Test cache statistics."""
        stats = self.cache.get_cache_stats()
        
        self.assertIn("memory_entries", stats)
        self.assertIn("disk_entries", stats)
        self.assertIn("max_memory_entries", stats)
        self.assertEqual(stats["memory_entries"], 0)  # Empty cache


class TestAIModelIntegration(unittest.TestCase):
    """Test AI model integration with mocking for missing dependencies."""
    
    def setUp(self):
        """Set up test environment."""
        self.sample_question = Question(
            question="What is the capital of France?",
            answer="Paris",
            type="mcq",
            options=["London", "Berlin", "Paris", "Madrid"],
            confidence=0.8
        )
    
    def test_missing_dependencies_handling(self):
        """Test handling of missing AI model dependencies."""
        # Create a mock missing dependency class
        class _MissingDependency:
            def __init__(self, *args, **kwargs):
                raise ImportError(
                    "AI model features require additional dependencies. "
                    "Install with: pip install quizaigen[premium] or pip install transformers torch"
                )

        # Test that the placeholder class raises ImportError
        with self.assertRaises(ImportError):
            _MissingDependency()

        # Test that the error message is helpful
        try:
            _MissingDependency()
        except ImportError as e:
            self.assertIn("AI model features require additional dependencies", str(e))
            self.assertIn("pip install", str(e))
    
    @patch('quizaigen.models.AI_MODELS_AVAILABLE', True)
    @patch('quizaigen.models.t5_integration.TRANSFORMERS_AVAILABLE', True)
    def test_t5_integration_mock(self):
        """Test T5 integration with mocked transformers."""
        with patch('quizaigen.models.t5_integration.T5ForConditionalGeneration') as mock_model, \
             patch('quizaigen.models.t5_integration.T5Tokenizer') as mock_tokenizer:
            
            # Mock the tokenizer and model
            mock_tokenizer.from_pretrained.return_value = Mock()
            mock_model.from_pretrained.return_value = Mock()
            
            from quizaigen.models.t5_integration import T5QuestionGenerator
            
            # This should not raise an error with mocked dependencies
            generator = T5QuestionGenerator(tier=ModelTier.PREMIUM)
            self.assertEqual(generator.tier, ModelTier.PREMIUM)
    
    @patch('quizaigen.models.AI_MODELS_AVAILABLE', True)
    @patch('quizaigen.models.bert_integration.TRANSFORMERS_AVAILABLE', True)
    def test_bert_integration_mock(self):
        """Test BERT integration with mocked transformers."""
        with patch('quizaigen.models.bert_integration.BertModel') as mock_model, \
             patch('quizaigen.models.bert_integration.BertTokenizer') as mock_tokenizer:
            
            # Mock the tokenizer and model
            mock_tokenizer.from_pretrained.return_value = Mock()
            mock_model.from_pretrained.return_value = Mock()
            
            from quizaigen.models.bert_integration import BERTAnswerValidator
            
            # This should not raise an error with mocked dependencies
            validator = BERTAnswerValidator(tier=ModelTier.PREMIUM)
            self.assertEqual(validator.tier, ModelTier.PREMIUM)
    
    def test_model_tier_access_control(self):
        """Test tier-based access control."""
        # Test that FREE tier only allows lightweight models
        free_config = ModelConfig(model_name="test", tier=ModelTier.FREE)
        lightweight = LightweightModel(free_config)
        
        # Should work fine
        lightweight.load_model()
        self.assertTrue(lightweight.is_loaded)
        
        # Test tier validation in config
        premium_config = ModelConfig(model_name="test", tier=ModelTier.PREMIUM)
        self.assertEqual(premium_config.tier, ModelTier.PREMIUM)


class TestQualityEnhancement(unittest.TestCase):
    """Test quality enhancement functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.sample_question = Question(
            question="What is 2+2?",
            answer="4",
            type="mcq",
            options=["3", "4", "5", "6"],
            confidence=0.7,
            metadata={}
        )
    
    @patch('quizaigen.models.AI_MODELS_AVAILABLE', True)
    def test_quality_enhancement_structure(self):
        """Test quality enhancement data structures."""
        from quizaigen.models.ai_quality_enhancer import QualityEnhancementResult
        
        result = QualityEnhancementResult(
            original_question=self.sample_question,
            enhanced_question=self.sample_question,
            improvements=["Improved clarity"],
            quality_scores={"overall": 0.8},
            confidence_boost=0.1,
            processing_time=1.5
        )
        
        self.assertEqual(result.original_question, self.sample_question)
        self.assertEqual(len(result.improvements), 1)
        self.assertEqual(result.confidence_boost, 0.1)
    
    def test_inference_request_structure(self):
        """Test inference request data structure."""
        from quizaigen.models.inference_pipeline import InferenceRequest, InferenceResult
        
        request = InferenceRequest(
            task="enhance_question",
            input_data={"question": self.sample_question.to_dict()},
            tier=ModelTier.PREMIUM,
            options={}
        )
        
        self.assertEqual(request.task, "enhance_question")
        self.assertEqual(request.tier, ModelTier.PREMIUM)
        
        # Test result structure
        result = InferenceResult(
            success=True,
            output="Enhanced question",
            confidence=0.9,
            processing_time=1.0,
            model_used="T5QuestionGenerator",
            tier=ModelTier.PREMIUM
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.confidence, 0.9)


class TestModelManager(unittest.TestCase):
    """Test model manager functionality."""
    
    @patch('quizaigen.models.AI_MODELS_AVAILABLE', True)
    def test_model_manager_initialization(self):
        """Test model manager initialization."""
        from quizaigen.models.model_manager import ModelManager, ModelType
        
        manager = ModelManager(tier=ModelTier.PREMIUM)
        
        self.assertEqual(manager.tier, ModelTier.PREMIUM)
        self.assertIsNotNone(manager.tier_models)
        
        # Test available models for tier
        available = manager.get_available_models()
        self.assertIn(ModelType.LIGHTWEIGHT, available)
    
    @patch('quizaigen.models.AI_MODELS_AVAILABLE', True)
    def test_tier_based_model_access(self):
        """Test tier-based model access control."""
        from quizaigen.models.model_manager import ModelManager, ModelType
        
        # FREE tier manager
        free_manager = ModelManager(tier=ModelTier.FREE)
        free_models = free_manager.get_available_models()
        
        # Should only have lightweight model
        self.assertIn(ModelType.LIGHTWEIGHT, free_models)
        self.assertEqual(len(free_models), 1)
        
        # PREMIUM tier manager
        premium_manager = ModelManager(tier=ModelTier.PREMIUM)
        premium_models = premium_manager.get_available_models()
        
        # Should have more models
        self.assertIn(ModelType.LIGHTWEIGHT, premium_models)
        self.assertGreater(len(premium_models), 1)


class TestInferencePipeline(unittest.TestCase):
    """Test inference pipeline functionality."""
    
    @patch('quizaigen.models.AI_MODELS_AVAILABLE', True)
    def test_pipeline_initialization(self):
        """Test inference pipeline initialization."""
        from quizaigen.models.inference_pipeline import InferencePipeline
        
        pipeline = InferencePipeline(tier=ModelTier.PREMIUM)
        
        self.assertEqual(pipeline.tier, ModelTier.PREMIUM)
        self.assertTrue(pipeline.enable_fallback)
        self.assertIsNotNone(pipeline.task_models)
    
    @patch('quizaigen.models.AI_MODELS_AVAILABLE', True)
    def test_task_model_mapping(self):
        """Test task to model mapping."""
        from quizaigen.models.inference_pipeline import InferencePipeline
        
        pipeline = InferencePipeline(tier=ModelTier.PREMIUM)
        
        # Check that tasks are mapped to appropriate models
        self.assertIn("enhance_question", pipeline.task_models)
        self.assertIn("validate_answer", pipeline.task_models)
        self.assertIn("generate_fill_blank", pipeline.task_models)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
