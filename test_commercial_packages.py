#!/usr/bin/env python3
"""
Test Script for QuizAIGen Commercial Packages
============================================

This script tests the commercial packages we've built to ensure they work correctly
with the license system and tier-based features.
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path
import json

def test_package_installation(package_path, tier):
    """Test installation of a commercial package."""
    print(f"\n{'='*60}")
    print(f"Testing {tier.upper()} Package Installation")
    print(f"{'='*60}")
    
    # Create temporary virtual environment for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        venv_path = Path(temp_dir) / "test_venv"
        
        print(f"Creating test environment: {venv_path}")
        
        # Create virtual environment
        result = subprocess.run([
            sys.executable, "-m", "venv", str(venv_path)
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Failed to create virtual environment: {result.stderr}")
            return False
        
        # Determine pip path based on OS
        if os.name == 'nt':  # Windows
            pip_path = venv_path / "Scripts" / "pip.exe"
            python_path = venv_path / "Scripts" / "python.exe"
        else:  # Unix-like
            pip_path = venv_path / "bin" / "pip"
            python_path = venv_path / "bin" / "python"
        
        # Install the package
        print(f"Installing package: {package_path}")
        result = subprocess.run([
            str(pip_path), "install", str(package_path)
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Failed to install package: {result.stderr}")
            return False
        
        print("✅ Package installed successfully")
        
        # Test basic import
        test_script = f'''
import sys
try:
    import quizaigen
    print("✅ QuizAIGen imported successfully")
    print(f"Version: {{quizaigen.__version__}}")
    print(f"Author: {{quizaigen.__author__}}")
    print(f"License: {{quizaigen.__license__}}")
    
    # Test license system
    try:
        license_info = quizaigen.get_license_info()
        print(f"License status: {{license_info.get('status', 'unknown')}}")
        print(f"Current tier: {{quizaigen.get_current_tier().value}}")
    except Exception as e:
        print(f"License system test: {{e}}")
    
    # Test basic functionality
    try:
        generator = quizaigen.QuestionGenerator()
        print("✅ QuestionGenerator created successfully")
        
        # Test basic question generation (should work for all tiers)
        test_text = "Python is a programming language created by Guido van Rossum."
        questions = generator.generate_mcq(test_text, num_questions=2)
        print(f"✅ Generated {{len(questions)}} MCQ questions")
        
        if questions:
            print(f"Sample question: {{questions[0].question}}")
    except Exception as e:
        print(f"❌ Basic functionality test failed: {{e}}")
        
except ImportError as e:
    print(f"❌ Failed to import QuizAIGen: {{e}}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {{e}}")
    sys.exit(1)
'''
        
        # Run the test script
        result = subprocess.run([
            str(python_path), "-c", test_script
        ], capture_output=True, text=True)
        
        print("Test Results:")
        print(result.stdout)
        
        if result.stderr:
            print("Warnings/Errors:")
            print(result.stderr)
        
        return result.returncode == 0

def test_tier_specific_features(tier):
    """Test tier-specific features."""
    print(f"\n{'='*60}")
    print(f"Testing {tier.upper()} Tier Features")
    print(f"{'='*60}")
    
    # This would test tier-specific features
    # For now, we'll just verify the package structure
    
    tier_features = {
        'free': [
            'basic_mcq', 'basic_boolean', 'basic_faq'
        ],
        'premium': [
            'basic_mcq', 'basic_boolean', 'basic_faq',
            'fill_blank', 'advanced_export', 'batch_processing'
        ],
        'enterprise': [
            'basic_mcq', 'basic_boolean', 'basic_faq',
            'fill_blank', 'advanced_export', 'batch_processing',
            'multilingual', 'custom_training', 'gpu_acceleration'
        ]
    }
    
    expected_features = tier_features.get(tier, [])
    print(f"Expected features for {tier}: {', '.join(expected_features)}")
    
    return True

def verify_package_contents(package_dir, tier):
    """Verify package contents and documentation."""
    print(f"\n{'='*60}")
    print(f"Verifying {tier.upper()} Package Contents")
    print(f"{'='*60}")
    
    package_path = Path(package_dir)
    
    # Check for required files
    required_files = {
        'wheel': '*.whl',
        'install_guide': 'INSTALL.md',
        'license': 'LICENSE.txt',
        'readme': 'README.md'
    }
    
    for file_type, pattern in required_files.items():
        files = list(package_path.glob(pattern))
        if files:
            print(f"✅ {file_type}: {files[0].name}")
        else:
            print(f"❌ Missing {file_type} ({pattern})")
    
    # Check wheel file details
    wheel_files = list(package_path.glob('*.whl'))
    if wheel_files:
        wheel_file = wheel_files[0]
        print(f"\nWheel file details:")
        print(f"  Name: {wheel_file.name}")
        print(f"  Size: {wheel_file.stat().st_size / 1024:.1f} KB")
    
    return True

def generate_test_report():
    """Generate a test report."""
    print(f"\n{'='*60}")
    print("COMMERCIAL PACKAGE BUILD TEST REPORT")
    print(f"{'='*60}")
    
    # Check what packages were built
    dist_dir = Path("dist/commercial")
    
    if not dist_dir.exists():
        print("❌ No commercial packages found!")
        return False
    
    tiers = ['free', 'premium', 'enterprise']
    results = {}
    
    for tier in tiers:
        tier_dir = dist_dir / tier
        if tier_dir.exists():
            print(f"\n📦 {tier.upper()} TIER PACKAGE")
            print("-" * 40)
            
            # Verify contents
            verify_package_contents(tier_dir, tier)
            
            # Test installation
            wheel_files = list(tier_dir.glob('*.whl'))
            if wheel_files:
                wheel_file = wheel_files[0]
                install_success = test_package_installation(wheel_file, tier)
                results[tier] = {
                    'package_exists': True,
                    'install_success': install_success,
                    'wheel_file': wheel_file.name
                }
            else:
                print(f"❌ No wheel file found for {tier} tier")
                results[tier] = {
                    'package_exists': False,
                    'install_success': False,
                    'wheel_file': None
                }
            
            # Test tier features
            test_tier_specific_features(tier)
        else:
            print(f"❌ {tier.upper()} tier package not found")
            results[tier] = {
                'package_exists': False,
                'install_success': False,
                'wheel_file': None
            }
    
    # Summary
    print(f"\n{'='*60}")
    print("BUILD SUMMARY")
    print(f"{'='*60}")
    
    for tier, result in results.items():
        status = "✅" if result['package_exists'] and result['install_success'] else "❌"
        print(f"{status} {tier.upper()}: Package exists: {result['package_exists']}, "
              f"Install test: {result['install_success']}")
    
    # Save results
    with open('build_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nDetailed results saved to: build_test_results.json")
    
    return all(r['package_exists'] for r in results.values())

def main():
    """Main test function."""
    print("QuizAIGen Commercial Package Build Test")
    print("=" * 60)
    
    # Change to the correct directory
    os.chdir(Path(__file__).parent)
    
    # Generate test report
    success = generate_test_report()
    
    if success:
        print("\n🎉 All commercial packages built successfully!")
        print("\nNext steps:")
        print("1. Set up license server for validation")
        print("2. Configure private PyPI server")
        print("3. Set up customer portal")
        print("4. Deploy monitoring infrastructure")
    else:
        print("\n❌ Some packages failed to build properly")
        print("Check the errors above and rebuild as needed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
