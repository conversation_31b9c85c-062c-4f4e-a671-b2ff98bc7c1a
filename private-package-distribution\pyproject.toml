[build-system]
requires = ["setuptools>=61.0", "wheel", "setuptools-scm"]
build-backend = "setuptools.build_meta"

[project]
name = "your-commercial-package"
dynamic = ["version"]
description = "Commercial Python package for SaaS application"
readme = "README.md"
license = {text = "Commercial License"}
authors = [
    {name = "Your Company", email = "<EMAIL>"}
]
maintainers = [
    {name = "Your Company", email = "<EMAIL>"}
]
keywords = ["commercial", "saas", "private"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.8"
dependencies = [
    "requests>=2.25.0",
    "cryptography>=3.4.0",
    "pyjwt>=2.0.0",
    "click>=8.0.0",
    "pydantic>=1.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "flake8>=3.8",
    "mypy>=0.812",
    "pre-commit>=2.0",
]
docs = [
    "sphinx>=4.0",
    "sphinx-rtd-theme>=0.5",
]

[project.urls]
Homepage = "https://yourcompany.com"
Documentation = "https://docs.yourcompany.com"
Repository = "https://github.com/yourcompany/private-repo"
"Bug Tracker" = "https://github.com/yourcompany/private-repo/issues"
Support = "https://support.yourcompany.com"

[project.scripts]
your-package-cli = "your_package.cli:main"

[tool.setuptools]
packages = ["your_package"]
include-package-data = true

[tool.setuptools.dynamic]
version = {attr = "your_package.__version__"}

[tool.setuptools.package-data]
your_package = ["*.json", "*.yaml", "*.txt", "data/*"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["your_package"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
