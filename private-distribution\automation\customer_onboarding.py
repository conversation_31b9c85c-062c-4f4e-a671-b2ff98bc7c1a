#!/usr/bin/env python3
"""
Automated Customer Onboarding System for QuizAIGen
=================================================

Handles automated customer onboarding, license generation,
and delivery workflows for commercial customers.
"""

import os
import sys
import json
import smtplib
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import tempfile
import zipfile

import requests
from jinja2 import Template

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CustomerOnboardingSystem:
    """
    Automated customer onboarding and delivery system.
    """
    
    def __init__(self, config_file: str):
        """
        Initialize the onboarding system.
        
        Args:
            config_file: Path to configuration file
        """
        self.config = self._load_config(config_file)
        self.api_base_url = self.config['api']['base_url']
        self.api_key = self.config['api']['key']
        
        # Email configuration
        self.smtp_server = self.config['email']['smtp_server']
        self.smtp_port = self.config['email']['smtp_port']
        self.smtp_username = self.config['email']['username']
        self.smtp_password = self.config['email']['password']
        self.from_email = self.config['email']['from_address']
        
        # Template directory
        self.template_dir = Path(self.config['templates']['directory'])
        
        # Package storage
        self.package_storage = Path(self.config['packages']['storage_directory'])
    
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            sys.exit(1)
    
    def onboard_customer(self, customer_data: Dict[str, Any]) -> bool:
        """
        Complete customer onboarding process.
        
        Args:
            customer_data: Customer information and license details
            
        Returns:
            bool: True if onboarding successful
        """
        try:
            logger.info(f"Starting onboarding for customer: {customer_data['email']}")
            
            # Step 1: Create customer account
            customer = self._create_customer_account(customer_data)
            if not customer:
                return False
            
            # Step 2: Generate license
            license_info = self._generate_license(customer['id'], customer_data['license'])
            if not license_info:
                return False
            
            # Step 3: Prepare package
            package_info = self._prepare_customer_package(customer, license_info)
            if not package_info:
                return False
            
            # Step 4: Send welcome email with package
            self._send_welcome_email(customer, license_info, package_info)
            
            # Step 5: Create customer portal access
            self._setup_portal_access(customer, license_info)
            
            # Step 6: Schedule follow-up communications
            self._schedule_followups(customer, license_info)
            
            logger.info(f"Onboarding completed for customer: {customer_data['email']}")
            return True
            
        except Exception as e:
            logger.error(f"Onboarding failed for {customer_data['email']}: {e}")
            return False
    
    def _create_customer_account(self, customer_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create customer account via API."""
        try:
            response = requests.post(
                f"{self.api_base_url}/customers",
                json={
                    'name': customer_data['name'],
                    'email': customer_data['email'],
                    'company': customer_data.get('company'),
                    'phone': customer_data.get('phone'),
                    'country': customer_data.get('country'),
                    'use_case': customer_data.get('use_case')
                },
                headers={'Authorization': f'Bearer {self.api_key}'}
            )
            
            if response.status_code == 201:
                customer = response.json()
                logger.info(f"Customer account created: {customer['id']}")
                return customer
            else:
                logger.error(f"Failed to create customer account: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating customer account: {e}")
            return None
    
    def _generate_license(self, customer_id: str, license_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate license for customer."""
        try:
            # Calculate expiration date
            if license_data['duration_months']:
                expires_at = datetime.now() + timedelta(days=license_data['duration_months'] * 30)
            else:
                expires_at = None
            
            response = requests.post(
                f"{self.api_base_url}/licenses",
                json={
                    'customer_id': customer_id,
                    'tier': license_data['tier'],
                    'features': license_data['features'],
                    'expires_at': expires_at.isoformat() if expires_at else None,
                    'max_installations': license_data.get('max_installations', 1),
                    'hardware_binding': license_data.get('hardware_binding', True)
                },
                headers={'Authorization': f'Bearer {self.api_key}'}
            )
            
            if response.status_code == 201:
                license_info = response.json()
                logger.info(f"License generated: {license_info['id']}")
                return license_info
            else:
                logger.error(f"Failed to generate license: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating license: {e}")
            return None
    
    def _prepare_customer_package(self, customer: Dict[str, Any], license_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Prepare customized package for customer."""
        try:
            tier = license_info['tier']
            
            # Get package files for tier
            package_dir = self.package_storage / tier
            if not package_dir.exists():
                logger.error(f"Package directory not found for tier: {tier}")
                return None
            
            # Create temporary directory for customization
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Copy package files
                customer_package_dir = temp_path / f"quizaigen-{tier}"
                customer_package_dir.mkdir()
                
                # Copy wheel file
                wheel_files = list(package_dir.glob("*.whl"))
                if wheel_files:
                    wheel_file = wheel_files[0]
                    customer_wheel = customer_package_dir / wheel_file.name
                    customer_wheel.write_bytes(wheel_file.read_bytes())
                
                # Create license file
                license_file = customer_package_dir / "license.key"
                license_file.write_text(license_info['license_key'])
                
                # Create installation script
                install_script = self._generate_install_script(customer, license_info)
                (customer_package_dir / "install.py").write_text(install_script)
                
                # Create README
                readme_content = self._generate_customer_readme(customer, license_info)
                (customer_package_dir / "README.md").write_text(readme_content)
                
                # Create ZIP package
                package_zip = temp_path / f"quizaigen-{tier}-{customer['id']}.zip"
                with zipfile.ZipFile(package_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for file_path in customer_package_dir.rglob('*'):
                        if file_path.is_file():
                            arcname = file_path.relative_to(temp_path)
                            zipf.write(file_path, arcname)
                
                # Store package for delivery
                delivery_dir = Path(self.config['delivery']['storage_directory'])
                delivery_dir.mkdir(parents=True, exist_ok=True)
                
                final_package = delivery_dir / package_zip.name
                final_package.write_bytes(package_zip.read_bytes())
                
                return {
                    'package_path': str(final_package),
                    'package_name': package_zip.name,
                    'tier': tier,
                    'size_mb': final_package.stat().st_size / (1024 * 1024)
                }
                
        except Exception as e:
            logger.error(f"Error preparing customer package: {e}")
            return None
    
    def _generate_install_script(self, customer: Dict[str, Any], license_info: Dict[str, Any]) -> str:
        """Generate automated installation script."""
        template = Template("""#!/usr/bin/env python3
\"\"\"
QuizAIGen {{ tier.title() }} Tier Installation Script
Generated for: {{ customer_name }} ({{ customer_email }})
\"\"\"

import os
import sys
import subprocess
import platform
from pathlib import Path

def main():
    print("QuizAIGen {{ tier.title() }} Tier Installation")
    print("=" * 50)
    print(f"Customer: {{ customer_name }}")
    print(f"License Tier: {{ tier.title() }}")
    print()
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("ERROR: Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"Python version: {sys.version}")
    print(f"Platform: {platform.platform()}")
    print()
    
    # Install package
    print("Installing QuizAIGen...")
    wheel_files = list(Path('.').glob('*.whl'))
    if not wheel_files:
        print("ERROR: No wheel file found")
        sys.exit(1)
    
    wheel_file = wheel_files[0]
    cmd = [sys.executable, '-m', 'pip', 'install', str(wheel_file)]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"ERROR: Installation failed: {result.stderr}")
        sys.exit(1)
    
    print("Installation successful!")
    print()
    
    # Set up license
    print("Setting up license...")
    license_file = Path('license.key')
    if license_file.exists():
        license_key = license_file.read_text().strip()
        
        # Create license directory
        license_dir = Path.home() / '.quizaigen'
        license_dir.mkdir(exist_ok=True)
        
        # Save license
        (license_dir / 'license.key').write_text(license_key)
        
        print(f"License saved to: {license_dir / 'license.key'}")
        print()
        
        # Test installation
        print("Testing installation...")
        test_cmd = [sys.executable, '-c', '''
import quizaigen
print(f"QuizAIGen version: {quizaigen.__version__}")
print(f"License tier: {quizaigen.get_current_tier().value}")
print("Installation test successful!")
''']
        
        result = subprocess.run(test_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"WARNING: Test failed: {result.stderr}")
    
    print("Setup complete!")
    print()
    print("Next steps:")
    print("1. Read the README.md file for usage instructions")
    print("2. Visit https://docs.quizaigen.com for full documentation")
    print("3. Contact <EMAIL> for assistance")

if __name__ == "__main__":
    main()
""")
        
        return template.render(
            customer_name=customer['name'],
            customer_email=customer['email'],
            tier=license_info['tier']
        )
    
    def _generate_customer_readme(self, customer: Dict[str, Any], license_info: Dict[str, Any]) -> str:
        """Generate customer-specific README."""
        template_file = self.template_dir / f"readme_{license_info['tier']}.md"
        
        if template_file.exists():
            template = Template(template_file.read_text())
        else:
            # Fallback template
            template = Template("""
# QuizAIGen {{ tier.title() }} Tier

Welcome {{ customer_name }}!

Your QuizAIGen {{ tier.title() }} license is ready to use.

## Installation

1. Run the installation script:
   ```bash
   python install.py
   ```

2. Or install manually:
   ```bash
   pip install *.whl
   ```

## License Setup

Your license key: `{{ license_key }}`

The installation script will automatically set up your license.

## Quick Start

```python
import quizaigen

# Check license status
print(quizaigen.get_license_info())

# Generate questions
generator = quizaigen.QuestionGenerator()
questions = generator.generate_mcq("Your text here", num_questions=5)
print(questions)
```

## Support

- Documentation: https://docs.quizaigen.com
- Support: <EMAIL>
- Customer Portal: https://portal.quizaigen.com

## License Information

- Tier: {{ tier.title() }}
- Features: {{ features|join(', ') }}
- Expires: {{ expires_at or 'Never' }}

Thank you for choosing QuizAIGen!
""")
        
        return template.render(
            customer_name=customer['name'],
            tier=license_info['tier'],
            license_key=license_info['license_key'],
            features=license_info['features'],
            expires_at=license_info.get('expires_at')
        )
    
    def _send_welcome_email(self, customer: Dict[str, Any], license_info: Dict[str, Any], package_info: Dict[str, Any]) -> None:
        """Send welcome email with package attachment."""
        try:
            # Load email template
            template_file = self.template_dir / f"welcome_email_{license_info['tier']}.html"
            if template_file.exists():
                template = Template(template_file.read_text())
            else:
                template = Template("""
<html>
<body>
<h2>Welcome to QuizAIGen {{ tier.title() }} Tier!</h2>

<p>Dear {{ customer_name }},</p>

<p>Thank you for purchasing QuizAIGen {{ tier.title() }}! Your license is now active and ready to use.</p>

<h3>Your License Details:</h3>
<ul>
<li><strong>Tier:</strong> {{ tier.title() }}</li>
<li><strong>Features:</strong> {{ features|join(', ') }}</li>
<li><strong>License Key:</strong> <code>{{ license_key }}</code></li>
<li><strong>Expires:</strong> {{ expires_at or 'Never' }}</li>
</ul>

<h3>Getting Started:</h3>
<ol>
<li>Download the attached package</li>
<li>Extract the files</li>
<li>Run the installation script: <code>python install.py</code></li>
<li>Start generating questions!</li>
</ol>

<h3>Resources:</h3>
<ul>
<li><a href="https://docs.quizaigen.com">Documentation</a></li>
<li><a href="https://portal.quizaigen.com">Customer Portal</a></li>
<li><a href="mailto:<EMAIL>">Support</a></li>
</ul>

<p>If you have any questions, please don't hesitate to contact our support team.</p>

<p>Best regards,<br>The QuizAIGen Team</p>
</body>
</html>
""")
            
            # Create email
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = customer['email']
            msg['Subject'] = f"Welcome to QuizAIGen {license_info['tier'].title()} - Your License is Ready!"
            
            # Add body
            body = template.render(
                customer_name=customer['name'],
                tier=license_info['tier'],
                license_key=license_info['license_key'],
                features=license_info['features'],
                expires_at=license_info.get('expires_at')
            )
            msg.attach(MIMEText(body, 'html'))
            
            # Add package attachment
            with open(package_info['package_path'], 'rb') as f:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(f.read())
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {package_info["package_name"]}'
                )
                msg.attach(part)
            
            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.smtp_username, self.smtp_password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Welcome email sent to {customer['email']}")
            
        except Exception as e:
            logger.error(f"Failed to send welcome email: {e}")
    
    def _setup_portal_access(self, customer: Dict[str, Any], license_info: Dict[str, Any]) -> None:
        """Set up customer portal access."""
        try:
            # Generate portal access token
            response = requests.post(
                f"{self.api_base_url}/portal/access",
                json={
                    'customer_id': customer['id'],
                    'email': customer['email']
                },
                headers={'Authorization': f'Bearer {self.api_key}'}
            )
            
            if response.status_code == 201:
                logger.info(f"Portal access created for {customer['email']}")
            else:
                logger.warning(f"Failed to create portal access: {response.text}")
                
        except Exception as e:
            logger.error(f"Error setting up portal access: {e}")
    
    def _schedule_followups(self, customer: Dict[str, Any], license_info: Dict[str, Any]) -> None:
        """Schedule follow-up communications."""
        try:
            # Schedule follow-up emails
            followups = [
                {'days': 1, 'type': 'getting_started'},
                {'days': 7, 'type': 'check_in'},
                {'days': 30, 'type': 'feedback_request'}
            ]
            
            if license_info.get('expires_at'):
                # Schedule renewal reminders
                followups.extend([
                    {'days': -30, 'type': 'renewal_reminder'},
                    {'days': -7, 'type': 'renewal_urgent'}
                ])
            
            for followup in followups:
                requests.post(
                    f"{self.api_base_url}/communications/schedule",
                    json={
                        'customer_id': customer['id'],
                        'type': followup['type'],
                        'days_offset': followup['days']
                    },
                    headers={'Authorization': f'Bearer {self.api_key}'}
                )
            
            logger.info(f"Follow-up communications scheduled for {customer['email']}")
            
        except Exception as e:
            logger.error(f"Error scheduling follow-ups: {e}")


def main():
    """Main entry point for customer onboarding."""
    import argparse
    
    parser = argparse.ArgumentParser(description="QuizAIGen Customer Onboarding")
    parser.add_argument("config", help="Configuration file")
    parser.add_argument("customer_data", help="Customer data JSON file")
    
    args = parser.parse_args()
    
    # Load customer data
    with open(args.customer_data, 'r') as f:
        customer_data = json.load(f)
    
    # Initialize onboarding system
    onboarding = CustomerOnboardingSystem(args.config)
    
    # Process onboarding
    success = onboarding.onboard_customer(customer_data)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
