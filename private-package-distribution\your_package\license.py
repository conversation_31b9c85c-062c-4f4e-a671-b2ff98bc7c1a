"""
License Management and Validation System
========================================

Handles license key validation, expiration checking, and usage tracking.
"""

import os
import json
import hashlib
import hmac
import base64
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import jwt
import requests


class LicenseError(Exception):
    """Custom exception for license-related errors."""
    pass


class LicenseValidator:
    """
    Comprehensive license validation system with multiple security layers.
    """
    
    def __init__(self, validation_server: Optional[str] = None):
        """
        Initialize license validator.
        
        Args:
            validation_server: URL of license validation server
        """
        self.validation_server = validation_server or os.getenv(
            'YOUR_PACKAGE_LICENSE_SERVER',
            'https://api.yourcompany.com/license'
        )
        self.license_key: Optional[str] = None
        self.license_data: Optional[Dict[str, Any]] = None
        self.last_validation: Optional[datetime] = None
        self.validation_cache_duration = timedelta(hours=24)
        
        # Embedded public key for JWT validation (replace with your actual key)
        self.public_key = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4f5wg5l2hKsTeNem/V41
fGnJm6gOdrj8ym3rFkEjWT2btf02VoBtzS9ze4gRZDRHFpjQqDFt3ShgsHm8xzO
...your actual public key here...
-----END PUBLIC KEY-----"""
        
        # Hardware fingerprinting salt (keep this secret)
        self._fingerprint_salt = b"your-secret-salt-here"
    
    def set_license_key(self, license_key: str) -> None:
        """Set the license key for validation."""
        self.license_key = license_key.strip()
        self.license_data = None  # Reset cached data
    
    def load_license_file(self, file_path: str) -> None:
        """Load license key from file."""
        try:
            with open(file_path, 'r') as f:
                content = f.read().strip()
                
            # Support both plain key and JSON format
            try:
                data = json.loads(content)
                self.license_key = data.get('license_key')
                if 'license_data' in data:
                    self.license_data = data['license_data']
            except json.JSONDecodeError:
                self.license_key = content
                
        except FileNotFoundError:
            raise LicenseError(f"License file not found: {file_path}")
        except Exception as e:
            raise LicenseError(f"Failed to load license file: {str(e)}")
    
    def _generate_hardware_fingerprint(self) -> str:
        """Generate hardware fingerprint for license binding."""
        import platform
        import uuid
        
        # Collect system information
        system_info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'machine': platform.machine(),
            'node': platform.node(),
            'mac_address': ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                   for elements in range(0, 2*6, 2)][::-1])
        }
        
        # Create deterministic fingerprint
        fingerprint_data = json.dumps(system_info, sort_keys=True).encode()
        fingerprint = hmac.new(
            self._fingerprint_salt,
            fingerprint_data,
            hashlib.sha256
        ).hexdigest()
        
        return fingerprint[:16]  # Use first 16 characters
    
    def _validate_jwt_license(self, license_key: str) -> Dict[str, Any]:
        """Validate JWT-based license key."""
        try:
            # Decode JWT token
            payload = jwt.decode(
                license_key,
                self.public_key,
                algorithms=['RS256'],
                options={"verify_exp": True}
            )
            
            # Validate required fields
            required_fields = ['customer_id', 'product', 'features', 'exp']
            for field in required_fields:
                if field not in payload:
                    raise LicenseError(f"Missing required field: {field}")
            
            # Check product match
            if payload.get('product') != 'your-package':
                raise LicenseError("License not valid for this product")
            
            # Check hardware binding if present
            if 'hardware_fingerprint' in payload:
                current_fingerprint = self._generate_hardware_fingerprint()
                if payload['hardware_fingerprint'] != current_fingerprint:
                    raise LicenseError("License not valid for this hardware")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise LicenseError("License has expired")
        except jwt.InvalidTokenError as e:
            raise LicenseError(f"Invalid license token: {str(e)}")
    
    def _validate_online(self, license_key: str) -> Dict[str, Any]:
        """Validate license with online server."""
        try:
            payload = {
                'license_key': license_key,
                'hardware_fingerprint': self._generate_hardware_fingerprint(),
                'product': 'your-package',
                'version': '1.0.0'
            }
            
            response = requests.post(
                f"{self.validation_server}/validate",
                json=payload,
                timeout=10,
                headers={'User-Agent': 'YourPackage/1.0.0'}
            )
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                raise LicenseError("Invalid license key")
            elif response.status_code == 403:
                raise LicenseError("License expired or suspended")
            else:
                raise LicenseError(f"License validation failed: {response.status_code}")
                
        except requests.RequestException:
            # Fallback to offline validation if online fails
            return self._validate_jwt_license(license_key)
    
    def validate(self, force_online: bool = False) -> bool:
        """
        Validate the current license.
        
        Args:
            force_online: Force online validation even if cache is valid
            
        Returns:
            bool: True if license is valid
        """
        if not self.license_key:
            return False
        
        # Check cache validity
        if (not force_online and 
            self.last_validation and 
            self.license_data and
            datetime.now() - self.last_validation < self.validation_cache_duration):
            return True
        
        try:
            # Try online validation first, fallback to offline
            try:
                if force_online or not self.license_data:
                    self.license_data = self._validate_online(self.license_key)
                else:
                    # Use cached data but verify JWT signature
                    self.license_data = self._validate_jwt_license(self.license_key)
            except Exception:
                # Final fallback to JWT validation
                self.license_data = self._validate_jwt_license(self.license_key)
            
            self.last_validation = datetime.now()
            
            # Track usage
            self._track_usage()
            
            return True
            
        except LicenseError:
            self.license_data = None
            self.last_validation = None
            return False
    
    def _track_usage(self) -> None:
        """Track package usage for analytics."""
        if not self.license_data:
            return
        
        try:
            usage_data = {
                'customer_id': self.license_data.get('customer_id'),
                'timestamp': datetime.now().isoformat(),
                'action': 'package_usage',
                'version': '1.0.0',
                'hardware_fingerprint': self._generate_hardware_fingerprint()
            }
            
            # Send usage data asynchronously (non-blocking)
            requests.post(
                f"{self.validation_server}/usage",
                json=usage_data,
                timeout=5
            )
        except Exception:
            # Silently fail usage tracking to not impact functionality
            pass
    
    def get_license_info(self) -> Dict[str, Any]:
        """Get current license information."""
        if not self.license_data:
            return {"status": "invalid"}
        
        return {
            "status": "valid",
            "customer_id": self.license_data.get('customer_id'),
            "features": self.license_data.get('features', []),
            "expires": datetime.fromtimestamp(
                self.license_data.get('exp', 0)
            ).isoformat() if self.license_data.get('exp') else None,
            "last_validated": self.last_validation.isoformat() if self.last_validation else None
        }
    
    def has_feature(self, feature: str) -> bool:
        """Check if license includes specific feature."""
        if not self.license_data:
            return False
        
        features = self.license_data.get('features', [])
        return feature in features or 'all' in features
    
    def get_remaining_days(self) -> Optional[int]:
        """Get number of days remaining on license."""
        if not self.license_data or 'exp' not in self.license_data:
            return None
        
        exp_date = datetime.fromtimestamp(self.license_data['exp'])
        remaining = exp_date - datetime.now()
        
        return max(0, remaining.days)


# Global license validator instance
_global_validator: Optional[LicenseValidator] = None


def get_license_validator() -> LicenseValidator:
    """Get global license validator instance."""
    global _global_validator
    if _global_validator is None:
        _global_validator = LicenseValidator()
    return _global_validator


def require_license(feature: Optional[str] = None):
    """Decorator to require valid license for function execution."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            validator = get_license_validator()
            
            if not validator.validate():
                raise LicenseError("Valid license required")
            
            if feature and not validator.has_feature(feature):
                raise LicenseError(f"License does not include feature: {feature}")
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator
