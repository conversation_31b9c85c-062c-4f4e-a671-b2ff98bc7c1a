#!/usr/bin/env python3
"""
QuizAIGen Package Upload Script

This script handles uploading the QuizAIGen package to PyPI or Test PyPI.
It includes validation, credential checking, and upload functionality.

Usage:
    python scripts/upload_package.py --test  # Upload to Test PyPI
    python scripts/upload_package.py         # Upload to PyPI
    python scripts/upload_package.py --check # Only validate, don't upload
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path
from typing import Optional, Tuple

import requests
from package_validator import PackageValidator


class PackageUploader:
    """Handles package upload to PyPI repositories."""
    
    def __init__(self, test_pypi: bool = False, dry_run: bool = False):
        self.test_pypi = test_pypi
        self.dry_run = dry_run
        self.repository_url = (
            "https://test.pypi.org/legacy/" if test_pypi 
            else "https://upload.pypi.org/legacy/"
        )
        self.check_url = (
            "https://test.pypi.org/pypi/quizaigen/json" if test_pypi
            else "https://pypi.org/pypi/quizaigen/json"
        )
        self.registry_name = "Test PyPI" if test_pypi else "PyPI"
        
    def check_credentials(self) -> bool:
        """Check if PyPI credentials are available."""
        token_var = "TEST_PYPI_API_TOKEN" if self.test_pypi else "PYPI_API_TOKEN"
        
        # Check environment variable
        if os.getenv(token_var):
            print(f"✅ Found {token_var} in environment")
            return True
            
        # Check .pypirc file
        pypirc_path = Path.home() / ".pypirc"
        if pypirc_path.exists():
            content = pypirc_path.read_text()
            section = "testpypi" if self.test_pypi else "pypi"
            if f"[{section}]" in content:
                print(f"✅ Found {section} configuration in .pypirc")
                return True
                
        print(f"❌ No credentials found for {self.registry_name}")
        print(f"Please set {token_var} environment variable or configure .pypirc")
        return False
        
    def get_package_version(self) -> Optional[str]:
        """Get the current package version from pyproject.toml."""
        try:
            import tomli
            
            pyproject_path = Path("pyproject.toml")
            if not pyproject_path.exists():
                print("❌ pyproject.toml not found")
                return None
                
            with open(pyproject_path, "rb") as f:
                data = tomli.load(f)
                
            version = data.get("project", {}).get("version")
            if not version:
                print("❌ Version not found in pyproject.toml")
                return None
                
            return version
            
        except ImportError:
            print("❌ tomli package required for reading pyproject.toml")
            print("Install with: pip install tomli")
            return None
        except Exception as e:
            print(f"❌ Error reading pyproject.toml: {e}")
            return None
            
    def check_version_exists(self, version: str) -> bool:
        """Check if version already exists on PyPI."""
        try:
            response = requests.get(self.check_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                existing_versions = list(data.get("releases", {}).keys())
                if version in existing_versions:
                    print(f"❌ Version {version} already exists on {self.registry_name}")
                    print(f"Existing versions: {', '.join(sorted(existing_versions)[-5:])}")
                    return True
                else:
                    print(f"✅ Version {version} is new on {self.registry_name}")
                    return False
            elif response.status_code == 404:
                print(f"✅ Package not found on {self.registry_name} (first release)")
                return False
            else:
                print(f"⚠️  Could not check {self.registry_name} (status: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"⚠️  Error checking {self.registry_name}: {e}")
            return False
            
    def validate_dist_files(self) -> bool:
        """Validate distribution files exist and are valid."""
        dist_dir = Path("dist")
        if not dist_dir.exists():
            print("❌ dist/ directory not found")
            print("Run 'python -m build' first")
            return False
            
        # Find wheel and source distribution
        wheel_files = list(dist_dir.glob("*.whl"))
        sdist_files = list(dist_dir.glob("*.tar.gz"))
        
        if not wheel_files:
            print("❌ No wheel files found in dist/")
            return False
            
        if not sdist_files:
            print("❌ No source distribution found in dist/")
            return False
            
        print(f"✅ Found {len(wheel_files)} wheel(s) and {len(sdist_files)} source distribution(s)")
        
        # Validate with twine
        try:
            result = subprocess.run(
                ["twine", "check", "dist/*"],
                capture_output=True,
                text=True,
                check=False
            )
            
            if result.returncode == 0:
                print("✅ All distribution files passed twine check")
                return True
            else:
                print("❌ Twine check failed:")
                print(result.stdout)
                print(result.stderr)
                return False
                
        except FileNotFoundError:
            print("❌ twine not found. Install with: pip install twine")
            return False
        except Exception as e:
            print(f"❌ Error running twine check: {e}")
            return False
            
    def upload_package(self) -> bool:
        """Upload package to PyPI."""
        if self.dry_run:
            print(f"🔍 DRY RUN: Would upload to {self.registry_name}")
            return True
            
        print(f"📤 Uploading to {self.registry_name}...")
        
        cmd = ["twine", "upload"]
        
        if self.test_pypi:
            cmd.extend(["--repository", "testpypi"])
            
        # Add token if available
        token_var = "TEST_PYPI_API_TOKEN" if self.test_pypi else "PYPI_API_TOKEN"
        token = os.getenv(token_var)
        if token:
            cmd.extend(["--username", "__token__", "--password", token])
            
        cmd.append("dist/*")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=False
            )
            
            if result.returncode == 0:
                print(f"✅ Successfully uploaded to {self.registry_name}!")
                print(result.stdout)
                return True
            else:
                print(f"❌ Upload to {self.registry_name} failed:")
                print(result.stdout)
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ Error uploading package: {e}")
            return False
            
    def post_upload_verification(self, version: str) -> bool:
        """Verify package is available after upload."""
        if self.dry_run:
            return True
            
        print(f"🔍 Verifying package availability on {self.registry_name}...")
        
        import time
        
        # Wait up to 2 minutes for package to be available
        for attempt in range(12):
            try:
                response = requests.get(self.check_url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if version in data.get("releases", {}):
                        print(f"✅ Package {version} is available on {self.registry_name}")
                        
                        # Show installation command
                        if self.test_pypi:
                            install_cmd = f"pip install -i https://test.pypi.org/simple/ quizaigen=={version}"
                        else:
                            install_cmd = f"pip install quizaigen=={version}"
                            
                        print(f"📦 Install with: {install_cmd}")
                        return True
                        
                print(f"⏳ Attempt {attempt + 1}/12: Package not yet available...")
                time.sleep(10)
                
            except Exception as e:
                print(f"⚠️  Error checking package: {e}")
                time.sleep(10)
                
        print(f"❌ Package not available on {self.registry_name} after 2 minutes")
        return False
        
    def run_full_upload(self) -> bool:
        """Run the complete upload process."""
        print(f"🚀 Starting upload to {self.registry_name}")
        print("=" * 50)
        
        # Step 1: Validate project structure
        print("\n📋 Step 1: Validating project structure...")
        validator = PackageValidator()
        if not validator.validate_all():
            print("❌ Project validation failed")
            return False
            
        # Step 2: Check credentials
        print("\n🔑 Step 2: Checking credentials...")
        if not self.check_credentials():
            return False
            
        # Step 3: Get package version
        print("\n📦 Step 3: Getting package version...")
        version = self.get_package_version()
        if not version:
            return False
        print(f"Package version: {version}")
        
        # Step 4: Check if version exists
        print("\n🔍 Step 4: Checking version availability...")
        if self.check_version_exists(version):
            if not self.dry_run:
                return False
                
        # Step 5: Validate distribution files
        print("\n📁 Step 5: Validating distribution files...")
        if not self.validate_dist_files():
            return False
            
        # Step 6: Upload package
        print("\n📤 Step 6: Uploading package...")
        if not self.upload_package():
            return False
            
        # Step 7: Verify upload
        print("\n✅ Step 7: Verifying upload...")
        if not self.post_upload_verification(version):
            return False
            
        print("\n🎉 Upload completed successfully!")
        return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Upload QuizAIGen package to PyPI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/upload_package.py --test     # Upload to Test PyPI
  python scripts/upload_package.py           # Upload to PyPI
  python scripts/upload_package.py --check   # Only validate, don't upload
  python scripts/upload_package.py --test --check  # Validate for Test PyPI
        """
    )
    
    parser.add_argument(
        "--test",
        action="store_true",
        help="Upload to Test PyPI instead of PyPI"
    )
    
    parser.add_argument(
        "--check",
        action="store_true",
        help="Only validate package, don't upload"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Change to project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    uploader = PackageUploader(
        test_pypi=args.test,
        dry_run=args.check
    )
    
    try:
        success = uploader.run_full_upload()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n❌ Upload cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()