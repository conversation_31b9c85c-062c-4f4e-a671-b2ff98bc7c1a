{"tests/test_multilingual_integration.py::TestMultilingualIntegration::test_cultural_adaptation": true, "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_entity_preservation_across_languages": true, "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_french_text_processing": true, "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_question_quality_metrics": true, "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_extract_with_ocr": true, "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_extract_with_pdfplumber": true, "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_extract_with_pypdf2": true, "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_process_word_enhanced": true, "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_validate_file_success": true, "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_validate_file_too_large": true, "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_validate_file_unsupported_format": true}