#!/usr/bin/env python3
"""
Test script for enhanced logging system
"""

import time
import random
from pathlib import Path
from quizaigen.utils.logger import (
    configure_logging, get_logger, LoggerMixin, LoggedOperation,
    get_logging_config, get_log_files, get_log_statistics,
    cleanup_old_logs, create_operation_logger, log_system_info
)


class TestClass(LoggerMixin):
    """Test class using LoggerMixin."""
    
    def __init__(self, name: str):
        super().__init__()
        self.name = name
    
    def do_work(self, duration: float = 0.1):
        """Simulate some work with logging."""
        self.log_info(f"Starting work for {self.name}", extra_data={'duration': duration})
        
        start_time = time.time()
        time.sleep(duration)
        end_time = time.time()
        
        actual_duration = end_time - start_time
        self.log_performance("work_operation", actual_duration, 
                           extra_data={'expected_duration': duration, 'worker': self.name})
        
        if random.random() < 0.1:  # 10% chance of "error"
            self.log_warning("Simulated warning during work", 
                           extra_data={'worker': self.name, 'random_value': random.random()})
        
        self.log_info(f"Work completed for {self.name}", 
                     extra_data={'actual_duration': actual_duration})
        
        return actual_duration
    
    def simulate_error(self):
        """Simulate an error for testing error logging."""
        try:
            raise ValueError(f"Simulated error from {self.name}")
        except Exception as e:
            self.log_error("An error occurred during simulation", 
                         exception=e, extra_data={'worker': self.name})


def test_basic_logging():
    """Test basic logging functionality."""
    print("=== Testing Basic Logging ===")
    
    # Configure logging with file output
    config = configure_logging(
        log_dir="test_logs",
        level="DEBUG",
        console_logging=True,
        file_logging=True,
        enable_json_logging=False,
        enable_performance_logging=True,
        separate_error_log=True
    )
    
    print(f"✓ Logging configured: {config.log_dir}")
    
    # Test basic logger
    logger = get_logger("test_basic")
    logger.debug("Debug message")
    logger.info("Info message")
    logger.warning("Warning message")
    logger.error("Error message")
    
    print("✓ Basic logging messages sent")


def test_mixin_logging():
    """Test LoggerMixin functionality."""
    print("\n=== Testing LoggerMixin ===")
    
    # Create test workers
    workers = [TestClass(f"worker_{i}") for i in range(3)]
    
    # Simulate work
    for worker in workers:
        duration = random.uniform(0.05, 0.2)
        worker.do_work(duration)
        
        # Occasionally simulate errors
        if random.random() < 0.3:
            worker.simulate_error()
    
    print("✓ LoggerMixin testing completed")


def test_operation_logging():
    """Test operation logging with context manager."""
    print("\n=== Testing Operation Logging ===")
    
    # Test successful operation
    with LoggedOperation("test_operation_success") as op:
        time.sleep(0.1)
        print("✓ Successful operation completed")
    
    # Test failed operation
    try:
        with LoggedOperation("test_operation_failure", 
                           extra_data={'test_mode': True}) as op:
            time.sleep(0.05)
            raise RuntimeError("Simulated operation failure")
    except RuntimeError:
        print("✓ Failed operation logged")
    
    # Test operation logger
    op_logger = create_operation_logger("batch_processing", "session_123")
    op_logger.info("Operation-specific log message")
    
    print("✓ Operation logging completed")


def test_json_logging():
    """Test JSON structured logging."""
    print("\n=== Testing JSON Logging ===")
    
    # Reconfigure for JSON logging
    configure_logging(
        log_dir="test_logs",
        level="INFO",
        enable_json_logging=True,
        console_logging=False,  # Disable console for cleaner output
        file_logging=True
    )
    
    logger = get_logger("test_json")
    logger.info("JSON structured log", extra={'extra_data': {
        'user_id': 'user_123',
        'operation': 'test_json_logging',
        'metrics': {'duration': 0.123, 'success': True}
    }})
    
    print("✓ JSON logging completed")


def test_log_management():
    """Test log management utilities."""
    print("\n=== Testing Log Management ===")
    
    # Get logging configuration
    config = get_logging_config()
    print(f"✓ Current config: {len(config['configured_loggers'])} loggers configured")
    
    # Get log files info
    log_files = get_log_files()
    print(f"✓ Log files found: {len(log_files)} files")
    
    # Get statistics
    stats = get_log_statistics()
    print(f"✓ Log statistics: {stats['total_log_files']} files, {stats['total_size_mb']} MB")
    
    # Test cleanup (with 0 days to clean everything for testing)
    # cleanup_results = cleanup_old_logs(days_to_keep=0)
    # print(f"✓ Cleanup test: would remove {cleanup_results['files_removed']} files")


def test_performance_logging():
    """Test performance logging specifically."""
    print("\n=== Testing Performance Logging ===")
    
    # Reconfigure to ensure performance logging is enabled
    configure_logging(
        log_dir="test_logs",
        level="INFO",
        enable_performance_logging=True,
        console_logging=True,
        file_logging=True
    )
    
    # Create a test class for performance logging
    class PerformanceTest(LoggerMixin):
        def cpu_intensive_task(self, iterations: int = 10000):
            start_time = time.time()
            
            # Simulate CPU work
            result = sum(i * i for i in range(iterations))
            
            duration = time.time() - start_time
            self.log_performance("cpu_intensive_task", duration, 
                               extra_data={'iterations': iterations, 'result': result})
            return result
        
        def io_simulation(self, delay: float = 0.1):
            start_time = time.time()
            
            # Simulate I/O wait
            time.sleep(delay)
            
            duration = time.time() - start_time
            self.log_performance("io_simulation", duration, 
                               extra_data={'expected_delay': delay})
    
    # Run performance tests
    perf_test = PerformanceTest()
    perf_test.cpu_intensive_task(5000)
    perf_test.io_simulation(0.05)
    
    print("✓ Performance logging completed")


def main():
    """Run all logging tests."""
    print("🚀 Starting Enhanced Logging System Tests")
    
    # Log system information
    log_system_info()
    
    # Run all tests
    test_basic_logging()
    test_mixin_logging()
    test_operation_logging()
    test_json_logging()
    test_log_management()
    test_performance_logging()
    
    print("\n✅ All logging tests completed successfully!")
    print(f"📁 Check the 'test_logs' directory for generated log files")
    
    # Show final statistics
    final_stats = get_log_statistics()
    print(f"📊 Final stats: {final_stats['total_log_files']} log files, {final_stats['total_size_mb']} MB total")


if __name__ == "__main__":
    main()
