"""
Benchmarking Tools

This module provides benchmarking capabilities for QuizAIGen components.
"""

import time
import statistics
from typing import Dict, Any, List, Callable, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from multiprocessing import cpu_count

from .performance_monitor import PerformanceMonitor, SystemResources
from .logger import LoggerMixin


@dataclass
class BenchmarkResult:
    """Container for benchmark results."""
    test_name: str
    iterations: int
    total_time: float
    avg_time: float
    min_time: float
    max_time: float
    median_time: float
    std_dev: float
    throughput: float
    success_rate: float
    system_resources: Dict[str, Any]
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'test_name': self.test_name,
            'iterations': self.iterations,
            'total_time': self.total_time,
            'avg_time': self.avg_time,
            'min_time': self.min_time,
            'max_time': self.max_time,
            'median_time': self.median_time,
            'std_dev': self.std_dev,
            'throughput': self.throughput,
            'success_rate': self.success_rate,
            'system_resources': self.system_resources,
            'metadata': self.metadata
        }


class Benchmark(LoggerMixin):
    """Benchmarking system for QuizAIGen components."""
    
    def __init__(self, monitor: Optional[PerformanceMonitor] = None):
        """
        Initialize benchmark system.
        
        Args:
            monitor: Performance monitor instance
        """
        self.monitor = monitor or PerformanceMonitor()
        self.results: List[BenchmarkResult] = []
    
    def run_function_benchmark(self, func: Callable, args: tuple = (), kwargs: dict = None,
                             iterations: int = 10, test_name: Optional[str] = None) -> BenchmarkResult:
        """
        Benchmark a function with multiple iterations.
        
        Args:
            func: Function to benchmark
            args: Function arguments
            kwargs: Function keyword arguments
            iterations: Number of iterations
            test_name: Name for the test
        
        Returns:
            Benchmark results
        """
        if kwargs is None:
            kwargs = {}
        
        if test_name is None:
            test_name = func.__name__
        
        self.log_info(f"Running benchmark: {test_name} ({iterations} iterations)")
        
        # Record system resources at start
        start_resources = SystemResources.get_current()
        
        times = []
        successes = 0
        errors = []
        
        total_start = time.time()
        
        for i in range(iterations):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                times.append(end_time - start_time)
                successes += 1
            except Exception as e:
                end_time = time.time()
                times.append(end_time - start_time)
                errors.append(str(e))
        
        total_end = time.time()
        total_time = total_end - total_start
        
        # Record system resources at end
        end_resources = SystemResources.get_current()
        
        # Calculate statistics
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            median_time = statistics.median(times)
            std_dev = statistics.stdev(times) if len(times) > 1 else 0.0
            throughput = iterations / total_time
        else:
            avg_time = min_time = max_time = median_time = std_dev = throughput = 0.0
        
        success_rate = successes / iterations if iterations > 0 else 0.0
        
        result = BenchmarkResult(
            test_name=test_name,
            iterations=iterations,
            total_time=total_time,
            avg_time=avg_time,
            min_time=min_time,
            max_time=max_time,
            median_time=median_time,
            std_dev=std_dev,
            throughput=throughput,
            success_rate=success_rate,
            system_resources={
                'start': {
                    'cpu_usage': start_resources.cpu_usage,
                    'memory_usage': start_resources.memory_usage_percent
                },
                'end': {
                    'cpu_usage': end_resources.cpu_usage,
                    'memory_usage': end_resources.memory_usage_percent
                }
            },
            metadata={
                'errors': errors,
                'error_count': len(errors)
            }
        )
        
        self.results.append(result)
        
        self.log_info(f"✓ {test_name}: {avg_time:.4f}s avg, {throughput:.2f} ops/sec, {success_rate:.1%} success")
        
        return result
    
    def run_parallel_benchmark(self, func: Callable, test_data: List[tuple],
                             worker_counts: List[int] = None,
                             test_name: Optional[str] = None) -> List[BenchmarkResult]:
        """
        Benchmark parallel processing with different worker counts.
        
        Args:
            func: Function to benchmark
            test_data: List of (args, kwargs) tuples for each test
            worker_counts: List of worker counts to test
            test_name: Base name for tests
        
        Returns:
            List of benchmark results
        """
        if worker_counts is None:
            worker_counts = [1, 2, 4, cpu_count(), cpu_count() * 2]
        
        if test_name is None:
            test_name = f"{func.__name__}_parallel"
        
        results = []
        
        # Sequential baseline
        sequential_result = self._run_sequential_test(func, test_data, f"{test_name}_sequential")
        results.append(sequential_result)
        
        # Parallel tests
        for worker_count in worker_counts:
            if worker_count <= len(test_data):  # Don't use more workers than tasks
                parallel_result = self._run_parallel_test(
                    func, test_data, worker_count, f"{test_name}_{worker_count}workers"
                )
                results.append(parallel_result)
        
        # Print comparison
        self._print_parallel_comparison(results)
        
        return results
    
    def _run_sequential_test(self, func: Callable, test_data: List[tuple], test_name: str) -> BenchmarkResult:
        """Run sequential benchmark test."""
        self.log_info(f"Running sequential benchmark: {test_name}")
        
        start_resources = SystemResources.get_current()
        times = []
        successes = 0
        errors = []
        
        total_start = time.time()
        
        for args, kwargs in test_data:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                times.append(end_time - start_time)
                successes += 1
            except Exception as e:
                end_time = time.time()
                times.append(end_time - start_time)
                errors.append(str(e))
        
        total_end = time.time()
        total_time = total_end - total_start
        end_resources = SystemResources.get_current()
        
        return self._create_benchmark_result(
            test_name, len(test_data), total_time, times, successes, errors,
            start_resources, end_resources
        )
    
    def _run_parallel_test(self, func: Callable, test_data: List[tuple], 
                          worker_count: int, test_name: str) -> BenchmarkResult:
        """Run parallel benchmark test."""
        self.log_info(f"Running parallel benchmark: {test_name}")
        
        start_resources = SystemResources.get_current()
        times = []
        successes = 0
        errors = []
        
        total_start = time.time()
        
        with ThreadPoolExecutor(max_workers=worker_count) as executor:
            futures = []
            for args, kwargs in test_data:
                future = executor.submit(func, *args, **kwargs)
                futures.append((future, time.time()))
            
            for future, submit_time in futures:
                try:
                    result = future.result()
                    end_time = time.time()
                    times.append(end_time - submit_time)
                    successes += 1
                except Exception as e:
                    end_time = time.time()
                    times.append(end_time - submit_time)
                    errors.append(str(e))
        
        total_end = time.time()
        total_time = total_end - total_start
        end_resources = SystemResources.get_current()
        
        return self._create_benchmark_result(
            test_name, len(test_data), total_time, times, successes, errors,
            start_resources, end_resources, {'worker_count': worker_count}
        )
    
    def _create_benchmark_result(self, test_name: str, iterations: int, total_time: float,
                               times: List[float], successes: int, errors: List[str],
                               start_resources: SystemResources, end_resources: SystemResources,
                               extra_metadata: dict = None) -> BenchmarkResult:
        """Create benchmark result from collected data."""
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            median_time = statistics.median(times)
            std_dev = statistics.stdev(times) if len(times) > 1 else 0.0
            throughput = iterations / total_time
        else:
            avg_time = min_time = max_time = median_time = std_dev = throughput = 0.0
        
        success_rate = successes / iterations if iterations > 0 else 0.0
        
        metadata = {
            'errors': errors,
            'error_count': len(errors)
        }
        if extra_metadata:
            metadata.update(extra_metadata)
        
        result = BenchmarkResult(
            test_name=test_name,
            iterations=iterations,
            total_time=total_time,
            avg_time=avg_time,
            min_time=min_time,
            max_time=max_time,
            median_time=median_time,
            std_dev=std_dev,
            throughput=throughput,
            success_rate=success_rate,
            system_resources={
                'start': {
                    'cpu_usage': start_resources.cpu_usage,
                    'memory_usage': start_resources.memory_usage_percent
                },
                'end': {
                    'cpu_usage': end_resources.cpu_usage,
                    'memory_usage': end_resources.memory_usage_percent
                }
            },
            metadata=metadata
        )
        
        self.results.append(result)
        print(f"✓ {test_name}: {avg_time:.4f}s avg, {throughput:.2f} ops/sec, {success_rate:.1%} success")
        
        return result
    
    def _print_parallel_comparison(self, results: List[BenchmarkResult]):
        """Print comparison of parallel benchmark results."""
        print("\n--- Parallel Processing Comparison ---")
        print(f"{'Test Name':<30} {'Throughput':<15} {'Speedup':<10} {'Efficiency':<12}")
        print("-" * 70)
        
        baseline = results[0]  # Sequential result
        baseline_throughput = baseline.throughput
        
        for result in results:
            speedup = result.throughput / baseline_throughput if baseline_throughput > 0 else 0
            
            # Calculate efficiency (speedup / worker_count)
            worker_count = result.metadata.get('worker_count', 1)
            efficiency = speedup / worker_count if worker_count > 0 else 0
            
            print(f"{result.test_name:<30} {result.throughput:<15.2f} {speedup:<10.2f}x {efficiency:<12.2%}")
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary of all benchmark results."""
        if not self.results:
            return {'message': 'No benchmark results available'}
        
        return {
            'total_tests': len(self.results),
            'avg_throughput': statistics.mean([r.throughput for r in self.results]),
            'avg_success_rate': statistics.mean([r.success_rate for r in self.results]),
            'best_performing_test': max(self.results, key=lambda r: r.throughput).test_name,
            'results': [r.to_dict() for r in self.results]
        }
    
    def clear_results(self):
        """Clear benchmark results."""
        self.results.clear()
