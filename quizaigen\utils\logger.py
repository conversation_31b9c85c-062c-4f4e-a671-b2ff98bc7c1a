"""
Enhanced Logging System for QuizAIGen

Provides comprehensive logging with file-based logging, automatic rotation,
structured logging, and performance tracking.
"""

import logging
import logging.handlers
import sys
import os
import json
from datetime import datetime, timezone
from typing import Optional, Dict, Any, Union
from pathlib import Path
from dataclasses import dataclass, asdict
import threading


@dataclass
class LogConfig:
    """Configuration for enhanced logging system."""
    log_dir: str = "logs"
    log_level: str = "INFO"
    console_logging: bool = True
    file_logging: bool = True
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    enable_json_logging: bool = False
    enable_performance_logging: bool = True
    separate_error_log: bool = True

    def __post_init__(self):
        """Ensure log directory exists."""
        Path(self.log_dir).mkdir(parents=True, exist_ok=True)


@dataclass
class LogEntry:
    """Structured log entry for JSON logging."""
    timestamp: str
    level: str
    logger_name: str
    module: str
    function: str
    line_number: int
    message: str
    extra_data: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)

    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), default=str)


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_entry = LogEntry(
            timestamp=datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat(),
            level=record.levelname,
            logger_name=record.name,
            module=record.module,
            function=record.funcName,
            line_number=record.lineno,
            message=record.getMessage(),
            extra_data=getattr(record, 'extra_data', {})
        )
        return log_entry.to_json()


class EnhancedFileHandler(logging.handlers.RotatingFileHandler):
    """Enhanced file handler with better error handling and metadata."""

    def __init__(self, filename: str, mode: str = 'a', maxBytes: int = 0,
                 backupCount: int = 0, encoding: str = None, delay: bool = False):
        """Initialize enhanced file handler."""
        # Ensure directory exists
        Path(filename).parent.mkdir(parents=True, exist_ok=True)
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay)

    def emit(self, record: logging.LogRecord) -> None:
        """Emit log record with enhanced error handling."""
        try:
            super().emit(record)
        except Exception as e:
            # Fallback to console if file logging fails
            sys.stderr.write(f"Logging error: {e}\n")
            sys.stderr.write(f"Failed to log: {record.getMessage()}\n")
            sys.stderr.flush()


class LoggerMixin:
    """
    Enhanced mixin class to add comprehensive logging capabilities to any class.

    Provides consistent logging methods with structured data support across the library.
    """

    def __init__(self, *args, **kwargs):
        """Initialize logger mixin."""
        super().__init__(*args, **kwargs)
        self._logger = None

    @property
    def logger(self) -> logging.Logger:
        """Get logger instance for this class."""
        if not hasattr(self, '_logger') or self._logger is None:
            self._logger = get_logger(self.__class__.__name__)
        return self._logger

    def log_debug(self, message: str, extra_data: Optional[Dict[str, Any]] = None, **kwargs) -> None:
        """Log debug message with optional structured data."""
        extra = {'extra_data': extra_data or {}}
        extra['extra_data'].update(kwargs)
        self.logger.debug(message, extra=extra)

    def log_info(self, message: str, extra_data: Optional[Dict[str, Any]] = None, **kwargs) -> None:
        """Log info message with optional structured data."""
        extra = {'extra_data': extra_data or {}}
        extra['extra_data'].update(kwargs)
        self.logger.info(message, extra=extra)

    def log_warning(self, message: str, extra_data: Optional[Dict[str, Any]] = None, **kwargs) -> None:
        """Log warning message with optional structured data."""
        extra = {'extra_data': extra_data or {}}
        extra['extra_data'].update(kwargs)
        self.logger.warning(message, extra=extra)

    def log_error(self, message: str, extra_data: Optional[Dict[str, Any]] = None,
                  exception: Optional[Exception] = None, **kwargs) -> None:
        """Log error message with optional structured data and exception info."""
        extra = {'extra_data': extra_data or {}}
        extra['extra_data'].update(kwargs)
        if exception:
            extra['extra_data']['exception_type'] = type(exception).__name__
            extra['extra_data']['exception_message'] = str(exception)
        self.logger.error(message, extra=extra, exc_info=exception is not None)

    def log_critical(self, message: str, extra_data: Optional[Dict[str, Any]] = None,
                     exception: Optional[Exception] = None, **kwargs) -> None:
        """Log critical message with optional structured data and exception info."""
        extra = {'extra_data': extra_data or {}}
        extra['extra_data'].update(kwargs)
        if exception:
            extra['extra_data']['exception_type'] = type(exception).__name__
            extra['extra_data']['exception_message'] = str(exception)
        self.logger.critical(message, extra=extra, exc_info=exception is not None)

    def log_performance(self, operation: str, duration: float,
                       extra_data: Optional[Dict[str, Any]] = None, **kwargs) -> None:
        """Log performance metrics."""
        perf_data = {
            'operation': operation,
            'duration_seconds': duration,
            'performance_log': True
        }
        if extra_data:
            perf_data.update(extra_data)
        perf_data.update(kwargs)

        extra = {'extra_data': perf_data}
        self.logger.info(f"Performance: {operation} completed in {duration:.4f}s", extra=extra)


# Global configuration
_log_config = LogConfig()
_loggers_lock = threading.Lock()
_configured_loggers = set()


def get_logger(name: str, level: Optional[str] = None, config: Optional[LogConfig] = None) -> logging.Logger:
    """
    Get a configured logger instance with enhanced file logging.

    Args:
        name: Logger name
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        config: Optional log configuration

    Returns:
        Configured logger instance with file and console handlers
    """
    if config is None:
        config = _log_config

    logger_name = f"quizaigen.{name}"
    logger = logging.getLogger(logger_name)

    # Thread-safe logger configuration
    with _loggers_lock:
        # Don't reconfigure if already done
        if logger_name in _configured_loggers:
            return logger

        # Set level
        log_level = level or config.log_level
        logger.setLevel(getattr(logging, log_level.upper()))

        # Clear any existing handlers
        logger.handlers.clear()

        # Create formatters
        if config.enable_json_logging:
            formatter = JSONFormatter()
        else:
            formatter = logging.Formatter(
                config.log_format,
                datefmt=config.date_format
            )

        # Console handler
        if config.console_logging:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, log_level.upper()))
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        # File handlers
        if config.file_logging:
            # Main log file
            main_log_file = Path(config.log_dir) / f"quizaigen_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = EnhancedFileHandler(
                str(main_log_file),
                maxBytes=config.max_file_size,
                backupCount=config.backup_count
            )
            file_handler.setLevel(getattr(logging, log_level.upper()))
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

            # Separate error log
            if config.separate_error_log:
                error_log_file = Path(config.log_dir) / f"quizaigen_errors_{datetime.now().strftime('%Y%m%d')}.log"
                error_handler = EnhancedFileHandler(
                    str(error_log_file),
                    maxBytes=config.max_file_size,
                    backupCount=config.backup_count
                )
                error_handler.setLevel(logging.ERROR)
                error_handler.setFormatter(formatter)
                logger.addHandler(error_handler)

            # Performance log (if enabled)
            if config.enable_performance_logging:
                perf_log_file = Path(config.log_dir) / f"quizaigen_performance_{datetime.now().strftime('%Y%m%d')}.log"
                perf_handler = EnhancedFileHandler(
                    str(perf_log_file),
                    maxBytes=config.max_file_size,
                    backupCount=config.backup_count
                )
                perf_handler.setLevel(logging.INFO)

                # Custom filter for performance logs
                class PerformanceFilter(logging.Filter):
                    def filter(self, record):
                        return hasattr(record, 'extra_data') and record.extra_data.get('performance_log', False)

                perf_handler.addFilter(PerformanceFilter())
                perf_handler.setFormatter(formatter)
                logger.addHandler(perf_handler)

        # Prevent propagation to root logger
        logger.propagate = False

        # Mark as configured
        _configured_loggers.add(logger_name)

    return logger


def configure_logging(
    log_dir: str = "logs",
    level: str = "INFO",
    console_logging: bool = True,
    file_logging: bool = True,
    max_file_size: int = 10 * 1024 * 1024,
    backup_count: int = 5,
    enable_json_logging: bool = False,
    enable_performance_logging: bool = True,
    separate_error_log: bool = True,
    log_format: Optional[str] = None
) -> LogConfig:
    """
    Configure enhanced global logging settings for QuizAIGen.

    Args:
        log_dir: Directory for log files
        level: Global log level
        console_logging: Enable console output
        file_logging: Enable file logging
        max_file_size: Maximum size per log file in bytes
        backup_count: Number of backup files to keep
        enable_json_logging: Use JSON format for structured logging
        enable_performance_logging: Enable separate performance log
        separate_error_log: Create separate error log file
        log_format: Custom log format string

    Returns:
        LogConfig instance with applied settings
    """
    global _log_config, _configured_loggers

    # Create new configuration
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"

    _log_config = LogConfig(
        log_dir=log_dir,
        log_level=level,
        console_logging=console_logging,
        file_logging=file_logging,
        max_file_size=max_file_size,
        backup_count=backup_count,
        log_format=log_format,
        enable_json_logging=enable_json_logging,
        enable_performance_logging=enable_performance_logging,
        separate_error_log=separate_error_log
    )

    # Clear configured loggers to force reconfiguration
    with _loggers_lock:
        _configured_loggers.clear()

        # Clear all existing QuizAIGen loggers
        for logger_name in list(logging.Logger.manager.loggerDict.keys()):
            if logger_name.startswith("quizaigen"):
                logger = logging.getLogger(logger_name)
                logger.handlers.clear()

    return _log_config


def setup_logging_for_session(session_id: str, operation: str = "general") -> str:
    """
    Set up logging for a specific session with unique log files.

    Args:
        session_id: Unique session identifier
        operation: Operation type for log file naming

    Returns:
        Path to the session log file
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    session_log_dir = Path(_log_config.log_dir) / f"sessions/{timestamp}_{session_id}"
    session_log_dir.mkdir(parents=True, exist_ok=True)

    session_log_file = session_log_dir / f"{operation}.log"

    # Create session-specific logger
    session_logger = logging.getLogger(f"quizaigen.session.{session_id}")
    session_logger.handlers.clear()

    # File handler for session
    session_handler = EnhancedFileHandler(
        str(session_log_file),
        maxBytes=_log_config.max_file_size,
        backupCount=_log_config.backup_count
    )

    if _log_config.enable_json_logging:
        formatter = JSONFormatter()
    else:
        formatter = logging.Formatter(
            _log_config.log_format,
            datefmt=_log_config.date_format
        )

    session_handler.setFormatter(formatter)
    session_logger.addHandler(session_handler)
    session_logger.setLevel(getattr(logging, _log_config.log_level.upper()))
    session_logger.propagate = False

    return str(session_log_file)


def get_logging_config() -> Dict[str, Any]:
    """
    Get current enhanced logging configuration.

    Returns:
        Dictionary with comprehensive logging configuration
    """
    return {
        "config": asdict(_log_config),
        "configured_loggers": list(_configured_loggers),
        "log_files": get_log_files(),
        "log_stats": get_log_statistics()
    }


def get_log_files() -> Dict[str, Any]:
    """
    Get information about current log files.

    Returns:
        Dictionary with log file information
    """
    log_dir = Path(_log_config.log_dir)
    if not log_dir.exists():
        return {"error": "Log directory does not exist"}

    log_files = {}

    for log_file in log_dir.rglob("*.log"):
        try:
            stat = log_file.stat()
            log_files[str(log_file)] = {
                "size_bytes": stat.st_size,
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "lines": count_log_lines(log_file)
            }
        except Exception as e:
            log_files[str(log_file)] = {"error": str(e)}

    return log_files


def get_log_statistics() -> Dict[str, Any]:
    """
    Get statistics about logging activity.

    Returns:
        Dictionary with logging statistics
    """
    log_dir = Path(_log_config.log_dir)
    if not log_dir.exists():
        return {"error": "Log directory does not exist"}

    stats = {
        "total_log_files": 0,
        "total_size_mb": 0,
        "log_levels": {"DEBUG": 0, "INFO": 0, "WARNING": 0, "ERROR": 0, "CRITICAL": 0},
        "recent_errors": [],
        "performance_logs": 0
    }

    for log_file in log_dir.rglob("*.log"):
        try:
            stats["total_log_files"] += 1
            stats["total_size_mb"] += log_file.stat().st_size / (1024 * 1024)

            # Analyze log content (last 100 lines for performance)
            if log_file.name.startswith("quizaigen_errors"):
                stats["recent_errors"].extend(get_recent_errors(log_file, limit=10))
            elif log_file.name.startswith("quizaigen_performance"):
                stats["performance_logs"] += count_log_lines(log_file)

        except Exception:
            continue

    stats["total_size_mb"] = round(stats["total_size_mb"], 2)
    return stats


def count_log_lines(log_file: Path) -> int:
    """Count lines in a log file efficiently."""
    try:
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            return sum(1 for _ in f)
    except Exception:
        return 0


def get_recent_errors(log_file: Path, limit: int = 10) -> list:
    """Get recent error entries from error log."""
    errors = []
    try:
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            for line in lines[-limit:]:
                if line.strip():
                    errors.append(line.strip())
    except Exception:
        pass
    return errors


def cleanup_old_logs(days_to_keep: int = 30) -> Dict[str, Any]:
    """
    Clean up log files older than specified days.

    Args:
        days_to_keep: Number of days to keep logs

    Returns:
        Dictionary with cleanup results
    """
    log_dir = Path(_log_config.log_dir)
    if not log_dir.exists():
        return {"error": "Log directory does not exist"}

    cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)

    cleanup_results = {
        "files_removed": 0,
        "space_freed_mb": 0,
        "errors": []
    }

    for log_file in log_dir.rglob("*.log*"):
        try:
            if log_file.stat().st_mtime < cutoff_time:
                size_mb = log_file.stat().st_size / (1024 * 1024)
                log_file.unlink()
                cleanup_results["files_removed"] += 1
                cleanup_results["space_freed_mb"] += size_mb
        except Exception as e:
            cleanup_results["errors"].append(f"Failed to remove {log_file}: {e}")

    cleanup_results["space_freed_mb"] = round(cleanup_results["space_freed_mb"], 2)
    return cleanup_results


def disable_logging() -> None:
    """Disable all QuizAIGen logging."""
    for logger_name in _configured_loggers:
        logging.getLogger(logger_name).disabled = True


def enable_logging() -> None:
    """Re-enable QuizAIGen logging."""
    for logger_name in _configured_loggers:
        logging.getLogger(logger_name).disabled = False


def log_system_info() -> None:
    """Log system information for debugging."""
    logger = get_logger("system")

    system_info = {
        "python_version": sys.version,
        "platform": os.name,
        "log_config": asdict(_log_config),
        "configured_loggers": len(_configured_loggers)
    }

    logger.info("System information logged", extra={'extra_data': system_info})


def create_operation_logger(operation_name: str, session_id: Optional[str] = None) -> logging.Logger:
    """
    Create a logger for a specific operation with dedicated log file.

    Args:
        operation_name: Name of the operation
        session_id: Optional session ID for grouping

    Returns:
        Configured logger for the operation
    """
    if session_id:
        logger_name = f"operation.{session_id}.{operation_name}"
        log_file = setup_logging_for_session(session_id, operation_name)
    else:
        logger_name = f"operation.{operation_name}"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_dir = Path(_log_config.log_dir) / "operations"
        log_dir.mkdir(parents=True, exist_ok=True)
        log_file = log_dir / f"{operation_name}_{timestamp}.log"

    return get_logger(logger_name)


# Context manager for operation logging
class LoggedOperation:
    """Context manager for logging operations with automatic timing."""

    def __init__(self, operation_name: str, logger: Optional[logging.Logger] = None,
                 extra_data: Optional[Dict[str, Any]] = None):
        """
        Initialize logged operation.

        Args:
            operation_name: Name of the operation
            logger: Optional logger instance
            extra_data: Optional extra data to log
        """
        self.operation_name = operation_name
        self.logger = logger or get_logger("operations")
        self.extra_data = extra_data or {}
        self.start_time = None
        self.success = False

    def __enter__(self):
        """Start the operation."""
        self.start_time = datetime.now()
        self.logger.info(
            f"Starting operation: {self.operation_name}",
            extra={'extra_data': {**self.extra_data, 'operation_start': True}}
        )
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """End the operation."""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()

        self.success = exc_type is None

        result_data = {
            **self.extra_data,
            'operation_end': True,
            'duration_seconds': duration,
            'success': self.success
        }

        if exc_type:
            result_data.update({
                'exception_type': exc_type.__name__,
                'exception_message': str(exc_val)
            })
            self.logger.error(
                f"Operation failed: {self.operation_name} ({duration:.4f}s)",
                extra={'extra_data': result_data},
                exc_info=True
            )
        else:
            self.logger.info(
                f"Operation completed: {self.operation_name} ({duration:.4f}s)",
                extra={'extra_data': result_data}
            )


# Default configuration - now with file logging enabled
configure_logging(
    level="INFO",
    file_logging=True,
    console_logging=True,
    enable_performance_logging=True
)
