# QuizAIGen Commercial Distribution Environment Configuration
# Copy this file to .env and update with your actual values

# Security Keys (REQUIRED - Generate secure keys for production)
JWT_SECRET_KEY=your-jwt-secret-key-here-generate-a-secure-one
ENCRYPTION_KEY=your-encryption-key-here-32-bytes-base64-encoded
QUIZAIGEN_FINGERPRINT_SALT=your-fingerprint-salt-here

# Database Configuration
DATABASE_URL=postgresql://license_user:secure_password@localhost:5432/license_db
POSTGRES_PASSWORD=secure_database_password

# Redis Configuration  
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/1

# Email Configuration (for customer communications)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# License Server Configuration
QUIZAIGEN_LICENSE_SERVER=https://api.quizaigen.com/license
PYPI_SERVER_URL=https://pypi.quizaigen.com

# Domain Configuration
DOMAIN_NAME=quizaigen.com
API_DOMAIN=api.quizaigen.com
PYPI_DOMAIN=pypi.quizaigen.com
PORTAL_DOMAIN=portal.quizaigen.com

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/quizaigen.crt
SSL_KEY_PATH=/etc/ssl/private/quizaigen.key

# Backup Configuration
BACKUP_S3_BUCKET=quizaigen-backups
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Monitoring Configuration
GRAFANA_PASSWORD=secure_grafana_password
PROMETHEUS_RETENTION=30d

# Alerting Configuration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
EMAIL_ALERTS_ENABLED=true
ALERT_EMAIL=<EMAIL>

# Application Configuration
DEBUG=false
ALLOWED_HOSTS=localhost,127.0.0.1,quizaigen.com,api.quizaigen.com
SECRET_KEY=your-django-secret-key-here

# License Validation Configuration
LICENSE_VALIDATION_TIMEOUT=30
HARDWARE_BINDING_ENABLED=true
USAGE_TRACKING_ENABLED=true
VALIDATION_CACHE_DURATION=3600

# Rate Limiting
API_RATE_LIMIT=1000/hour
DOWNLOAD_RATE_LIMIT=10/minute

# Feature Flags
ENABLE_SELF_SERVICE=true
ENABLE_USAGE_ANALYTICS=true
ENABLE_AUTOMATED_BILLING=false

# Development/Testing (set to false in production)
SKIP_LICENSE_VALIDATION=false
MOCK_PAYMENT_PROCESSING=false
ENABLE_DEBUG_LOGGING=false
