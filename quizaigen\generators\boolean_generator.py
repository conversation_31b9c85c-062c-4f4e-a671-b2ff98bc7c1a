"""
Boolean Question Generator

This module implements True/False question generation.
"""

import random
from typing import List, Dict, Any, Optional

from .base import BaseQuestionGenerator, Question
from ..utils.text_utils import extract_sentences, extract_keywords


class BooleanGenerator(BaseQuestionGenerator):
    """Generator for Boolean (True/False) Questions."""
    
    def _get_question_type(self) -> str:
        """Get the question type identifier."""
        return 'boolean'
    
    def _generate_questions_impl(self, text: str, num_questions: int, 
                               **kwargs) -> List[Question]:
        """
        Generate Boolean questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of generated Boolean questions
        """
        self.log_info("Starting Boolean question generation", extra_data={
            'text_length': len(text),
            'num_questions': num_questions,
            'kwargs': kwargs
        })

        # Extract sentences for question generation
        sentences = extract_sentences(text, min_length=15, max_length=150)

        if len(sentences) < num_questions:
            self.log_warning(f"Only {len(sentences)} suitable sentences found for {num_questions} questions")
            num_questions = len(sentences)

        self.log_info(f"Extracted {len(sentences)} sentences, generating {num_questions} Boolean questions")
        
        questions = []
        used_sentences = set()
        
        # Generate half true and half false questions
        true_questions_needed = num_questions // 2
        false_questions_needed = num_questions - true_questions_needed
        
        # Generate true questions (statements from text)
        for i in range(true_questions_needed):
            available_sentences = [s for s in sentences if s not in used_sentences]
            if not available_sentences:
                break
            
            sentence = random.choice(available_sentences)
            used_sentences.add(sentence)
            
            question = self._generate_true_question(sentence)
            if question and self._validate_generated_question(question):
                questions.append(question)
        
        # Generate false questions (modified statements)
        for i in range(false_questions_needed):
            available_sentences = [s for s in sentences if s not in used_sentences]
            if not available_sentences:
                break
            
            sentence = random.choice(available_sentences)
            used_sentences.add(sentence)
            
            question = self._generate_false_question(sentence)
            if question and self._validate_generated_question(question):
                questions.append(question)
        
        # Shuffle the questions so true/false are mixed
        random.shuffle(questions)
        
        return questions
    
    def _generate_true_question(self, sentence: str) -> Optional[Question]:
        """
        Generate a True question from a sentence.
        
        Args:
            sentence: Source sentence
        
        Returns:
            Generated Boolean question or None if generation fails
        """
        try:
            # Clean up the sentence for question format
            question_text = sentence.strip()
            
            # Remove trailing punctuation and add question format
            question_text = question_text.rstrip('.!?')
            
            # Add True/False prompt
            question_text = f"True or False: {question_text}"
            
            # Calculate confidence based on sentence quality
            confidence = self._calculate_confidence(sentence, is_true=True)
            
            return Question(
                question=question_text,
                type='boolean',
                answer='True',
                explanation=f"This statement is true based on the provided text.",
                difficulty='easy',
                confidence=confidence,
                metadata={
                    'source_sentence': sentence,
                    'generation_method': 'direct_statement',
                    'is_modified': False
                }
            )
            
        except Exception as e:
            self.log_error(f"Failed to generate true question: {str(e)}")
            return None
    
    def _generate_false_question(self, sentence: str) -> Optional[Question]:
        """
        Generate a False question by modifying a sentence.
        
        Args:
            sentence: Source sentence
        
        Returns:
            Generated Boolean question or None if generation fails
        """
        try:
            # Try different modification strategies
            modified_sentence = None
            modification_type = None
            
            # Strategy 1: Negate the statement
            modified_sentence, modification_type = self._negate_statement(sentence)
            
            # Strategy 2: Change numbers if negation didn't work
            if not modified_sentence:
                modified_sentence, modification_type = self._modify_numbers(sentence)
            
            # Strategy 3: Change keywords if other strategies didn't work
            if not modified_sentence:
                modified_sentence, modification_type = self._modify_keywords(sentence)
            
            if not modified_sentence:
                return None
            
            # Format as question
            question_text = modified_sentence.strip().rstrip('.!?')
            question_text = f"True or False: {question_text}"
            
            # Calculate confidence
            confidence = self._calculate_confidence(sentence, is_true=False)
            
            return Question(
                question=question_text,
                type='boolean',
                answer='False',
                explanation=f"This statement is false. The original text states something different.",
                difficulty='medium',
                confidence=confidence,
                metadata={
                    'source_sentence': sentence,
                    'modified_sentence': modified_sentence,
                    'generation_method': modification_type,
                    'is_modified': True
                }
            )
            
        except Exception as e:
            self.log_error(f"Failed to generate false question: {str(e)}")
            return None
    
    def _negate_statement(self, sentence: str) -> tuple[Optional[str], Optional[str]]:
        """
        Negate a statement to make it false.
        
        Args:
            sentence: Original sentence
        
        Returns:
            Tuple of (modified_sentence, modification_type)
        """
        # Simple negation patterns
        negation_patterns = [
            ('is ', 'is not '),
            ('are ', 'are not '),
            ('was ', 'was not '),
            ('were ', 'were not '),
            ('can ', 'cannot '),
            ('will ', 'will not '),
            ('has ', 'has not '),
            ('have ', 'have not '),
            ('does ', 'does not '),
            ('do ', 'do not ')
        ]
        
        sentence_lower = sentence.lower()
        
        for positive, negative in negation_patterns:
            if positive in sentence_lower:
                # Find the position and replace
                pos = sentence_lower.find(positive)
                if pos != -1:
                    modified = (sentence[:pos] + 
                              sentence[pos:pos+len(positive)].replace(positive, negative) + 
                              sentence[pos+len(positive):])
                    return modified, 'negation'
        
        return None, None
    
    def _modify_numbers(self, sentence: str) -> tuple[Optional[str], Optional[str]]:
        """
        Modify numbers in a sentence to make it false.
        
        Args:
            sentence: Original sentence
        
        Returns:
            Tuple of (modified_sentence, modification_type)
        """
        import re
        
        # Find numbers in the sentence
        number_pattern = r'\b\d+\b'
        numbers = re.findall(number_pattern, sentence)
        
        if numbers:
            # Replace the first number with a different one
            original_num = numbers[0]
            num_value = int(original_num)
            
            # Generate a different number
            if num_value > 10:
                new_num = str(num_value + random.randint(5, 20))
            elif num_value > 1:
                new_num = str(num_value + random.randint(1, 5))
            else:
                new_num = str(num_value + random.randint(2, 10))
            
            modified = re.sub(number_pattern, new_num, sentence, count=1)
            return modified, 'number_modification'
        
        return None, None
    
    def _modify_keywords(self, sentence: str) -> tuple[Optional[str], Optional[str]]:
        """
        Modify keywords in a sentence to make it false.
        
        Args:
            sentence: Original sentence
        
        Returns:
            Tuple of (modified_sentence, modification_type)
        """
        # Extract keywords from the sentence
        keywords = extract_keywords(sentence, top_k=3)
        
        if keywords:
            target_keyword = keywords[0]
            
            # Simple word replacements to make statement false
            replacements = {
                'increase': 'decrease',
                'decrease': 'increase',
                'high': 'low',
                'low': 'high',
                'large': 'small',
                'small': 'large',
                'fast': 'slow',
                'slow': 'fast',
                'hot': 'cold',
                'cold': 'hot',
                'good': 'bad',
                'bad': 'good',
                'easy': 'difficult',
                'difficult': 'easy'
            }
            
            for original, replacement in replacements.items():
                if original in sentence.lower():
                    modified = sentence.replace(original, replacement)
                    return modified, 'keyword_modification'
        
        return None, None
    
    def _calculate_confidence(self, sentence: str, is_true: bool) -> float:
        """
        Calculate confidence score for the generated question.
        
        Args:
            sentence: Source sentence
            is_true: Whether this is a true or false question
        
        Returns:
            Confidence score between 0 and 1
        """
        confidence = 0.6  # Base confidence
        
        # True questions are generally more reliable
        if is_true:
            confidence += 0.2
        
        # Longer sentences tend to be more informative
        if len(sentence.split()) >= 10:
            confidence += 0.1
        
        # Sentences with specific information are better
        if any(char.isdigit() for char in sentence):
            confidence += 0.1
        
        # Decrease confidence for very short or very long sentences
        if len(sentence) < 20 or len(sentence) > 120:
            confidence -= 0.1
        
        return max(0.0, min(1.0, confidence))
    
    def get_supported_parameters(self) -> Dict[str, Any]:
        """Get supported parameters for Boolean generation."""
        base_params = super().get_supported_parameters()
        
        boolean_params = {
            'true_false_ratio': {
                'type': 'float',
                'description': 'Ratio of true to false questions',
                'default': 0.5,
                'min': 0.0,
                'max': 1.0
            },
            'modification_strategy': {
                'type': 'str',
                'description': 'Strategy for creating false statements',
                'choices': ['negation', 'number_modification', 'keyword_modification', 'mixed'],
                'default': 'mixed'
            }
        }
        
        base_params.update(boolean_params)
        return base_params
