"""
Base Model Classes for QuizAIGen AI Integration

Provides abstract base classes and common functionality for AI model integration.
Supports tiered feature access based on licensing.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
import logging
from enum import Enum

from ..core.exceptions import ProcessingError, LicenseError
from ..utils.logger import LoggerMixin


class ModelTier(Enum):
    """Model tier enumeration for feature access control."""
    FREE = "free"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"


@dataclass
class ModelConfig:
    """Configuration for AI models."""
    model_name: str
    model_path: Optional[str] = None
    cache_dir: Optional[str] = None
    device: str = "cpu"  # cpu, cuda, mps
    max_length: int = 512
    batch_size: int = 1
    temperature: float = 0.7
    top_k: int = 50
    top_p: float = 0.9
    tier: ModelTier = ModelTier.FREE
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        if not 0.0 <= self.temperature <= 2.0:
            raise ValueError("temperature must be between 0.0 and 2.0")
        if not 0.0 <= self.top_p <= 1.0:
            raise ValueError("top_p must be between 0.0 and 1.0")


@dataclass
class ModelOutput:
    """Standard output format for model inference."""
    text: str
    confidence: float
    metadata: Dict[str, Any]
    processing_time: float
    model_name: str
    
    def __post_init__(self):
        """Validate output after initialization."""
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError("confidence must be between 0.0 and 1.0")
        if self.processing_time < 0:
            raise ValueError("processing_time must be non-negative")


class BaseModel(ABC, LoggerMixin):
    """
    Abstract base class for all AI models in QuizAIGen.
    
    Provides common functionality for model loading, inference, and caching.
    Implements tier-based feature access control.
    """
    
    def __init__(self, config: ModelConfig):
        """
        Initialize the base model.

        Args:
            config: Model configuration
        """
        super().__init__()  # Initialize LoggerMixin
        self.config = config
        self.model = None
        self.tokenizer = None
        self.is_loaded = False
        self._tier = config.tier

        self.log_info(f"Initialized {self.__class__.__name__} for {self._tier.value} tier")
    
    @abstractmethod
    def load_model(self) -> None:
        """Load the model and tokenizer."""
        pass
    
    @abstractmethod
    def _inference(self, input_text: str, **kwargs) -> ModelOutput:
        """
        Perform model inference.
        
        Args:
            input_text: Input text for inference
            **kwargs: Additional inference parameters
            
        Returns:
            Model output
        """
        pass
    
    def predict(self, input_text: str, **kwargs) -> ModelOutput:
        """
        Make a prediction using the model.
        
        Args:
            input_text: Input text
            **kwargs: Additional parameters
            
        Returns:
            Model output
            
        Raises:
            ProcessingError: If inference fails
            LicenseError: If tier access is not allowed
        """
        # Check tier access
        self._check_tier_access()
        
        # Ensure model is loaded
        if not self.is_loaded:
            self.load_model()
        
        try:
            return self._inference(input_text, **kwargs)
        except Exception as e:
            self.log_error(f"Inference failed: {str(e)}")
            raise ProcessingError(
                stage="model_inference",
                message=f"Model inference failed: {str(e)}"
            )
    
    def batch_predict(self, input_texts: List[str], **kwargs) -> List[ModelOutput]:
        """
        Make batch predictions.
        
        Args:
            input_texts: List of input texts
            **kwargs: Additional parameters
            
        Returns:
            List of model outputs
        """
        self._check_tier_access()
        
        if not self.is_loaded:
            self.load_model()
        
        outputs = []
        for text in input_texts:
            try:
                output = self._inference(text, **kwargs)
                outputs.append(output)
            except Exception as e:
                self.log_warning(f"Batch inference failed for text: {str(e)}")
                # Create fallback output
                outputs.append(ModelOutput(
                    text="",
                    confidence=0.0,
                    metadata={"error": str(e)},
                    processing_time=0.0,
                    model_name=self.config.model_name
                ))
        
        return outputs
    
    def _check_tier_access(self) -> None:
        """
        Check if current tier has access to this model.
        
        Raises:
            LicenseError: If access is not allowed
        """
        # This would be implemented with actual license checking
        # For now, we'll allow all tiers for development
        pass
    
    def unload_model(self) -> None:
        """Unload the model to free memory."""
        if self.model is not None:
            del self.model
            self.model = None
        
        if self.tokenizer is not None:
            del self.tokenizer
            self.tokenizer = None
        
        self.is_loaded = False
        self.log_info("Model unloaded")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the model.
        
        Returns:
            Model information dictionary
        """
        return {
            "model_name": self.config.model_name,
            "tier": self._tier.value,
            "device": self.config.device,
            "is_loaded": self.is_loaded,
            "max_length": self.config.max_length,
            "batch_size": self.config.batch_size
        }
    
    def __del__(self):
        """Cleanup when object is destroyed."""
        if self.is_loaded:
            self.unload_model()


class LightweightModel(BaseModel):
    """
    Lightweight model implementation for free tier.
    
    Uses simple rule-based or statistical approaches instead of heavy AI models.
    """
    
    def __init__(self, config: ModelConfig):
        """Initialize lightweight model."""
        config.tier = ModelTier.FREE
        super().__init__(config)
    
    def load_model(self) -> None:
        """Load lightweight model (no actual model loading needed)."""
        self.is_loaded = True
        self.log_info("Lightweight model ready")
    
    def _inference(self, input_text: str, **kwargs) -> ModelOutput:
        """
        Lightweight inference using rule-based approaches.
        
        Args:
            input_text: Input text
            **kwargs: Additional parameters
            
        Returns:
            Model output
        """
        import time
        start_time = time.time()
        
        # Simple rule-based processing
        processed_text = self._simple_processing(input_text)
        confidence = self._calculate_simple_confidence(input_text, processed_text)
        
        processing_time = time.time() - start_time
        
        return ModelOutput(
            text=processed_text,
            confidence=confidence,
            metadata={
                "method": "rule_based",
                "input_length": len(input_text)
            },
            processing_time=processing_time,
            model_name=self.config.model_name
        )
    
    def _simple_processing(self, text: str) -> str:
        """Simple text processing for lightweight model."""
        # This would implement simple rule-based processing
        return text.strip()
    
    def _calculate_simple_confidence(self, input_text: str, output_text: str) -> float:
        """Calculate confidence for simple processing."""
        # Simple confidence based on text length and basic heuristics
        if len(input_text) < 10:
            return 0.3
        elif len(input_text) < 50:
            return 0.6
        else:
            return 0.8
