"""
Content Appropriateness Filter for QuizAIGen

Provides content filtering to ensure questions are appropriate for educational contexts.
Filters inappropriate content, bias, and ensures educational suitability.
"""

from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum
import re

from ..generators.base import Question
from ..models.base_model import ModelTier
from ..core.exceptions import ProcessingError, LicenseError
from ..utils.logger import LoggerMixin


class ContentIssue(Enum):
    """Types of content issues that can be detected."""
    INAPPROPRIATE_LANGUAGE = "inappropriate_language"
    BIAS = "bias"
    VIOLENCE = "violence"
    DISCRIMINATION = "discrimination"
    ADULT_CONTENT = "adult_content"
    POLITICAL_BIAS = "political_bias"
    RELIGIOUS_BIAS = "religious_bias"
    FACTUAL_ERROR = "factual_error"
    UNCLEAR_CONTENT = "unclear_content"
    COPYRIGHT_CONCERN = "copyright_concern"


@dataclass
class ContentFilterResult:
    """Result of content filtering."""
    is_appropriate: bool
    confidence: float  # 0.0 to 1.0
    issues: List[ContentIssue]
    severity_score: float  # 0.0 (no issues) to 1.0 (severe issues)
    reasoning: str
    suggestions: List[str]  # Suggestions for improvement
    
    def to_dict(self) -> Dict:
        """Convert to dictionary format."""
        return {
            'is_appropriate': self.is_appropriate,
            'confidence': self.confidence,
            'issues': [issue.value for issue in self.issues],
            'severity_score': self.severity_score,
            'reasoning': self.reasoning,
            'suggestions': self.suggestions
        }


class ContentFilter(LoggerMixin):
    """
    AI-powered content appropriateness filter.
    
    Analyzes questions for:
    - Inappropriate language and content
    - Bias and discrimination
    - Educational suitability
    - Factual accuracy (basic checks)
    - Clarity and comprehensibility
    """
    
    def __init__(self, tier: ModelTier = ModelTier.FREE):
        """
        Initialize content filter.
        
        Args:
            tier: License tier for feature access
        """
        super().__init__()
        self.tier = tier
        
        # Inappropriate content patterns
        self.inappropriate_patterns = {
            ContentIssue.INAPPROPRIATE_LANGUAGE: [
                r'\b(damn|hell|crap)\b',  # Mild profanity
                r'\b(stupid|dumb|idiot)\b',  # Derogatory terms
            ],
            ContentIssue.VIOLENCE: [
                r'\b(kill|murder|violence|attack|weapon|gun|bomb)\b',
                r'\b(fight|war|battle|death|blood)\b'
            ],
            ContentIssue.ADULT_CONTENT: [
                r'\b(sex|sexual|adult|mature|explicit)\b',
                r'\b(alcohol|drug|smoking|gambling)\b'
            ],
            ContentIssue.DISCRIMINATION: [
                r'\b(race|racial|ethnic|gender|religion|disability)\b.*\b(inferior|superior|better|worse)\b',
                r'\b(stereotype|prejudice|discrimination)\b'
            ]
        }
        
        # Bias indicators
        self.bias_patterns = {
            ContentIssue.POLITICAL_BIAS: [
                r'\b(liberal|conservative|democrat|republican)\b.*\b(wrong|bad|evil|stupid)\b',
                r'\b(left|right).*wing\b.*\b(extremist|radical)\b'
            ],
            ContentIssue.RELIGIOUS_BIAS: [
                r'\b(christian|muslim|jewish|hindu|buddhist)\b.*\b(wrong|false|evil)\b',
                r'\b(religion|faith|belief)\b.*\b(stupid|ignorant|primitive)\b'
            ]
        }
        
        # Educational quality indicators
        self.quality_patterns = {
            'unclear_language': [
                r'\b(um|uh|like|you know|whatever)\b',
                r'\.{3,}',  # Multiple dots indicating unclear thought
                r'\?{2,}',  # Multiple question marks
            ],
            'informal_language': [
                r'\b(gonna|wanna|gotta|kinda|sorta)\b',
                r'\b(yeah|yep|nope|ok|okay)\b'
            ]
        }
        
        # Severity weights for different issues
        self.severity_weights = {
            ContentIssue.INAPPROPRIATE_LANGUAGE: 0.6,
            ContentIssue.VIOLENCE: 0.9,
            ContentIssue.ADULT_CONTENT: 0.8,
            ContentIssue.DISCRIMINATION: 0.9,
            ContentIssue.POLITICAL_BIAS: 0.4,
            ContentIssue.RELIGIOUS_BIAS: 0.4,
            ContentIssue.BIAS: 0.5,
            ContentIssue.FACTUAL_ERROR: 0.7,
            ContentIssue.UNCLEAR_CONTENT: 0.3,
            ContentIssue.COPYRIGHT_CONCERN: 0.6
        }
        
        self.log_info(f"Initialized ContentFilter with tier: {tier.value}")
    
    def filter_question(self, question: Question, context: str = "") -> ContentFilterResult:
        """
        Filter a question for content appropriateness.
        
        Args:
            question: Question to filter
            context: Optional context for filtering
            
        Returns:
            ContentFilterResult with appropriateness assessment
            
        Raises:
            ProcessingError: If filtering fails
        """
        try:
            self.log_debug(f"Filtering content for question: {question.question[:50]}...")
            
            issues = []
            severity_scores = []
            suggestions = []
            
            # Check for inappropriate content
            content_issues, content_suggestions = self._check_inappropriate_content(question.question)
            issues.extend(content_issues)
            suggestions.extend(content_suggestions)
            
            # Check for bias
            bias_issues, bias_suggestions = self._check_bias(question.question)
            issues.extend(bias_issues)
            suggestions.extend(bias_suggestions)
            
            # Check educational quality
            quality_issues, quality_suggestions = self._check_educational_quality(question)
            issues.extend(quality_issues)
            suggestions.extend(quality_suggestions)
            
            # Advanced checks for Premium/Enterprise tiers
            if self.tier in [ModelTier.PREMIUM, ModelTier.ENTERPRISE]:
                advanced_issues, advanced_suggestions = self._advanced_content_checks(question, context)
                issues.extend(advanced_issues)
                suggestions.extend(advanced_suggestions)
            
            # Calculate severity score
            if issues:
                severity_scores = [self.severity_weights.get(issue, 0.5) for issue in issues]
                severity_score = max(severity_scores)  # Use highest severity
            else:
                severity_score = 0.0
            
            # Determine appropriateness
            is_appropriate = severity_score < 0.7  # Threshold for appropriateness
            
            # Calculate confidence
            confidence = self._calculate_confidence(issues, severity_score)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(issues, severity_score, is_appropriate)
            
            result = ContentFilterResult(
                is_appropriate=is_appropriate,
                confidence=confidence,
                issues=issues,
                severity_score=severity_score,
                reasoning=reasoning,
                suggestions=list(set(suggestions))  # Remove duplicates
            )
            
            self.log_info(f"Content filter result: {'PASS' if is_appropriate else 'FAIL'} "
                         f"(severity: {severity_score:.2f})")
            return result
            
        except Exception as e:
            self.log_error(f"Content filtering failed: {str(e)}")
            raise ProcessingError(
                stage="content_filtering",
                message=f"Failed to filter question content: {str(e)}"
            )
    
    def filter_questions_batch(self, questions: List[Question], 
                             context: str = "") -> List[ContentFilterResult]:
        """
        Filter multiple questions for content appropriateness.
        
        Args:
            questions: List of questions to filter
            context: Optional context for filtering
            
        Returns:
            List of content filter results
        """
        results = []
        
        for question in questions:
            try:
                result = self.filter_question(question, context)
                results.append(result)
            except Exception as e:
                self.log_warning(f"Failed to filter question content: {str(e)}")
                # Create default result for failed cases
                default_result = ContentFilterResult(
                    is_appropriate=True,  # Default to appropriate if filtering fails
                    confidence=0.0,
                    issues=[],
                    severity_score=0.0,
                    reasoning="Content filtering failed - defaulting to appropriate",
                    suggestions=[]
                )
                results.append(default_result)
        
        return results
    
    def _check_inappropriate_content(self, text: str) -> tuple[List[ContentIssue], List[str]]:
        """Check for inappropriate language and content."""
        issues = []
        suggestions = []
        text_lower = text.lower()
        
        for issue_type, patterns in self.inappropriate_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    issues.append(issue_type)
                    if issue_type == ContentIssue.INAPPROPRIATE_LANGUAGE:
                        suggestions.append("Consider using more professional language")
                    elif issue_type == ContentIssue.VIOLENCE:
                        suggestions.append("Avoid violent or aggressive content")
                    elif issue_type == ContentIssue.ADULT_CONTENT:
                        suggestions.append("Ensure content is age-appropriate")
                    elif issue_type == ContentIssue.DISCRIMINATION:
                        suggestions.append("Remove discriminatory language or concepts")
                    break  # Only add each issue type once
        
        return issues, suggestions
    
    def _check_bias(self, text: str) -> tuple[List[ContentIssue], List[str]]:
        """Check for bias in content."""
        issues = []
        suggestions = []
        text_lower = text.lower()
        
        for issue_type, patterns in self.bias_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    issues.append(issue_type)
                    suggestions.append("Consider neutral, unbiased language")
                    break  # Only add each issue type once
        
        return issues, suggestions
    
    def _check_educational_quality(self, question: Question) -> tuple[List[ContentIssue], List[str]]:
        """Check educational quality and clarity."""
        issues = []
        suggestions = []
        text_lower = question.question.lower()
        
        # Check for unclear language
        for pattern in self.quality_patterns['unclear_language']:
            if re.search(pattern, text_lower):
                issues.append(ContentIssue.UNCLEAR_CONTENT)
                suggestions.append("Clarify the question wording")
                break
        
        # Check for informal language
        informal_count = 0
        for pattern in self.quality_patterns['informal_language']:
            informal_count += len(re.findall(pattern, text_lower))
        
        if informal_count > 2:  # Threshold for too much informal language
            issues.append(ContentIssue.UNCLEAR_CONTENT)
            suggestions.append("Use more formal, academic language")
        
        # Check question structure
        if not question.question.strip().endswith('?') and question.type != 'fill_blank':
            issues.append(ContentIssue.UNCLEAR_CONTENT)
            suggestions.append("Ensure questions end with proper punctuation")
        
        return issues, suggestions

    def _advanced_content_checks(self, question: Question, context: str) -> tuple[List[ContentIssue], List[str]]:
        """
        Advanced content checks for Premium/Enterprise tiers.

        This would use BERT/AI models for more sophisticated analysis.
        For now, provides rule-based approximation.
        """
        if self.tier == ModelTier.FREE:
            return [], []

        issues = []
        suggestions = []

        # Basic factual consistency check
        if self._has_potential_factual_issues(question.question):
            issues.append(ContentIssue.FACTUAL_ERROR)
            suggestions.append("Verify factual accuracy of the question")

        # Copyright concern detection (basic)
        if self._has_copyright_concerns(question.question):
            issues.append(ContentIssue.COPYRIGHT_CONCERN)
            suggestions.append("Ensure content doesn't infringe on copyrights")

        return issues, suggestions

    def _has_potential_factual_issues(self, text: str) -> bool:
        """Basic check for potential factual issues."""
        # Look for absolute statements that might be incorrect
        absolute_patterns = [
            r'\b(always|never|all|none|every|no one)\b',
            r'\b(first|last|only|best|worst)\b.*\b(in the world|ever|of all time)\b'
        ]

        text_lower = text.lower()
        for pattern in absolute_patterns:
            if re.search(pattern, text_lower):
                return True

        return False

    def _has_copyright_concerns(self, text: str) -> bool:
        """Basic check for potential copyright issues."""
        # Look for references to copyrighted material
        copyright_indicators = [
            r'\b(quote|excerpt|passage)\b.*\b(from|by)\b',
            r'\b(according to|as stated in)\b.*\b(book|article|paper)\b',
            r'©|\(c\)|copyright'
        ]

        text_lower = text.lower()
        for pattern in copyright_indicators:
            if re.search(pattern, text_lower):
                return True

        return False

    def _calculate_confidence(self, issues: List[ContentIssue], severity_score: float) -> float:
        """Calculate confidence in the filtering result."""
        if not issues:
            return 0.9  # High confidence when no issues found

        # Lower confidence for edge cases (moderate severity)
        if 0.3 <= severity_score <= 0.7:
            return 0.6

        # High confidence for clear cases (very low or very high severity)
        return 0.9

    def _generate_reasoning(self, issues: List[ContentIssue],
                          severity_score: float, is_appropriate: bool) -> str:
        """Generate human-readable reasoning for the filtering result."""
        if not issues:
            return "No content issues detected. Question appears appropriate for educational use."

        issue_descriptions = {
            ContentIssue.INAPPROPRIATE_LANGUAGE: "inappropriate language",
            ContentIssue.VIOLENCE: "violent content",
            ContentIssue.ADULT_CONTENT: "adult content",
            ContentIssue.DISCRIMINATION: "discriminatory content",
            ContentIssue.POLITICAL_BIAS: "political bias",
            ContentIssue.RELIGIOUS_BIAS: "religious bias",
            ContentIssue.BIAS: "bias",
            ContentIssue.FACTUAL_ERROR: "potential factual errors",
            ContentIssue.UNCLEAR_CONTENT: "unclear content",
            ContentIssue.COPYRIGHT_CONCERN: "copyright concerns"
        }

        issue_list = [issue_descriptions.get(issue, issue.value) for issue in issues]

        if len(issue_list) == 1:
            issue_text = issue_list[0]
        elif len(issue_list) == 2:
            issue_text = f"{issue_list[0]} and {issue_list[1]}"
        else:
            issue_text = f"{', '.join(issue_list[:-1])}, and {issue_list[-1]}"

        result_text = "appropriate" if is_appropriate else "inappropriate"

        reasoning = f"Detected {issue_text}. Severity score: {severity_score:.2f}. "
        reasoning += f"Question assessed as {result_text} for educational use."

        return reasoning

    def get_appropriate_questions(self, questions: List[Question],
                                context: str = "") -> List[Question]:
        """
        Filter and return only appropriate questions.

        Args:
            questions: List of questions to filter
            context: Optional context for filtering

        Returns:
            List of appropriate questions with filter metadata
        """
        results = self.filter_questions_batch(questions, context)

        appropriate_questions = []
        for question, result in zip(questions, results):
            if result.is_appropriate:
                # Add filter metadata to question
                if question.metadata is None:
                    question.metadata = {}
                question.metadata['content_filter'] = result.to_dict()
                appropriate_questions.append(question)

        self.log_info(f"Filtered {len(appropriate_questions)} appropriate questions "
                     f"from {len(questions)} total questions")
        return appropriate_questions

    def get_content_issues_summary(self, questions: List[Question]) -> Dict[str, int]:
        """
        Get summary of content issues across questions.

        Args:
            questions: List of questions to analyze

        Returns:
            Dictionary with issue type counts
        """
        results = self.filter_questions_batch(questions)

        issue_counts = {issue.value: 0 for issue in ContentIssue}

        for result in results:
            for issue in result.issues:
                issue_counts[issue.value] += 1

        # Remove zero counts for cleaner output
        return {issue: count for issue, count in issue_counts.items() if count > 0}
