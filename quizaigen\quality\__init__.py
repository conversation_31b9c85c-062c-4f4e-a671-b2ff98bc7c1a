"""
QuizAIGen Quality Control Module

This module provides advanced quality control features for question generation,
including difficulty assessment, content filtering, <PERSON>'s taxonomy classification,
and advanced duplicate detection.
"""

from .difficulty_assessor import DifficultyAssessor, DifficultyLevel, DifficultyAssessment
from .content_filter import ContentFilter, ContentFilterResult, ContentIssue
from .bloom_classifier import BloomClassifier, BloomLevel, BloomClassification
from .duplicate_detector import DuplicateDetector, DuplicateMatch, DuplicateGroup, SimilarityMethod

__all__ = [
    'DifficultyAssessor',
    'DifficultyLevel',
    'DifficultyAssessment',
    'ContentFilter',
    'ContentFilterResult',
    'ContentIssue',
    'BloomClassifier',
    'BloomLevel',
    'BloomClassification',
    'DuplicateDetector',
    'DuplicateMatch',
    'DuplicateGroup',
    'SimilarityMethod'
]
