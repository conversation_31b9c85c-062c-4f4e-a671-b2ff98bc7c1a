# QuizAIGen Customer Integration Guide

## Overview

This guide helps customers integrate QuizAIGen commercial packages into their applications and workflows.

## Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager
- Valid QuizAIGen license key

### Package Installation

#### Option 1: Private PyPI Server (Recommended)

```bash
# Configure pip to use private PyPI server
pip config set global.extra-index-url https://pypi.quizaigen.com/simple/

# Install with authentication
pip install quizaigen-premium --trusted-host pypi.quizaigen.com
```

#### Option 2: Direct Wheel Installation

```bash
# Download package from customer portal
# Extract and install
pip install quizaigen_premium-1.0.0-py3-none-any.whl
```

#### Option 3: Automated Installation Script

```bash
# Use provided installation script
python install.py
```

### License Setup

#### Method 1: Environment Variable

```bash
export QUIZAIGEN_LICENSE_KEY="your-license-key-here"
```

#### Method 2: License File

```bash
# Create license directory
mkdir -p ~/.quizaigen

# Save license key
echo "your-license-key-here" > ~/.quizaigen/license.key
```

#### Method 3: Programmatic Setup

```python
import quizaigen

# Initialize with license
quizaigen.initialize_license("your-license-key-here")
```

## Quick Start

### Basic Usage

```python
import quizaigen

# Check license status
print(quizaigen.get_license_info())

# Create question generator
generator = quizaigen.QuestionGenerator()

# Generate MCQ questions
text = """
Python is a high-level programming language created by Guido van Rossum.
It emphasizes code readability and simplicity. Python supports multiple
programming paradigms including procedural, object-oriented, and functional.
"""

questions = generator.generate_mcq(text, num_questions=5)

for question in questions:
    print(f"Q: {question.question}")
    for i, option in enumerate(question.options, 1):
        print(f"  {i}. {option}")
    print(f"Answer: {question.answer}\n")
```

### Advanced Features (Premium/Enterprise)

```python
# Fill-in-blank questions (Premium+)
fill_blank_questions = generator.generate_fill_blank(text, num_questions=3)

# Batch processing (Premium+)
batch_processor = quizaigen.BatchProcessor()
results = batch_processor.process_multiple_texts([text1, text2, text3])

# Advanced export formats (Premium+)
export_manager = quizaigen.ExportManager()
export_manager.export_to_qti(questions, "output.xml")
export_manager.export_to_moodle(questions, "moodle_import.xml")

# Multilingual support (Enterprise)
multilingual_generator = quizaigen.MultilingualGenerator()
spanish_questions = multilingual_generator.generate_mcq(
    text, 
    num_questions=5, 
    target_language="es"
)
```

## Integration Patterns

### Web Application Integration

#### Flask Example

```python
from flask import Flask, request, jsonify
import quizaigen

app = Flask(__name__)

# Initialize QuizAIGen
quizaigen.initialize_license(os.getenv('QUIZAIGEN_LICENSE_KEY'))
generator = quizaigen.QuestionGenerator()

@app.route('/generate-questions', methods=['POST'])
def generate_questions():
    try:
        data = request.json
        text = data['text']
        question_type = data.get('type', 'mcq')
        num_questions = data.get('num_questions', 5)
        
        if question_type == 'mcq':
            questions = generator.generate_mcq(text, num_questions)
        elif question_type == 'boolean':
            questions = generator.generate_boolean(text, num_questions)
        elif question_type == 'faq':
            questions = generator.generate_faq(text, num_questions)
        else:
            return jsonify({'error': 'Unsupported question type'}), 400
        
        return jsonify({
            'questions': [q.to_dict() for q in questions],
            'count': len(questions)
        })
        
    except quizaigen.LicenseError as e:
        return jsonify({'error': str(e), 'license_required': True}), 403
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
```

#### Django Example

```python
# views.py
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
import quizaigen

# Initialize QuizAIGen
quizaigen.initialize_license(settings.QUIZAIGEN_LICENSE_KEY)
generator = quizaigen.QuestionGenerator()

@csrf_exempt
@require_http_methods(["POST"])
def generate_questions(request):
    try:
        data = json.loads(request.body)
        text = data['text']
        question_type = data.get('type', 'mcq')
        num_questions = data.get('num_questions', 5)
        
        # Check user permissions
        if not request.user.has_perm('app.generate_questions'):
            return JsonResponse({'error': 'Permission denied'}, status=403)
        
        # Generate questions
        if question_type == 'mcq':
            questions = generator.generate_mcq(text, num_questions)
        elif question_type == 'fill_blank':
            # Check premium feature access
            if not request.user.profile.has_premium_access:
                return JsonResponse({
                    'error': 'Premium feature requires upgrade',
                    'upgrade_required': True
                }, status=403)
            questions = generator.generate_fill_blank(text, num_questions)
        
        return JsonResponse({
            'questions': [q.to_dict() for q in questions],
            'count': len(questions),
            'license_tier': quizaigen.get_current_tier().value
        })
        
    except quizaigen.LicenseError as e:
        return JsonResponse({'error': str(e), 'license_error': True}, status=403)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
```

### API Integration

#### RESTful API Wrapper

```python
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional
import quizaigen

app = FastAPI(title="QuizAIGen API Wrapper")

# Initialize QuizAIGen
quizaigen.initialize_license(os.getenv('QUIZAIGEN_LICENSE_KEY'))
generator = quizaigen.QuestionGenerator()

class QuestionRequest(BaseModel):
    text: str
    question_type: str = "mcq"
    num_questions: int = 5
    difficulty: Optional[str] = None

class QuestionResponse(BaseModel):
    questions: List[dict]
    count: int
    license_tier: str

@app.post("/generate", response_model=QuestionResponse)
async def generate_questions(request: QuestionRequest):
    try:
        if request.question_type == 'mcq':
            questions = generator.generate_mcq(
                request.text, 
                request.num_questions,
                difficulty=request.difficulty
            )
        elif request.question_type == 'boolean':
            questions = generator.generate_boolean(request.text, request.num_questions)
        elif request.question_type == 'faq':
            questions = generator.generate_faq(request.text, request.num_questions)
        elif request.question_type == 'fill_blank':
            questions = generator.generate_fill_blank(request.text, request.num_questions)
        else:
            raise HTTPException(status_code=400, detail="Unsupported question type")
        
        return QuestionResponse(
            questions=[q.to_dict() for q in questions],
            count=len(questions),
            license_tier=quizaigen.get_current_tier().value
        )
        
    except quizaigen.LicenseError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/license-info")
async def get_license_info():
    return quizaigen.get_license_info()

@app.get("/health")
async def health_check():
    return {"status": "healthy", "license_valid": quizaigen.is_licensed()}
```

### Batch Processing

#### Large Scale Processing

```python
import quizaigen
from concurrent.futures import ThreadPoolExecutor
import pandas as pd

# Initialize
quizaigen.initialize_license(license_key)
batch_processor = quizaigen.BatchProcessor()

def process_document(doc_data):
    """Process a single document."""
    try:
        text = doc_data['content']
        doc_id = doc_data['id']
        
        # Generate questions
        questions = batch_processor.generate_questions(
            text=text,
            question_types=['mcq', 'boolean', 'faq'],
            num_questions_per_type=5
        )
        
        return {
            'doc_id': doc_id,
            'success': True,
            'questions': questions,
            'count': len(questions)
        }
        
    except Exception as e:
        return {
            'doc_id': doc_data['id'],
            'success': False,
            'error': str(e)
        }

# Load documents
documents = pd.read_csv('documents.csv').to_dict('records')

# Process in parallel
with ThreadPoolExecutor(max_workers=4) as executor:
    results = list(executor.map(process_document, documents))

# Save results
successful_results = [r for r in results if r['success']]
failed_results = [r for r in results if not r['success']]

print(f"Processed: {len(successful_results)} successful, {len(failed_results)} failed")
```

### Document Processing Integration

```python
import quizaigen
from pathlib import Path

# Initialize document processor
doc_processor = quizaigen.DocumentProcessor()

def process_uploaded_file(file_path):
    """Process uploaded document file."""
    try:
        # Extract text from document
        if file_path.suffix.lower() == '.pdf':
            text = doc_processor.extract_from_pdf(file_path)
        elif file_path.suffix.lower() in ['.docx', '.doc']:
            text = doc_processor.extract_from_word(file_path)
        elif file_path.suffix.lower() == '.txt':
            text = file_path.read_text(encoding='utf-8')
        else:
            raise ValueError(f"Unsupported file type: {file_path.suffix}")
        
        # Generate questions
        generator = quizaigen.QuestionGenerator()
        questions = generator.generate_mcq(text, num_questions=10)
        
        return {
            'success': True,
            'text_length': len(text),
            'questions': [q.to_dict() for q in questions],
            'file_name': file_path.name
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'file_name': file_path.name
        }

# Example usage
file_path = Path('uploaded_document.pdf')
result = process_uploaded_file(file_path)
```

## Error Handling

### License Errors

```python
import quizaigen

try:
    generator = quizaigen.QuestionGenerator()
    questions = generator.generate_fill_blank(text, num_questions=5)
    
except quizaigen.LicenseError as e:
    if "expired" in str(e).lower():
        print("License has expired. Please renew your license.")
    elif "feature" in str(e).lower():
        print("This feature requires a higher license tier.")
    else:
        print(f"License error: {e}")
        
except Exception as e:
    print(f"Unexpected error: {e}")
```

### Graceful Degradation

```python
def generate_questions_with_fallback(text, question_type='mcq', num_questions=5):
    """Generate questions with graceful degradation."""
    try:
        generator = quizaigen.QuestionGenerator()
        
        if question_type == 'fill_blank':
            # Try premium feature first
            try:
                return generator.generate_fill_blank(text, num_questions)
            except quizaigen.LicenseError:
                # Fallback to basic MCQ
                print("Fill-blank not available, falling back to MCQ")
                return generator.generate_mcq(text, num_questions)
        
        elif question_type == 'mcq':
            return generator.generate_mcq(text, num_questions)
        
        elif question_type == 'boolean':
            return generator.generate_boolean(text, num_questions)
            
    except quizaigen.LicenseError as e:
        print(f"License error: {e}")
        return []
    except Exception as e:
        print(f"Generation error: {e}")
        return []
```

## Performance Optimization

### Caching

```python
import functools
import hashlib
import pickle
from pathlib import Path

# Simple file-based cache
def cache_questions(cache_dir="./question_cache"):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(text, *args, **kwargs):
            # Create cache key
            cache_key = hashlib.md5(
                f"{text}{args}{kwargs}".encode()
            ).hexdigest()
            
            cache_path = Path(cache_dir) / f"{cache_key}.pkl"
            cache_path.parent.mkdir(exist_ok=True)
            
            # Check cache
            if cache_path.exists():
                with open(cache_path, 'rb') as f:
                    return pickle.load(f)
            
            # Generate and cache
            result = func(text, *args, **kwargs)
            with open(cache_path, 'wb') as f:
                pickle.dump(result, f)
            
            return result
        return wrapper
    return decorator

@cache_questions()
def generate_cached_questions(text, question_type='mcq', num_questions=5):
    generator = quizaigen.QuestionGenerator()
    if question_type == 'mcq':
        return generator.generate_mcq(text, num_questions)
    # ... other types
```

### Async Processing

```python
import asyncio
import quizaigen

async def generate_questions_async(text_list):
    """Generate questions asynchronously."""
    
    def generate_sync(text):
        generator = quizaigen.QuestionGenerator()
        return generator.generate_mcq(text, num_questions=5)
    
    loop = asyncio.get_event_loop()
    
    # Run in thread pool to avoid blocking
    tasks = [
        loop.run_in_executor(None, generate_sync, text)
        for text in text_list
    ]
    
    results = await asyncio.gather(*tasks)
    return results

# Usage
async def main():
    texts = ["Text 1...", "Text 2...", "Text 3..."]
    results = await generate_questions_async(texts)
    print(f"Generated questions for {len(results)} texts")

# Run
asyncio.run(main())
```

## Troubleshooting

### Common Issues

1. **License validation fails**
   ```python
   # Check license status
   print(quizaigen.get_license_info())
   
   # Verify license key format
   license_key = "your-license-key"
   if not license_key.startswith('eyJ'):
       print("Invalid license key format")
   ```

2. **Import errors**
   ```bash
   # Check installation
   pip show quizaigen-premium
   
   # Verify Python path
   python -c "import sys; print(sys.path)"
   ```

3. **Performance issues**
   ```python
   # Enable debug logging
   import logging
   logging.basicConfig(level=logging.DEBUG)
   
   # Check system resources
   import psutil
   print(f"Memory usage: {psutil.virtual_memory().percent}%")
   print(f"CPU usage: {psutil.cpu_percent()}%")
   ```

### Getting Help

- **Documentation**: https://docs.quizaigen.com
- **Support Portal**: https://support.quizaigen.com
- **Email Support**: <EMAIL>
- **Community Forum**: https://community.quizaigen.com

### License Management

```python
# Check license expiration
license_info = quizaigen.get_license_info()
if license_info.get('expires'):
    from datetime import datetime
    expires = datetime.fromisoformat(license_info['expires'])
    days_left = (expires - datetime.now()).days
    print(f"License expires in {days_left} days")

# Check feature availability
features = license_info.get('features', [])
print(f"Available features: {', '.join(features)}")
```

## Best Practices

1. **Always handle license errors gracefully**
2. **Cache results when possible**
3. **Use batch processing for large datasets**
4. **Monitor license usage and expiration**
5. **Keep the library updated**
6. **Follow security best practices for license keys**

## Examples Repository

Complete examples are available at: https://github.com/quizaigen/integration-examples
