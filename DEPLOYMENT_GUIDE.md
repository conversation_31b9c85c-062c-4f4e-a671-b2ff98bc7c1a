# QuizAIGen Deployment Guide

## Overview

This guide covers deployment strategies for QuizAIGen in various environments, from development to production scale.

## Table of Contents

1. [Development Setup](#development-setup)
2. [Production Deployment](#production-deployment)
3. [Docker Deployment](#docker-deployment)
4. [Cloud Deployment](#cloud-deployment)
5. [Monitoring & Logging](#monitoring--logging)
6. [Security Configuration](#security-configuration)
7. [Performance Optimization](#performance-optimization)

---

## Development Setup

### Local Development Environment

```bash
# Clone the repository
git clone https://github.com/your-org/quizaigen-api.git
cd quizaigen-api

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
pip install -e .

# Set environment variables
export QUIZAIGEN_LICENSE_KEY="your-license-key"
export QUIZAIGEN_TIER="premium"
export FLASK_ENV="development"
export FLASK_DEBUG=1

# Run development server
python app.py
```

### Environment Variables

Create a `.env` file:

```env
# QuizAIGen Configuration
QUIZAIGEN_LICENSE_KEY=your-license-key-here
QUIZAIGEN_TIER=premium
QUIZAIGEN_MAX_QUESTIONS=100
QUIZAIGEN_TIMEOUT=30

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true
API_SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=postgresql://user:pass@localhost:5432/quizaigen
REDIS_URL=redis://localhost:6379/0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/var/log/quizaigen/app.log

# Security Configuration
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=100
```

---

## Production Deployment

### System Requirements

**Minimum Requirements:**
- CPU: 2 cores
- RAM: 4GB
- Storage: 20GB SSD
- Python: 3.8+

**Recommended for Production:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 50GB+ SSD
- Load Balancer
- Redis for caching
- PostgreSQL for data storage

### Production Setup

```bash
# Create production user
sudo useradd -m -s /bin/bash quizaigen
sudo su - quizaigen

# Install application
git clone https://github.com/your-org/quizaigen-api.git
cd quizaigen-api
python -m venv venv
source venv/bin/activate
pip install -r requirements-prod.txt

# Create configuration
sudo mkdir -p /etc/quizaigen
sudo cp config/production.yaml /etc/quizaigen/config.yaml

# Create systemd service
sudo cp deploy/quizaigen.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable quizaigen
sudo systemctl start quizaigen
```

### Systemd Service Configuration

Create `/etc/systemd/system/quizaigen.service`:

```ini
[Unit]
Description=QuizAIGen API Server
After=network.target

[Service]
Type=exec
User=quizaigen
Group=quizaigen
WorkingDirectory=/home/<USER>/quizaigen-api
Environment=PATH=/home/<USER>/quizaigen-api/venv/bin
EnvironmentFile=/etc/quizaigen/environment
ExecStart=/home/<USER>/quizaigen-api/venv/bin/gunicorn --config gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### Gunicorn Configuration

Create `gunicorn.conf.py`:

```python
import multiprocessing

# Server socket
bind = "0.0.0.0:8000"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers
max_requests = 1000
max_requests_jitter = 50
preload_app = True

# Logging
accesslog = "/var/log/quizaigen/access.log"
errorlog = "/var/log/quizaigen/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "quizaigen-api"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190
```

---

## Docker Deployment

### Dockerfile

```dockerfile
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Install QuizAIGen
RUN pip install -e .

# Create non-root user
RUN useradd -m -u 1000 quizaigen && chown -R quizaigen:quizaigen /app
USER quizaigen

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["gunicorn", "--config", "gunicorn.conf.py", "app:app"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  quizaigen-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - QUIZAIGEN_LICENSE_KEY=${QUIZAIGEN_LICENSE_KEY}
      - QUIZAIGEN_TIER=premium
      - DATABASE_URL=**************************************/quizaigen
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/var/log/quizaigen
    restart: unless-stopped

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=quizaigen
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - quizaigen-api
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### Nginx Configuration

```nginx
upstream quizaigen_api {
    server quizaigen-api:8000;
}

server {
    listen 80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    location / {
        proxy_pass http://quizaigen_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    location /health {
        proxy_pass http://quizaigen_api/health;
        access_log off;
    }
}
```

---

## Cloud Deployment

### AWS Deployment

#### Using AWS ECS

```yaml
# ecs-task-definition.json
{
    "family": "quizaigen-api",
    "networkMode": "awsvpc",
    "requiresCompatibilities": ["FARGATE"],
    "cpu": "512",
    "memory": "1024",
    "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
    "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
    "containerDefinitions": [
        {
            "name": "quizaigen-api",
            "image": "your-account.dkr.ecr.region.amazonaws.com/quizaigen-api:latest",
            "portMappings": [
                {
                    "containerPort": 8000,
                    "protocol": "tcp"
                }
            ],
            "environment": [
                {
                    "name": "QUIZAIGEN_TIER",
                    "value": "premium"
                }
            ],
            "secrets": [
                {
                    "name": "QUIZAIGEN_LICENSE_KEY",
                    "valueFrom": "arn:aws:secretsmanager:region:account:secret:quizaigen-license"
                }
            ],
            "logConfiguration": {
                "logDriver": "awslogs",
                "options": {
                    "awslogs-group": "/ecs/quizaigen-api",
                    "awslogs-region": "us-east-1",
                    "awslogs-stream-prefix": "ecs"
                }
            },
            "healthCheck": {
                "command": ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"],
                "interval": 30,
                "timeout": 5,
                "retries": 3,
                "startPeriod": 60
            }
        }
    ]
}
```

#### Terraform Configuration

```hcl
# main.tf
provider "aws" {
  region = var.aws_region
}

# ECS Cluster
resource "aws_ecs_cluster" "quizaigen" {
  name = "quizaigen-cluster"
  
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

# Application Load Balancer
resource "aws_lb" "quizaigen" {
  name               = "quizaigen-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets           = var.public_subnet_ids

  enable_deletion_protection = false
}

# ECS Service
resource "aws_ecs_service" "quizaigen" {
  name            = "quizaigen-service"
  cluster         = aws_ecs_cluster.quizaigen.id
  task_definition = aws_ecs_task_definition.quizaigen.arn
  desired_count   = 2
  launch_type     = "FARGATE"

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets         = var.private_subnet_ids
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.quizaigen.arn
    container_name   = "quizaigen-api"
    container_port   = 8000
  }

  depends_on = [aws_lb_listener.quizaigen]
}
```

### Google Cloud Platform

#### Using Cloud Run

```yaml
# cloudrun.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: quizaigen-api
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "1000m"
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      containers:
      - image: gcr.io/your-project/quizaigen-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: QUIZAIGEN_TIER
          value: "premium"
        - name: QUIZAIGEN_LICENSE_KEY
          valueFrom:
            secretKeyRef:
              name: quizaigen-secrets
              key: license-key
        resources:
          limits:
            cpu: "1000m"
            memory: "2Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
```

### Azure Deployment

#### Using Container Instances

```yaml
# azure-container-instance.yaml
apiVersion: 2019-12-01
location: eastus
name: quizaigen-api
properties:
  containers:
  - name: quizaigen-api
    properties:
      image: your-registry.azurecr.io/quizaigen-api:latest
      resources:
        requests:
          cpu: 1
          memoryInGb: 2
      ports:
      - port: 8000
        protocol: TCP
      environmentVariables:
      - name: QUIZAIGEN_TIER
        value: premium
      - name: QUIZAIGEN_LICENSE_KEY
        secureValue: your-license-key
  osType: Linux
  restartPolicy: Always
  ipAddress:
    type: Public
    ports:
    - protocol: TCP
      port: 8000
tags:
  environment: production
  service: quizaigen-api
type: Microsoft.ContainerInstance/containerGroups
```

---

## Monitoring & Logging

### Application Monitoring

```python
# monitoring.py
import logging
import time
from functools import wraps
from prometheus_client import Counter, Histogram, generate_latest

# Metrics
REQUEST_COUNT = Counter('quizaigen_requests_total', 'Total requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('quizaigen_request_duration_seconds', 'Request duration')
QUESTION_GENERATION_COUNT = Counter('quizaigen_questions_generated_total', 'Questions generated', ['type'])

def monitor_requests(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        try:
            result = f(*args, **kwargs)
            REQUEST_COUNT.labels(method=request.method, endpoint=request.endpoint, status=200).inc()
            return result
        except Exception as e:
            REQUEST_COUNT.labels(method=request.method, endpoint=request.endpoint, status=500).inc()
            raise
        finally:
            REQUEST_DURATION.observe(time.time() - start_time)
    return decorated_function

@app.route('/metrics')
def metrics():
    return generate_latest()
```

### Logging Configuration

```python
# logging_config.py
import logging
import logging.config

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'json': {
            'format': '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s", "request_id": "%(request_id)s"}'
        }
    },
    'handlers': {
        'default': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.StreamHandler',
        },
        'file': {
            'level': 'INFO',
            'formatter': 'json',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/quizaigen/app.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
        }
    },
    'loggers': {
        '': {
            'handlers': ['default', 'file'],
            'level': 'INFO',
            'propagate': False
        }
    }
}

logging.config.dictConfig(LOGGING_CONFIG)
```

---

## Security Configuration

### SSL/TLS Configuration

```bash
# Generate SSL certificate (Let's Encrypt)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d api.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Security Headers

```python
# security.py
from flask import Flask
from flask_talisman import Talisman

app = Flask(__name__)

# Security headers
Talisman(app, {
    'force_https': True,
    'strict_transport_security': True,
    'strict_transport_security_max_age': 31536000,
    'content_security_policy': {
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline'",
        'style-src': "'self' 'unsafe-inline'",
        'img-src': "'self' data:",
    }
})
```

### API Rate Limiting

```python
# rate_limiting.py
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["100 per hour"],
    storage_uri="redis://localhost:6379"
)

@app.route('/api/generate')
@limiter.limit("10 per minute")
def generate_questions():
    # Implementation
    pass
```

---

*This deployment guide provides comprehensive instructions for deploying QuizAIGen in various environments. For specific deployment questions, contact our support team.*
