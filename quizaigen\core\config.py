"""
QuizAIGen Configuration Management

This module handles configuration loading, validation, and management.
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
from pydantic import BaseModel, field_validator

from .exceptions import ConfigurationError
from ..models.base_model import ModelTier


class ModelConfig(BaseModel):
    """Configuration for individual models."""
    name: str
    cache: bool = True
    max_length: int = 512
    device: Optional[str] = None
    batch_size: int = 8
    
    @field_validator('max_length')
    @classmethod
    def validate_max_length(cls, v):
        if v <= 0 or v > 2048:
            raise ValueError('max_length must be between 1 and 2048')
        return v

    @field_validator('batch_size')
    @classmethod
    def validate_batch_size(cls, v):
        if v <= 0 or v > 64:
            raise ValueError('batch_size must be between 1 and 64')
        return v


class MultiLanguageConfig(BaseModel):
    """Configuration for multi-language support."""
    auto_detect: bool = True
    fallback_language: str = 'en'
    min_confidence: float = 0.7
    supported_languages: List[str] = ['en', 'es', 'fr', 'de', 'it', 'pt']
    language_models: Dict[str, str] = {
        'en': 'en_core_web_sm',
        'es': 'es_core_news_sm',
        'fr': 'fr_core_news_sm',
        'de': 'de_core_news_sm',
        'it': 'it_core_news_sm',
        'pt': 'pt_core_news_sm'
    }

    @field_validator('min_confidence')
    @classmethod
    def validate_min_confidence(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError('min_confidence must be between 0.0 and 1.0')
        return v

    @field_validator('fallback_language')
    @classmethod
    def validate_fallback_language(cls, v):
        supported = ['en', 'es', 'fr', 'de', 'it', 'pt', 'nl', 'ru', 'zh', 'ja', 'ko', 'ar']
        if v not in supported:
            raise ValueError(f'fallback_language must be one of: {supported}')
        return v


class ProcessingConfig(BaseModel):
    """Configuration for processing parameters."""
    max_questions: int = 50
    min_quality_score: float = 0.7
    remove_duplicates: bool = True
    language: str = 'en'
    min_sentence_length: int = 10
    max_sentence_length: int = 500
    multilang: MultiLanguageConfig = MultiLanguageConfig()

    @field_validator('max_questions')
    @classmethod
    def validate_max_questions(cls, v):
        if v <= 0 or v > 1000:
            raise ValueError('max_questions must be between 1 and 1000')
        return v

    @field_validator('min_quality_score')
    @classmethod
    def validate_min_quality_score(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError('min_quality_score must be between 0.0 and 1.0')
        return v


class ExportConfig(BaseModel):
    """Configuration for export settings."""
    default_format: str = 'json'
    include_metadata: bool = True
    pretty_print: bool = True
    output_dir: str = './output'
    
    @field_validator('default_format')
    @classmethod
    def validate_default_format(cls, v):
        allowed_formats = ['json', 'csv', 'xml', 'qti', 'moodle']
        if v not in allowed_formats:
            raise ValueError(f'default_format must be one of {allowed_formats}')
        return v


class Config:
    """Main configuration class for QuizAIGen."""

    def __init__(self, config_dict: Optional[Dict[str, Any]] = None, config_file: Optional[str] = None):
        """
        Initialize configuration.

        Args:
            config_dict: Configuration dictionary
            config_file: Path to configuration file (JSON or YAML)
        """
        self._config = self._load_default_config()

        if config_file:
            self._load_from_file(config_file)

        if config_dict:
            self._update_config(config_dict)

        self._validate_config()

        # Set tier attribute for compatibility with DuplicateDetector
        self.tier = ModelTier.FREE  # Default tier
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration."""
        return {
            'models': {
                'mcq': {
                    'name': 't5-base',
                    'cache': True,
                    'max_length': 512,
                    'batch_size': 8
                },
                'boolean': {
                    'name': 'bert-base-uncased',
                    'cache': True,
                    'max_length': 512,
                    'batch_size': 8
                },
                'faq': {
                    'name': 't5-base',
                    'cache': True,
                    'max_length': 512,
                    'batch_size': 8
                },
                'fill_blank': {
                    'name': 't5-base',
                    'cache': True,
                    'max_length': 512,
                    'batch_size': 8
                },
                'paraphrase': {
                    'name': 't5-base',
                    'cache': True,
                    'max_length': 512,
                    'batch_size': 8
                },
                'qa': {
                    'name': 'bert-base-uncased',
                    'cache': True,
                    'max_length': 512,
                    'batch_size': 8
                }
            },
            'processing': {
                'max_questions': 50,
                'min_quality_score': 0.7,
                'remove_duplicates': True,
                'language': 'en',
                'min_sentence_length': 10,
                'max_sentence_length': 500
            },
            'export': {
                'default_format': 'json',
                'include_metadata': True,
                'pretty_print': True,
                'output_dir': './output'
            }
        }
    
    def _load_from_file(self, config_file: str) -> None:
        """Load configuration from file."""
        config_path = Path(config_file)
        
        if not config_path.exists():
            raise ConfigurationError(
                'config_file',
                f"Configuration file not found: {config_file}"
            )
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    file_config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    file_config = json.load(f)
                else:
                    raise ConfigurationError(
                        'config_file',
                        f"Unsupported configuration file format: {config_path.suffix}"
                    )
            
            self._update_config(file_config)
            
        except (json.JSONDecodeError, yaml.YAMLError) as e:
            raise ConfigurationError(
                'config_file',
                f"Failed to parse configuration file: {str(e)}"
            )
    
    def _update_config(self, config_dict: Dict[str, Any]) -> None:
        """Update configuration with provided dictionary."""
        def deep_update(base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self._config, config_dict)
    
    def _validate_config(self) -> None:
        """Validate configuration using Pydantic models."""
        try:
            # Validate model configurations
            for model_name, model_config in self._config['models'].items():
                ModelConfig(**model_config)
            
            # Validate processing configuration
            ProcessingConfig(**self._config['processing'])
            
            # Validate export configuration
            ExportConfig(**self._config['export'])
            
        except Exception as e:
            raise ConfigurationError(
                'validation',
                f"Configuration validation failed: {str(e)}"
            )
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key."""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value by key."""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self._validate_config()
    
    def get_model_config(self, model_type: str) -> Dict[str, Any]:
        """Get configuration for specific model type."""
        model_config = self.get(f'models.{model_type}')
        if not model_config:
            raise ConfigurationError(
                f'models.{model_type}',
                f"No configuration found for model type: {model_type}"
            )
        return model_config
    
    def get_processing_config(self) -> Dict[str, Any]:
        """Get processing configuration."""
        return self.get('processing', {})
    
    def get_export_config(self) -> Dict[str, Any]:
        """Get export configuration."""
        return self.get('export', {})
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return self._config.copy()
    
    def save(self, file_path: str) -> None:
        """Save configuration to file."""
        config_path = Path(file_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(self._config, f, default_flow_style=False, indent=2)
                elif config_path.suffix.lower() == '.json':
                    json.dump(self._config, f, indent=2, ensure_ascii=False)
                else:
                    raise ConfigurationError(
                        'save',
                        f"Unsupported file format: {config_path.suffix}"
                    )
        except Exception as e:
            raise ConfigurationError(
                'save',
                f"Failed to save configuration: {str(e)}"
            )
