"""
Base Question Generator

This module provides the base class for all question generators.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass

from ..utils.logger import LoggerMixin
from ..core.config import Config
from ..core.exceptions import ValidationError, ProcessingError
from ..utils.validation_utils import validate_text_input, validate_positive_integer


@dataclass
class Question:
    """Data class representing a generated question."""
    question: str
    type: str
    answer: Optional[str] = None
    options: Optional[List[str]] = None
    explanation: Optional[str] = None
    difficulty: Optional[str] = None
    keywords: Optional[List[str]] = None
    source_text: Optional[str] = None
    confidence: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert question to dictionary."""
        result = {
            'question': self.question,
            'type': self.type
        }
        
        if self.answer is not None:
            result['answer'] = self.answer
        if self.options is not None:
            result['options'] = self.options
        if self.explanation is not None:
            result['explanation'] = self.explanation
        if self.difficulty is not None:
            result['difficulty'] = self.difficulty
        if self.keywords is not None:
            result['keywords'] = self.keywords
        if self.source_text is not None:
            result['source_text'] = self.source_text
        if self.confidence is not None:
            result['confidence'] = self.confidence
        if self.metadata is not None:
            result['metadata'] = self.metadata
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Question':
        """Create question from dictionary."""
        return cls(
            question=data['question'],
            type=data['type'],
            answer=data.get('answer'),
            options=data.get('options'),
            explanation=data.get('explanation'),
            difficulty=data.get('difficulty'),
            keywords=data.get('keywords'),
            source_text=data.get('source_text'),
            confidence=data.get('confidence'),
            metadata=data.get('metadata')
        )


class BaseQuestionGenerator(ABC, LoggerMixin):
    """Base class for all question generators."""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the question generator.
        
        Args:
            config: Configuration object
        """
        self.config = config or Config()
        self.question_type = self._get_question_type()
        self.model_config = self.config.get_model_config(self.question_type)
        self.processing_config = self.config.get_processing_config()
        
        self.log_info(f"Initialized {self.__class__.__name__}")
    
    @abstractmethod
    def _get_question_type(self) -> str:
        """Get the question type identifier."""
        pass
    
    @abstractmethod
    def _generate_questions_impl(self, text: str, num_questions: int, 
                               **kwargs) -> List[Question]:
        """
        Implementation-specific question generation logic.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of generated questions
        """
        pass
    
    def generate_questions(self, text: str, num_questions: int = 5, 
                         **kwargs) -> List[Question]:
        """
        Generate questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of generated questions
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        # Validate inputs
        text = validate_text_input(text, field_name="text")
        num_questions = validate_positive_integer(
            num_questions, 
            field_name="num_questions",
            min_value=1,
            max_value=self.processing_config.get('max_questions', 50)
        )
        
        self.log_info(f"Generating {num_questions} {self.question_type} questions")
        
        try:
            # Generate questions using implementation
            questions = self._generate_questions_impl(text, num_questions, **kwargs)
            
            # Post-process questions
            questions = self._post_process_questions(questions, text)
            
            self.log_info(f"Successfully generated {len(questions)} questions")
            return questions
            
        except Exception as e:
            self.log_error(f"Failed to generate questions: {str(e)}")
            raise ProcessingError(
                stage="question_generation",
                message=f"Failed to generate {self.question_type} questions: {str(e)}"
            )
    
    def _post_process_questions(self, questions: List[Question], 
                              source_text: str) -> List[Question]:
        """
        Post-process generated questions.
        
        Args:
            questions: List of generated questions
            source_text: Original source text
        
        Returns:
            Post-processed questions
        """
        processed_questions = []
        
        for question in questions:
            # Add source text reference
            question.source_text = source_text[:200] + "..." if len(source_text) > 200 else source_text
            
            # Filter by quality if configured
            min_quality = self.processing_config.get('min_quality_score', 0.0)
            if question.confidence is not None and question.confidence < min_quality:
                self.log_debug(f"Filtered question with low confidence: {question.confidence}")
                continue
            
            processed_questions.append(question)
        
        # Remove duplicates if configured
        if self.processing_config.get('remove_duplicates', True):
            processed_questions = self._remove_duplicate_questions(processed_questions)
        
        return processed_questions
    
    def _remove_duplicate_questions(self, questions: List[Question]) -> List[Question]:
        """
        Remove duplicate questions based on text similarity.
        
        Args:
            questions: List of questions
        
        Returns:
            List of unique questions
        """
        from ..utils.text_utils import calculate_text_similarity
        
        unique_questions = []
        similarity_threshold = 0.8
        
        for question in questions:
            is_duplicate = False
            
            for existing_question in unique_questions:
                similarity = calculate_text_similarity(
                    question.question, 
                    existing_question.question
                )
                
                if similarity > similarity_threshold:
                    is_duplicate = True
                    self.log_debug(f"Removed duplicate question: {question.question[:50]}...")
                    break
            
            if not is_duplicate:
                unique_questions.append(question)
        
        return unique_questions
    
    def _validate_generated_question(self, question: Question) -> bool:
        """
        Validate a generated question.
        
        Args:
            question: Question to validate
        
        Returns:
            True if question is valid
        """
        try:
            # Basic validation
            if not question.question or not question.question.strip():
                return False
            
            if len(question.question.strip()) < 10:
                return False
            
            # Type-specific validation
            if question.type == 'mcq':
                if not question.options or len(question.options) < 2:
                    return False
                if not question.answer:
                    return False
            
            return True
            
        except Exception as e:
            self.log_warning(f"Question validation failed: {str(e)}")
            return False
    
    def get_supported_parameters(self) -> Dict[str, Any]:
        """
        Get supported parameters for this generator.
        
        Returns:
            Dictionary of supported parameters and their descriptions
        """
        return {
            'num_questions': {
                'type': 'int',
                'description': 'Number of questions to generate',
                'default': 5,
                'min': 1,
                'max': self.processing_config.get('max_questions', 50)
            }
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the model used by this generator.
        
        Returns:
            Dictionary with model information
        """
        return {
            'question_type': self.question_type,
            'model_name': self.model_config.get('name', 'unknown'),
            'max_length': self.model_config.get('max_length', 512),
            'batch_size': self.model_config.get('batch_size', 8)
        }
