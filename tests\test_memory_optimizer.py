"""
Tests for Memory Optimization Utilities

Tests the memory optimization functionality including memory monitoring,
object pooling, memory-efficient caching, and optimization strategies.
"""

import unittest
import time
import threading
from unittest.mock import Mock, patch
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from quizaigen.utils.memory_optimizer import (
    MemorySnapshot, MemoryThresholds, MemoryPool, MemoryEfficientCache,
    MemoryOptimizer, get_memory_optimizer, optimize_memory_usage
)


class TestObject:
    """Test object for pooling."""
    
    def __init__(self):
        self.data = []
        self.reset_called = False
    
    def reset(self):
        self.data.clear()
        self.reset_called = True


class TestMemorySnapshot(unittest.TestCase):
    """Test memory snapshot functionality."""
    
    def test_memory_snapshot_creation(self):
        """Test creating memory snapshot."""
        snapshot = MemorySnapshot(
            timestamp=time.time(),
            rss_mb=100.0,
            vms_mb=200.0,
            percent=50.0,
            available_mb=1000.0,
            gc_objects=1000,
            gc_collections={0: 10, 1: 5, 2: 1}
        )
        
        self.assertEqual(snapshot.rss_mb, 100.0)
        self.assertEqual(snapshot.percent, 50.0)
        self.assertEqual(snapshot.gc_objects, 1000)
        self.assertIn(0, snapshot.gc_collections)


class TestMemoryThresholds(unittest.TestCase):
    """Test memory thresholds configuration."""
    
    def test_default_thresholds(self):
        """Test default threshold values."""
        thresholds = MemoryThresholds()
        
        self.assertEqual(thresholds.warning_percent, 75.0)
        self.assertEqual(thresholds.critical_percent, 85.0)
        self.assertEqual(thresholds.gc_trigger_percent, 80.0)
        self.assertEqual(thresholds.cache_clear_percent, 90.0)
        self.assertIsNone(thresholds.max_rss_mb)
    
    def test_custom_thresholds(self):
        """Test custom threshold values."""
        thresholds = MemoryThresholds(
            warning_percent=60.0,
            critical_percent=80.0,
            max_rss_mb=500.0
        )
        
        self.assertEqual(thresholds.warning_percent, 60.0)
        self.assertEqual(thresholds.critical_percent, 80.0)
        self.assertEqual(thresholds.max_rss_mb, 500.0)


class TestMemoryPool(unittest.TestCase):
    """Test memory pool functionality."""
    
    def setUp(self):
        """Set up test pool."""
        self.pool = MemoryPool(factory=TestObject, max_size=5)
    
    def test_pool_creation(self):
        """Test pool creation."""
        self.assertEqual(self.pool.max_size, 5)
        self.assertEqual(len(self.pool.pool), 0)
        self.assertEqual(self.pool.created_count, 0)
        self.assertEqual(self.pool.reused_count, 0)
    
    def test_get_new_object(self):
        """Test getting new object from empty pool."""
        obj = self.pool.get()
        
        self.assertIsInstance(obj, TestObject)
        self.assertEqual(self.pool.created_count, 1)
        self.assertEqual(self.pool.reused_count, 0)
    
    def test_put_and_get_reuse(self):
        """Test putting object back and reusing it."""
        # Get and return object
        obj1 = self.pool.get()
        obj1.data = ["test"]
        self.pool.put(obj1)
        
        # Get object again (should be reused)
        obj2 = self.pool.get()
        
        self.assertIs(obj1, obj2)
        self.assertTrue(obj2.reset_called)
        self.assertEqual(len(obj2.data), 0)  # Should be reset
        self.assertEqual(self.pool.reused_count, 1)
    
    def test_pool_size_limit(self):
        """Test pool size limit enforcement."""
        objects = []
        
        # Fill pool beyond max size
        for i in range(10):
            obj = self.pool.get()
            objects.append(obj)
        
        # Return all objects
        for obj in objects:
            self.pool.put(obj)
        
        # Pool should not exceed max size
        self.assertLessEqual(len(self.pool.pool), self.pool.max_size)
    
    def test_get_stats(self):
        """Test pool statistics."""
        # Get some objects
        obj1 = self.pool.get()
        obj2 = self.pool.get()
        
        # Return one
        self.pool.put(obj1)
        
        # Get another (should reuse)
        obj3 = self.pool.get()
        
        stats = self.pool.get_stats()
        
        self.assertEqual(stats['created_count'], 2)
        self.assertEqual(stats['reused_count'], 1)
        self.assertGreater(stats['reuse_ratio'], 0)


class TestMemoryEfficientCache(unittest.TestCase):
    """Test memory-efficient cache functionality."""
    
    def setUp(self):
        """Set up test cache."""
        self.cache = MemoryEfficientCache(max_size=3, max_memory_mb=1.0)
    
    def test_cache_creation(self):
        """Test cache creation."""
        self.assertEqual(self.cache.max_size, 3)
        self.assertEqual(self.cache.max_memory_mb, 1.0)
        self.assertEqual(len(self.cache.cache), 0)
    
    def test_put_and_get(self):
        """Test basic put and get operations."""
        self.cache.put("key1", "value1")
        self.cache.put("key2", "value2")
        
        self.assertEqual(self.cache.get("key1"), "value1")
        self.assertEqual(self.cache.get("key2"), "value2")
        self.assertIsNone(self.cache.get("nonexistent"))
    
    def test_lru_eviction(self):
        """Test LRU eviction when size limit is exceeded."""
        # Fill cache to capacity
        self.cache.put("key1", "value1")
        self.cache.put("key2", "value2")
        self.cache.put("key3", "value3")
        
        # Access key1 to make it recently used
        self.cache.get("key1")
        
        # Add another item (should evict key2, the least recently used)
        self.cache.put("key4", "value4")
        
        self.assertIsNotNone(self.cache.get("key1"))  # Should still be there
        self.assertIsNone(self.cache.get("key2"))     # Should be evicted
        self.assertIsNotNone(self.cache.get("key3"))  # Should still be there
        self.assertIsNotNone(self.cache.get("key4"))  # Should be there
    
    def test_update_existing(self):
        """Test updating existing cache entry."""
        self.cache.put("key1", "value1")
        self.cache.put("key1", "value1_updated")
        
        self.assertEqual(self.cache.get("key1"), "value1_updated")
        self.assertEqual(len(self.cache.cache), 1)
    
    def test_clear(self):
        """Test cache clearing."""
        self.cache.put("key1", "value1")
        self.cache.put("key2", "value2")
        
        self.cache.clear()
        
        self.assertEqual(len(self.cache.cache), 0)
        self.assertEqual(self.cache.memory_usage, 0.0)
        self.assertIsNone(self.cache.get("key1"))
    
    def test_get_stats(self):
        """Test cache statistics."""
        self.cache.put("key1", "value1")
        self.cache.put("key2", "value2")
        
        stats = self.cache.get_stats()
        
        self.assertEqual(stats['size'], 2)
        self.assertEqual(stats['max_size'], 3)
        self.assertGreater(stats['memory_usage_mb'], 0)
        self.assertEqual(stats['max_memory_mb'], 1.0)
        self.assertGreater(stats['utilization'], 0)


class TestMemoryOptimizer(unittest.TestCase):
    """Test memory optimizer functionality."""
    
    def setUp(self):
        """Set up test optimizer."""
        self.thresholds = MemoryThresholds(
            warning_percent=50.0,
            critical_percent=70.0
        )
        self.optimizer = MemoryOptimizer(self.thresholds)
    
    def test_optimizer_creation(self):
        """Test optimizer creation."""
        self.assertEqual(self.optimizer.thresholds.warning_percent, 50.0)
        self.assertEqual(len(self.optimizer.snapshots), 0)
        self.assertEqual(len(self.optimizer.pools), 0)
        self.assertEqual(len(self.optimizer.caches), 0)
    
    def test_get_memory_snapshot(self):
        """Test getting memory snapshot."""
        snapshot = self.optimizer.get_memory_snapshot()
        
        self.assertIsInstance(snapshot, MemorySnapshot)
        self.assertGreater(snapshot.timestamp, 0)
        self.assertGreaterEqual(snapshot.gc_objects, 0)
        self.assertEqual(len(self.optimizer.snapshots), 1)
    
    @patch('quizaigen.utils.memory_optimizer.PSUTIL_AVAILABLE', False)
    def test_memory_snapshot_without_psutil(self):
        """Test memory snapshot without psutil."""
        snapshot = self.optimizer.get_memory_snapshot()
        
        self.assertEqual(snapshot.rss_mb, 0.0)
        self.assertEqual(snapshot.percent, 0.0)
        self.assertGreaterEqual(snapshot.gc_objects, 0)
    
    def test_register_cache(self):
        """Test registering cache."""
        cache = MemoryEfficientCache()
        self.optimizer.register_cache("test_cache", cache)
        
        self.assertIn("test_cache", self.optimizer.caches)
        self.assertIs(self.optimizer.caches["test_cache"], cache)
    
    def test_register_pool(self):
        """Test registering pool."""
        pool = MemoryPool(TestObject)
        self.optimizer.register_pool("test_pool", pool)
        
        self.assertIn("test_pool", self.optimizer.pools)
        self.assertIs(self.optimizer.pools["test_pool"], pool)
    
    def test_create_cache(self):
        """Test creating and registering cache."""
        cache = self.optimizer.create_cache("new_cache", max_size=100)
        
        self.assertIsInstance(cache, MemoryEfficientCache)
        self.assertIn("new_cache", self.optimizer.caches)
        self.assertEqual(cache.max_size, 100)
    
    def test_create_pool(self):
        """Test creating and registering pool."""
        pool = self.optimizer.create_pool("new_pool", TestObject, max_size=50)
        
        self.assertIsInstance(pool, MemoryPool)
        self.assertIn("new_pool", self.optimizer.pools)
        self.assertEqual(pool.max_size, 50)
    
    def test_memory_limit_context(self):
        """Test memory limit context manager."""
        with self.optimizer.memory_limit(max_memory_mb=1000.0):
            # Should not raise any exceptions for normal usage
            data = [i for i in range(100)]
            self.assertEqual(len(data), 100)
    
    def test_get_optimization_stats(self):
        """Test getting optimization statistics."""
        # Register some components
        self.optimizer.create_cache("test_cache")
        self.optimizer.create_pool("test_pool", TestObject)
        
        stats = self.optimizer.get_optimization_stats()
        
        self.assertIn('current_memory', stats)
        self.assertIn('optimization_actions', stats)
        self.assertIn('registered_components', stats)
        self.assertIn('monitoring', stats)
        
        self.assertEqual(stats['registered_components']['caches'], 1)
        self.assertEqual(stats['registered_components']['pools'], 1)
    
    def test_monitoring_start_stop(self):
        """Test starting and stopping monitoring."""
        self.assertFalse(self.optimizer.monitoring_active)
        
        # Start monitoring
        self.optimizer.start_monitoring(interval=0.1)
        self.assertTrue(self.optimizer.monitoring_active)
        
        # Let it run briefly
        time.sleep(0.2)
        
        # Stop monitoring
        self.optimizer.stop_monitoring()
        self.assertFalse(self.optimizer.monitoring_active)


class TestGlobalOptimizer(unittest.TestCase):
    """Test global optimizer functionality."""
    
    def test_get_global_optimizer(self):
        """Test getting global optimizer instance."""
        optimizer1 = get_memory_optimizer()
        optimizer2 = get_memory_optimizer()
        
        # Should return the same instance
        self.assertIs(optimizer1, optimizer2)
        self.assertIsInstance(optimizer1, MemoryOptimizer)


class TestOptimizeMemoryUsageDecorator(unittest.TestCase):
    """Test memory usage optimization decorator."""
    
    def test_decorator_basic_functionality(self):
        """Test that decorator doesn't break function execution."""
        
        @optimize_memory_usage
        def test_function(x, y):
            return x + y
        
        result = test_function(2, 3)
        self.assertEqual(result, 5)
    
    def test_decorator_with_exception(self):
        """Test decorator behavior when function raises exception."""
        
        @optimize_memory_usage
        def failing_function():
            raise ValueError("Test error")
        
        with self.assertRaises(ValueError):
            failing_function()


class TestIntegration(unittest.TestCase):
    """Integration tests for memory optimization."""
    
    def test_end_to_end_optimization(self):
        """Test end-to-end memory optimization workflow."""
        optimizer = MemoryOptimizer()
        
        # Create cache and pool
        cache = optimizer.create_cache("integration_cache", max_size=10)
        pool = optimizer.create_pool("integration_pool", TestObject, max_size=5)
        
        # Use cache
        for i in range(15):  # More than cache size
            cache.put(f"key_{i}", f"value_{i}")
        
        # Use pool - first get some objects and return them to enable reuse
        first_batch = []
        for i in range(5):
            obj = pool.get()
            obj.data = [f"item_{i}"]
            first_batch.append(obj)

        # Return first batch to pool
        for obj in first_batch:
            pool.put(obj)

        # Get more objects - these should reuse objects from the pool
        second_batch = []
        for i in range(5, 10):
            obj = pool.get()
            obj.data = [f"item_{i}"]
            second_batch.append(obj)

        # Return second batch
        for obj in second_batch:
            pool.put(obj)
        
        # Get statistics
        stats = optimizer.get_optimization_stats()
        
        # Verify integration
        self.assertIn('cache_stats', stats)
        self.assertIn('pool_stats', stats)
        self.assertIn('integration_cache', stats['cache_stats'])
        self.assertIn('integration_pool', stats['pool_stats'])
        
        # Verify cache worked (should have evicted some items)
        cache_stats = stats['cache_stats']['integration_cache']
        self.assertLessEqual(cache_stats['size'], 10)
        
        # Verify pool worked (should have reused objects)
        pool_stats = stats['pool_stats']['integration_pool']
        self.assertGreater(pool_stats['reused_count'], 0)


if __name__ == '__main__':
    unittest.main()
