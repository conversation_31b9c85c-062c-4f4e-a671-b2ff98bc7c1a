"""
QuizAIGen Validation Utilities

This module provides input validation functions.
"""

import re
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from ..core.exceptions import ValidationError


def validate_text_input(text: str, min_length: int = 10, 
                       max_length: int = 10000, 
                       field_name: str = "text") -> str:
    """
    Validate text input.
    
    Args:
        text: Text to validate
        min_length: Minimum text length
        max_length: Maximum text length
        field_name: Field name for error messages
    
    Returns:
        Validated text
    
    Raises:
        ValidationError: If validation fails
    """
    if not isinstance(text, str):
        raise ValidationError(field_name, text, "Text must be a string")
    
    text = text.strip()
    
    if not text:
        raise ValidationError(field_name, text, "Text cannot be empty")
    
    if len(text) < min_length:
        raise ValidationError(
            field_name, text, 
            f"Text must be at least {min_length} characters long"
        )
    
    if len(text) > max_length:
        raise ValidationError(
            field_name, text, 
            f"Text must be at most {max_length} characters long"
        )
    
    return text


def validate_file_path(file_path: Union[str, Path], 
                      must_exist: bool = True,
                      allowed_extensions: Optional[List[str]] = None,
                      field_name: str = "file_path") -> Path:
    """
    Validate file path.
    
    Args:
        file_path: File path to validate
        must_exist: Whether file must exist
        allowed_extensions: List of allowed file extensions
        field_name: Field name for error messages
    
    Returns:
        Validated Path object
    
    Raises:
        ValidationError: If validation fails
    """
    if not file_path:
        raise ValidationError(field_name, file_path, "File path cannot be empty")
    
    try:
        path = Path(file_path)
    except Exception as e:
        raise ValidationError(field_name, file_path, f"Invalid file path: {str(e)}")
    
    if must_exist and not path.exists():
        raise ValidationError(field_name, file_path, "File does not exist")
    
    if allowed_extensions:
        extension = path.suffix.lower().lstrip('.')
        if extension not in [ext.lower().lstrip('.') for ext in allowed_extensions]:
            raise ValidationError(
                field_name, file_path, 
                f"File extension must be one of: {allowed_extensions}"
            )
    
    return path


def validate_url(url: str, field_name: str = "url") -> str:
    """
    Validate URL format.
    
    Args:
        url: URL to validate
        field_name: Field name for error messages
    
    Returns:
        Validated URL
    
    Raises:
        ValidationError: If validation fails
    """
    if not isinstance(url, str):
        raise ValidationError(field_name, url, "URL must be a string")
    
    url = url.strip()
    
    if not url:
        raise ValidationError(field_name, url, "URL cannot be empty")
    
    # Basic URL pattern
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    if not url_pattern.match(url):
        raise ValidationError(field_name, url, "Invalid URL format")
    
    return url


def validate_positive_integer(value: Any, field_name: str = "value", 
                            min_value: int = 1, max_value: Optional[int] = None) -> int:
    """
    Validate positive integer.
    
    Args:
        value: Value to validate
        field_name: Field name for error messages
        min_value: Minimum allowed value
        max_value: Maximum allowed value
    
    Returns:
        Validated integer
    
    Raises:
        ValidationError: If validation fails
    """
    if not isinstance(value, int):
        try:
            value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(field_name, value, "Value must be an integer")
    
    if value < min_value:
        raise ValidationError(
            field_name, value, 
            f"Value must be at least {min_value}"
        )
    
    if max_value is not None and value > max_value:
        raise ValidationError(
            field_name, value, 
            f"Value must be at most {max_value}"
        )
    
    return value


def validate_float_range(value: Any, field_name: str = "value",
                        min_value: float = 0.0, max_value: float = 1.0) -> float:
    """
    Validate float within range.
    
    Args:
        value: Value to validate
        field_name: Field name for error messages
        min_value: Minimum allowed value
        max_value: Maximum allowed value
    
    Returns:
        Validated float
    
    Raises:
        ValidationError: If validation fails
    """
    if not isinstance(value, (int, float)):
        try:
            value = float(value)
        except (ValueError, TypeError):
            raise ValidationError(field_name, value, "Value must be a number")
    
    if value < min_value:
        raise ValidationError(
            field_name, value, 
            f"Value must be at least {min_value}"
        )
    
    if value > max_value:
        raise ValidationError(
            field_name, value, 
            f"Value must be at most {max_value}"
        )
    
    return float(value)


def validate_choice(value: Any, choices: List[Any], 
                   field_name: str = "value") -> Any:
    """
    Validate value is in allowed choices.
    
    Args:
        value: Value to validate
        choices: List of allowed choices
        field_name: Field name for error messages
    
    Returns:
        Validated value
    
    Raises:
        ValidationError: If validation fails
    """
    if value not in choices:
        raise ValidationError(
            field_name, value, 
            f"Value must be one of: {choices}"
        )
    
    return value


def validate_email(email: str, field_name: str = "email") -> str:
    """
    Validate email format.
    
    Args:
        email: Email to validate
        field_name: Field name for error messages
    
    Returns:
        Validated email
    
    Raises:
        ValidationError: If validation fails
    """
    if not isinstance(email, str):
        raise ValidationError(field_name, email, "Email must be a string")
    
    email = email.strip().lower()
    
    if not email:
        raise ValidationError(field_name, email, "Email cannot be empty")
    
    # Basic email pattern
    email_pattern = re.compile(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    )
    
    if not email_pattern.match(email):
        raise ValidationError(field_name, email, "Invalid email format")
    
    return email


def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate configuration dictionary.
    
    Args:
        config: Configuration dictionary to validate
    
    Returns:
        Validated configuration
    
    Raises:
        ValidationError: If validation fails
    """
    if not isinstance(config, dict):
        raise ValidationError("config", config, "Configuration must be a dictionary")
    
    # Validate required sections
    required_sections = ['models', 'processing', 'export']
    for section in required_sections:
        if section not in config:
            raise ValidationError(
                f"config.{section}", None, 
                f"Configuration must contain '{section}' section"
            )
    
    # Validate models section
    models_config = config['models']
    if not isinstance(models_config, dict):
        raise ValidationError(
            "config.models", models_config, 
            "Models configuration must be a dictionary"
        )
    
    # Validate processing section
    processing_config = config['processing']
    if not isinstance(processing_config, dict):
        raise ValidationError(
            "config.processing", processing_config, 
            "Processing configuration must be a dictionary"
        )
    
    # Validate export section
    export_config = config['export']
    if not isinstance(export_config, dict):
        raise ValidationError(
            "config.export", export_config, 
            "Export configuration must be a dictionary"
        )
    
    return config


def validate_question_data(question: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate question data structure.
    
    Args:
        question: Question dictionary to validate
    
    Returns:
        Validated question
    
    Raises:
        ValidationError: If validation fails
    """
    if not isinstance(question, dict):
        raise ValidationError("question", question, "Question must be a dictionary")
    
    # Required fields
    required_fields = ['question', 'type']
    for field in required_fields:
        if field not in question:
            raise ValidationError(
                f"question.{field}", None, 
                f"Question must contain '{field}' field"
            )
    
    # Validate question text
    validate_text_input(question['question'], field_name="question.question")
    
    # Validate question type
    allowed_types = ['mcq', 'boolean', 'short_answer', 'fill_blank', 'paraphrase', 'qa']
    validate_choice(question['type'], allowed_types, field_name="question.type")
    
    # Type-specific validation
    if question['type'] == 'mcq':
        if 'options' not in question:
            raise ValidationError(
                "question.options", None, 
                "MCQ question must contain 'options' field"
            )
        
        if not isinstance(question['options'], list) or len(question['options']) < 2:
            raise ValidationError(
                "question.options", question.get('options'), 
                "MCQ options must be a list with at least 2 items"
            )
        
        if 'answer' not in question:
            raise ValidationError(
                "question.answer", None, 
                "MCQ question must contain 'answer' field"
            )
    
    return question
