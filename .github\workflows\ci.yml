name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.8', '3.9', '3.10', '3.11', '3.12']
        exclude:
          # Reduce matrix size for faster builds
          - os: macos-latest
            python-version: '3.8'
          - os: windows-latest
            python-version: '3.8'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y tesseract-ocr tesseract-ocr-eng
        sudo apt-get install -y poppler-utils

    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install tesseract
        brew install poppler

    - name: Install system dependencies (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        # Install tesseract via chocolatey
        choco install tesseract
        # Add tesseract to PATH
        echo "C:\Program Files\Tesseract-OCR" >> $GITHUB_PATH

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        pip install -e .

    - name: Download spaCy models
      run: |
        python -m spacy download en_core_web_sm
        python -m spacy download es_core_news_sm || true
        python -m spacy download fr_core_news_sm || true
        python -m spacy download de_core_news_sm || true

    - name: Lint with flake8
      run: |
        # Stop the build if there are Python syntax errors or undefined names
        flake8 quizaigen --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings
        flake8 quizaigen --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

    - name: Format check with black
      run: |
        black --check --diff quizaigen tests

    - name: Type check with mypy
      run: |
        mypy quizaigen --ignore-missing-imports

    - name: Test with pytest
      run: |
        pytest tests/ -v --cov=quizaigen --cov-report=xml --cov-report=html --cov-report=term-missing

    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.python-version == '3.11'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install safety bandit

    - name: Security check with safety
      run: |
        safety check --json || true

    - name: Security check with bandit
      run: |
        bandit -r quizaigen -f json || true

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: |
        python -m build

    - name: Check package
      run: |
        twine check dist/*

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  integration-test:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y tesseract-ocr tesseract-ocr-eng poppler-utils

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -e .

    - name: Download spaCy models
      run: |
        python -m spacy download en_core_web_sm

    - name: Run integration tests
      run: |
        pytest tests/integration/ -v --tb=short

    - name: Run end-to-end tests
      run: |
        python test_basic.py

  performance-test:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark
        pip install -e .

    - name: Run performance tests
      run: |
        pytest tests/ -k "benchmark" --benchmark-only --benchmark-json=benchmark.json || true

    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: benchmark-results
        path: benchmark.json
