"""
Question Difficulty Assessor for QuizAIGen

Provides AI-powered difficulty assessment for questions using multiple metrics
including vocabulary complexity, cognitive load, and semantic analysis.
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import re
import math

from ..generators.base import Question
from ..models.base_model import ModelTier
from ..core.exceptions import ProcessingError, LicenseError
from ..utils.logger import LoggerMixin


class DifficultyLevel(Enum):
    """Question difficulty levels."""
    BEGINNER = "beginner"
    ELEMENTARY = "elementary"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


@dataclass
class DifficultyAssessment:
    """Result of difficulty assessment."""
    level: DifficultyLevel
    score: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    factors: Dict[str, float]  # Individual assessment factors
    reasoning: str  # Explanation of assessment
    
    def to_dict(self) -> Dict:
        """Convert to dictionary format."""
        return {
            'level': self.level.value,
            'score': self.score,
            'confidence': self.confidence,
            'factors': self.factors,
            'reasoning': self.reasoning
        }


class DifficultyAssessor(LoggerMixin):
    """
    AI-powered question difficulty assessor.
    
    Uses multiple metrics to assess question difficulty:
    - Vocabulary complexity (word frequency, syllable count)
    - Syntactic complexity (sentence structure, clause count)
    - Cognitive load (question type, required reasoning)
    - Semantic complexity (concept density, abstraction level)
    """
    
    def __init__(self, tier: ModelTier = ModelTier.FREE):
        """
        Initialize difficulty assessor.
        
        Args:
            tier: License tier for feature access
        """
        super().__init__()
        self.tier = tier
        
        # Difficulty thresholds for different levels
        self.difficulty_thresholds = {
            DifficultyLevel.BEGINNER: (0.0, 0.2),
            DifficultyLevel.ELEMENTARY: (0.2, 0.4),
            DifficultyLevel.INTERMEDIATE: (0.4, 0.6),
            DifficultyLevel.ADVANCED: (0.6, 0.8),
            DifficultyLevel.EXPERT: (0.8, 1.0)
        }
        
        # Weight factors for different assessment components
        self.assessment_weights = {
            'vocabulary_complexity': 0.25,
            'syntactic_complexity': 0.20,
            'cognitive_load': 0.30,
            'semantic_complexity': 0.25
        }
        
        # Common word frequency lists (simplified)
        self.common_words = set([
            'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have',
            'i', 'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you',
            'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they',
            'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would',
            'there', 'their', 'what', 'so', 'up', 'out', 'if', 'about',
            'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can',
            'like', 'time', 'no', 'just', 'him', 'know', 'take', 'people',
            'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see',
            'other', 'than', 'then', 'now', 'look', 'only', 'come', 'its',
            'over', 'think', 'also', 'back', 'after', 'use', 'two', 'how',
            'our', 'work', 'first', 'well', 'way', 'even', 'new', 'want',
            'because', 'any', 'these', 'give', 'day', 'most', 'us'
        ])
        
        self.log_info(f"Initialized DifficultyAssessor with tier: {tier.value}")
    
    def assess_question(self, question: Question, context: str = "") -> DifficultyAssessment:
        """
        Assess the difficulty of a question.
        
        Args:
            question: Question to assess
            context: Optional context for assessment
            
        Returns:
            DifficultyAssessment with level, score, and detailed factors
            
        Raises:
            ProcessingError: If assessment fails
            LicenseError: If tier doesn't support advanced features
        """
        try:
            self.log_debug(f"Assessing difficulty for question: {question.question[:50]}...")
            
            # Calculate individual difficulty factors
            factors = {}
            
            # Basic factors (available in all tiers)
            factors['vocabulary_complexity'] = self._assess_vocabulary_complexity(question.question)
            factors['syntactic_complexity'] = self._assess_syntactic_complexity(question.question)
            factors['cognitive_load'] = self._assess_cognitive_load(question)
            
            # Advanced factors (Premium/Enterprise only)
            if self.tier in [ModelTier.PREMIUM, ModelTier.ENTERPRISE]:
                factors['semantic_complexity'] = self._assess_semantic_complexity(question.question, context)
            else:
                factors['semantic_complexity'] = 0.5  # Default for free tier
            
            # Calculate weighted overall score
            overall_score = sum(
                factors[factor] * self.assessment_weights[factor]
                for factor in factors
            )
            
            # Determine difficulty level
            difficulty_level = self._score_to_level(overall_score)
            
            # Calculate confidence based on factor consistency
            confidence = self._calculate_confidence(factors)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(factors, difficulty_level)
            
            assessment = DifficultyAssessment(
                level=difficulty_level,
                score=overall_score,
                confidence=confidence,
                factors=factors,
                reasoning=reasoning
            )
            
            self.log_info(f"Assessed difficulty: {difficulty_level.value} (score: {overall_score:.2f})")
            return assessment
            
        except Exception as e:
            self.log_error(f"Difficulty assessment failed: {str(e)}")
            raise ProcessingError(
                stage="difficulty_assessment",
                message=f"Failed to assess question difficulty: {str(e)}"
            )
    
    def assess_questions_batch(self, questions: List[Question], 
                             context: str = "") -> List[DifficultyAssessment]:
        """
        Assess difficulty for multiple questions.
        
        Args:
            questions: List of questions to assess
            context: Optional context for assessment
            
        Returns:
            List of difficulty assessments
        """
        assessments = []
        
        for question in questions:
            try:
                assessment = self.assess_question(question, context)
                assessments.append(assessment)
            except Exception as e:
                self.log_warning(f"Failed to assess question difficulty: {str(e)}")
                # Create default assessment for failed cases
                default_assessment = DifficultyAssessment(
                    level=DifficultyLevel.INTERMEDIATE,
                    score=0.5,
                    confidence=0.0,
                    factors={},
                    reasoning="Assessment failed - using default intermediate level"
                )
                assessments.append(default_assessment)
        
        return assessments
    
    def _assess_vocabulary_complexity(self, text: str) -> float:
        """Assess vocabulary complexity based on word frequency and length."""
        words = re.findall(r'\b\w+\b', text.lower())
        if not words:
            return 0.0
        
        # Calculate metrics
        uncommon_words = sum(1 for word in words if word not in self.common_words)
        uncommon_ratio = uncommon_words / len(words)
        
        avg_word_length = sum(len(word) for word in words) / len(words)
        
        # Normalize and combine metrics
        complexity_score = (uncommon_ratio * 0.7) + (min(avg_word_length / 10, 1.0) * 0.3)
        
        return min(complexity_score, 1.0)
    
    def _assess_syntactic_complexity(self, text: str) -> float:
        """Assess syntactic complexity based on sentence structure."""
        # Count sentences
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if not sentences:
            return 0.0
        
        # Calculate average sentence length
        total_words = len(re.findall(r'\b\w+\b', text))
        avg_sentence_length = total_words / len(sentences)
        
        # Count complex punctuation (commas, semicolons, colons)
        complex_punct = len(re.findall(r'[,;:]', text))
        punct_density = complex_punct / max(total_words, 1)
        
        # Normalize and combine
        length_score = min(avg_sentence_length / 20, 1.0)
        punct_score = min(punct_density * 10, 1.0)
        
        complexity_score = (length_score * 0.6) + (punct_score * 0.4)
        
        return min(complexity_score, 1.0)
    
    def _assess_cognitive_load(self, question: Question) -> float:
        """Assess cognitive load based on question type and content."""
        base_scores = {
            'boolean': 0.2,  # Simple true/false
            'mcq': 0.4,      # Multiple choice requires analysis
            'short_answer': 0.6,  # Requires recall and formulation
            'fill_blank': 0.5,    # Requires specific knowledge
            'essay': 0.8,         # Requires synthesis and analysis
            'faq': 0.3           # Usually straightforward
        }
        
        base_score = base_scores.get(question.type, 0.5)
        
        # Adjust based on question content complexity
        question_text = question.question.lower()
        
        # Look for cognitive complexity indicators
        complexity_indicators = {
            'analyze': 0.8, 'evaluate': 0.9, 'synthesize': 0.9, 'compare': 0.7,
            'contrast': 0.7, 'explain': 0.6, 'describe': 0.4, 'identify': 0.3,
            'list': 0.2, 'define': 0.3, 'calculate': 0.6, 'solve': 0.7,
            'prove': 0.9, 'justify': 0.8, 'critique': 0.8, 'create': 0.9
        }
        
        max_indicator_score = 0.0
        for indicator, score in complexity_indicators.items():
            if indicator in question_text:
                max_indicator_score = max(max_indicator_score, score)
        
        # Combine base score with indicator score
        if max_indicator_score > 0:
            cognitive_score = (base_score * 0.4) + (max_indicator_score * 0.6)
        else:
            cognitive_score = base_score
        
        return min(cognitive_score, 1.0)

    def _assess_semantic_complexity(self, text: str, context: str = "") -> float:
        """
        Assess semantic complexity (Premium/Enterprise feature).

        This would use BERT embeddings to analyze concept density and abstraction.
        For now, provides rule-based approximation.
        """
        if self.tier == ModelTier.FREE:
            raise LicenseError("Semantic complexity assessment requires Premium or Enterprise tier")

        # Rule-based approximation for semantic complexity
        # In full implementation, this would use BERT embeddings

        # Count abstract concepts (simplified heuristic)
        abstract_indicators = [
            'concept', 'theory', 'principle', 'framework', 'methodology',
            'paradigm', 'philosophy', 'ideology', 'hypothesis', 'assumption',
            'implication', 'consequence', 'relationship', 'correlation',
            'causation', 'influence', 'impact', 'effect', 'significance'
        ]

        text_lower = text.lower()
        abstract_count = sum(1 for indicator in abstract_indicators if indicator in text_lower)

        # Calculate concept density
        words = re.findall(r'\b\w+\b', text)
        concept_density = abstract_count / max(len(words), 1)

        # Assess domain specificity
        domain_terms = self._count_domain_specific_terms(text)
        domain_score = min(domain_terms / max(len(words), 1) * 10, 1.0)

        # Combine metrics
        semantic_score = (concept_density * 0.6) + (domain_score * 0.4)

        return min(semantic_score, 1.0)

    def _count_domain_specific_terms(self, text: str) -> int:
        """Count domain-specific technical terms."""
        # Simplified domain term detection
        domain_patterns = [
            r'\b\w*tion\b',  # -tion endings (often technical)
            r'\b\w*ism\b',   # -ism endings (concepts/theories)
            r'\b\w*ology\b', # -ology endings (fields of study)
            r'\b\w{8,}\b'    # Long words (often technical)
        ]

        count = 0
        for pattern in domain_patterns:
            matches = re.findall(pattern, text.lower())
            count += len(matches)

        return count

    def _score_to_level(self, score: float) -> DifficultyLevel:
        """Convert numerical score to difficulty level."""
        for level, (min_score, max_score) in self.difficulty_thresholds.items():
            if min_score <= score < max_score:
                return level

        # Handle edge case for score = 1.0
        if score >= 0.8:
            return DifficultyLevel.EXPERT

        return DifficultyLevel.INTERMEDIATE  # Default fallback

    def _calculate_confidence(self, factors: Dict[str, float]) -> float:
        """Calculate confidence based on factor consistency."""
        if not factors:
            return 0.0

        # Calculate variance in factor scores
        scores = list(factors.values())
        mean_score = sum(scores) / len(scores)
        variance = sum((score - mean_score) ** 2 for score in scores) / len(scores)

        # Convert variance to confidence (lower variance = higher confidence)
        confidence = max(0.0, 1.0 - (variance * 4))  # Scale variance

        return min(confidence, 1.0)

    def _generate_reasoning(self, factors: Dict[str, float], level: DifficultyLevel) -> str:
        """Generate human-readable reasoning for the assessment."""
        reasoning_parts = []

        # Analyze each factor
        for factor, score in factors.items():
            factor_name = factor.replace('_', ' ').title()

            if score < 0.3:
                reasoning_parts.append(f"{factor_name} is low ({score:.2f})")
            elif score < 0.7:
                reasoning_parts.append(f"{factor_name} is moderate ({score:.2f})")
            else:
                reasoning_parts.append(f"{factor_name} is high ({score:.2f})")

        # Combine reasoning
        factor_summary = "; ".join(reasoning_parts)

        reasoning = f"Assessed as {level.value} difficulty. {factor_summary}."

        # Add specific insights based on dominant factors
        max_factor = max(factors.items(), key=lambda x: x[1])
        if max_factor[1] > 0.7:
            factor_name = max_factor[0].replace('_', ' ')
            reasoning += f" Primary complexity driver: {factor_name}."

        return reasoning

    def get_difficulty_distribution(self, questions: List[Question]) -> Dict[str, int]:
        """
        Get distribution of difficulty levels across questions.

        Args:
            questions: List of questions to analyze

        Returns:
            Dictionary with difficulty level counts
        """
        assessments = self.assess_questions_batch(questions)

        distribution = {level.value: 0 for level in DifficultyLevel}

        for assessment in assessments:
            distribution[assessment.level.value] += 1

        return distribution

    def filter_by_difficulty(self, questions: List[Question],
                           target_levels: List[DifficultyLevel]) -> List[Question]:
        """
        Filter questions by target difficulty levels.

        Args:
            questions: List of questions to filter
            target_levels: List of desired difficulty levels

        Returns:
            Filtered list of questions
        """
        assessments = self.assess_questions_batch(questions)

        filtered_questions = []
        for question, assessment in zip(questions, assessments):
            if assessment.level in target_levels:
                # Add difficulty metadata to question
                if not hasattr(question, 'metadata'):
                    question.metadata = {}
                question.metadata['difficulty'] = assessment.to_dict()
                filtered_questions.append(question)

        self.log_info(f"Filtered {len(filtered_questions)} questions from {len(questions)} based on difficulty")
        return filtered_questions
