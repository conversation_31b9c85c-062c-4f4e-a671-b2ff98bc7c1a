#!/usr/bin/env python3
"""
QuizAIGen Code Protection and Obfuscation System
===============================================

Comprehensive code protection including obfuscation, license integration,
and anti-tampering measures for commercial distribution.
"""

import os
import sys
import shutil
import subprocess
import tempfile
import zipfile
from pathlib import Path
from typing import List, Dict, Any
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class QuizAIGenProtector:
    """
    Comprehensive protection system for QuizAIGen commercial package.
    """
    
    def __init__(self, source_dir: str, output_dir: str, protection_level: str = "standard"):
        """
        Initialize the protection system.
        
        Args:
            source_dir: Source directory containing the package
            output_dir: Output directory for protected package
            protection_level: Protection level (basic, standard, advanced)
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.protection_level = protection_level
        self.temp_dir = None
        
        # Protection configuration
        self.config = {
            "basic": {
                "obfuscate_names": True,
                "remove_docstrings": True,
                "remove_comments": True,
                "compile_to_pyc": True,
                "add_license_checks": True
            },
            "standard": {
                "obfuscate_names": True,
                "remove_docstrings": True,
                "remove_comments": True,
                "compile_to_pyc": True,
                "add_license_checks": True,
                "encrypt_strings": True,
                "add_anti_debug": True,
                "randomize_structure": True
            },
            "advanced": {
                "obfuscate_names": True,
                "remove_docstrings": True,
                "remove_comments": True,
                "compile_to_pyc": True,
                "add_license_checks": True,
                "encrypt_strings": True,
                "add_anti_debug": True,
                "randomize_structure": True,
                "add_integrity_checks": True,
                "virtualize_code": True,
                "pack_executable": True
            }
        }
        
        # Files to exclude from obfuscation
        self.exclude_files = {
            '__init__.py',  # Keep main init readable
            'setup.py',
            'pyproject.toml',
            'README.md',
            'LICENSE'
        }
        
        # Directories to exclude
        self.exclude_dirs = {
            '__pycache__',
            '.git',
            '.pytest_cache',
            'tests',
            'docs',
            'examples'
        }
    
    def protect_package(self) -> bool:
        """
        Apply comprehensive protection to the package.
        
        Returns:
            bool: True if protection successful
        """
        try:
            logger.info(f"Starting protection with level: {self.protection_level}")
            
            # Create temporary working directory
            self.temp_dir = Path(tempfile.mkdtemp(prefix="quizaigen_protect_"))
            logger.info(f"Working directory: {self.temp_dir}")
            
            # Step 1: Copy source to temp directory
            self._copy_source()
            
            # Step 2: Apply license validation integration
            self._integrate_license_validation()
            
            # Step 3: Apply obfuscation based on protection level
            config = self.config[self.protection_level]
            
            if config.get("remove_docstrings"):
                self._remove_docstrings()
            
            if config.get("remove_comments"):
                self._remove_comments()
            
            if config.get("obfuscate_names"):
                self._obfuscate_names()
            
            if config.get("encrypt_strings"):
                self._encrypt_strings()
            
            if config.get("add_anti_debug"):
                self._add_anti_debug()
            
            if config.get("randomize_structure"):
                self._randomize_structure()
            
            if config.get("add_integrity_checks"):
                self._add_integrity_checks()
            
            if config.get("compile_to_pyc"):
                self._compile_to_pyc()
            
            # Step 4: Create final package
            self._create_final_package()
            
            logger.info("Protection completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Protection failed: {e}")
            return False
        finally:
            # Cleanup
            if self.temp_dir and self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
    
    def _copy_source(self) -> None:
        """Copy source files to temporary directory."""
        logger.info("Copying source files...")
        
        def should_copy(path: Path) -> bool:
            # Skip excluded directories
            for exclude_dir in self.exclude_dirs:
                if exclude_dir in path.parts:
                    return False
            
            # Skip hidden files and directories
            if any(part.startswith('.') for part in path.parts):
                return False
            
            return True
        
        for item in self.source_dir.rglob('*'):
            if should_copy(item):
                relative_path = item.relative_to(self.source_dir)
                dest_path = self.temp_dir / relative_path
                
                if item.is_file():
                    dest_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, dest_path)
                elif item.is_dir():
                    dest_path.mkdir(parents=True, exist_ok=True)
    
    def _integrate_license_validation(self) -> None:
        """Integrate license validation into core modules."""
        logger.info("Integrating license validation...")
        
        # Add license checks to critical modules
        critical_modules = [
            'api/question_generator.py',
            'generators/mcq_generator.py',
            'generators/fill_blank_generator.py',
            'export/export_manager.py'
        ]
        
        license_check_code = '''
# License validation check
from ..core.license_validator import get_license_validator, require_license
from ..models.base_model import ModelTier

def _validate_license_for_feature(tier=ModelTier.PREMIUM, feature=None):
    """Validate license for feature access."""
    validator = get_license_validator()
    current_tier = validator.get_current_tier()
    
    tier_hierarchy = {
        ModelTier.FREE: 0,
        ModelTier.PREMIUM: 1,
        ModelTier.ENTERPRISE: 2
    }
    
    if tier_hierarchy[current_tier] < tier_hierarchy[tier]:
        raise LicenseError(f"Feature requires {tier.value} license or higher")
    
    if feature and not validator.has_feature(feature):
        raise LicenseError(f"License does not include feature: {feature}")

'''
        
        for module_path in critical_modules:
            full_path = self.temp_dir / 'quizaigen' / module_path
            if full_path.exists():
                # Read existing content
                content = full_path.read_text()
                
                # Add license validation at the top
                lines = content.split('\n')
                
                # Find import section end
                import_end = 0
                for i, line in enumerate(lines):
                    if line.strip() and not (line.startswith('import ') or 
                                           line.startswith('from ') or 
                                           line.startswith('#') or
                                           line.startswith('"""') or
                                           line.startswith("'''")):
                        import_end = i
                        break
                
                # Insert license check code
                lines.insert(import_end, license_check_code)
                
                # Write back
                full_path.write_text('\n'.join(lines))
    
    def _remove_docstrings(self) -> None:
        """Remove docstrings from Python files."""
        logger.info("Removing docstrings...")
        
        for py_file in self.temp_dir.rglob('*.py'):
            if py_file.name in self.exclude_files:
                continue
            
            try:
                # Use ast to remove docstrings while preserving functionality
                import ast
                
                content = py_file.read_text()
                tree = ast.parse(content)
                
                # Remove docstrings
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.Module)):
                        if (node.body and 
                            isinstance(node.body[0], ast.Expr) and 
                            isinstance(node.body[0].value, ast.Str)):
                            node.body.pop(0)
                
                # Convert back to code
                import astor
                new_content = astor.to_source(tree)
                py_file.write_text(new_content)
                
            except Exception as e:
                logger.warning(f"Failed to remove docstrings from {py_file}: {e}")
    
    def _remove_comments(self) -> None:
        """Remove comments from Python files."""
        logger.info("Removing comments...")
        
        for py_file in self.temp_dir.rglob('*.py'):
            if py_file.name in self.exclude_files:
                continue
            
            try:
                lines = py_file.read_text().split('\n')
                cleaned_lines = []
                
                for line in lines:
                    # Remove inline comments but preserve strings
                    if '#' in line:
                        # Simple heuristic: if # is in quotes, keep it
                        in_string = False
                        quote_char = None
                        
                        for i, char in enumerate(line):
                            if char in ['"', "'"] and (i == 0 or line[i-1] != '\\'):
                                if not in_string:
                                    in_string = True
                                    quote_char = char
                                elif char == quote_char:
                                    in_string = False
                                    quote_char = None
                            elif char == '#' and not in_string:
                                line = line[:i].rstrip()
                                break
                    
                    if line.strip():  # Keep non-empty lines
                        cleaned_lines.append(line)
                
                py_file.write_text('\n'.join(cleaned_lines))
                
            except Exception as e:
                logger.warning(f"Failed to remove comments from {py_file}: {e}")
    
    def _obfuscate_names(self) -> None:
        """Obfuscate variable and function names."""
        logger.info("Obfuscating names...")
        
        try:
            # Use pyarmor for advanced obfuscation
            subprocess.run([
                sys.executable, '-m', 'pyarmor',
                'obfuscate',
                '--output', str(self.temp_dir / 'obfuscated'),
                '--recursive',
                str(self.temp_dir / 'quizaigen')
            ], check=True, capture_output=True)
            
            # Replace original with obfuscated
            shutil.rmtree(self.temp_dir / 'quizaigen')
            shutil.move(
                self.temp_dir / 'obfuscated' / 'quizaigen',
                self.temp_dir / 'quizaigen'
            )
            
        except subprocess.CalledProcessError:
            logger.warning("Pyarmor not available, using basic name obfuscation")
            self._basic_name_obfuscation()
        except FileNotFoundError:
            logger.warning("Pyarmor not found, using basic name obfuscation")
            self._basic_name_obfuscation()
    
    def _basic_name_obfuscation(self) -> None:
        """Basic name obfuscation without external tools."""
        import random
        import string
        
        # Generate random names
        def random_name(length=8):
            return ''.join(random.choices(string.ascii_letters, k=length))
        
        # This is a simplified version - in practice, you'd need
        # sophisticated AST manipulation to safely rename variables
        logger.info("Applying basic name obfuscation...")
    
    def _encrypt_strings(self) -> None:
        """Encrypt string literals in the code."""
        logger.info("Encrypting strings...")
        # Implementation would encrypt string literals and add decryption at runtime
        pass
    
    def _add_anti_debug(self) -> None:
        """Add anti-debugging measures."""
        logger.info("Adding anti-debug measures...")
        # Implementation would add checks for debuggers, profilers, etc.
        pass
    
    def _randomize_structure(self) -> None:
        """Randomize code structure."""
        logger.info("Randomizing structure...")
        # Implementation would randomize function order, add dummy code, etc.
        pass
    
    def _add_integrity_checks(self) -> None:
        """Add integrity checks to detect tampering."""
        logger.info("Adding integrity checks...")
        # Implementation would add checksums and validation
        pass
    
    def _compile_to_pyc(self) -> None:
        """Compile Python files to bytecode."""
        logger.info("Compiling to bytecode...")
        
        import py_compile
        
        for py_file in self.temp_dir.rglob('*.py'):
            if py_file.name in self.exclude_files:
                continue
            
            try:
                pyc_file = py_file.with_suffix('.pyc')
                py_compile.compile(py_file, pyc_file, doraise=True)
                py_file.unlink()  # Remove source file
            except Exception as e:
                logger.warning(f"Failed to compile {py_file}: {e}")
    
    def _create_final_package(self) -> None:
        """Create the final protected package."""
        logger.info("Creating final package...")
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy protected files to output
        if (self.temp_dir / 'quizaigen').exists():
            if (self.output_dir / 'quizaigen').exists():
                shutil.rmtree(self.output_dir / 'quizaigen')
            
            shutil.copytree(
                self.temp_dir / 'quizaigen',
                self.output_dir / 'quizaigen'
            )
        
        # Copy other necessary files
        for file_name in ['pyproject.toml', 'README.md', 'LICENSE']:
            src_file = self.temp_dir / file_name
            if src_file.exists():
                shutil.copy2(src_file, self.output_dir / file_name)


def main():
    """Main entry point for the protection script."""
    parser = argparse.ArgumentParser(description="QuizAIGen Code Protection System")
    parser.add_argument("source_dir", help="Source directory containing the package")
    parser.add_argument("output_dir", help="Output directory for protected package")
    parser.add_argument(
        "--protection-level",
        choices=["basic", "standard", "advanced"],
        default="standard",
        help="Protection level to apply"
    )
    
    args = parser.parse_args()
    
    protector = QuizAIGenProtector(
        args.source_dir,
        args.output_dir,
        args.protection_level
    )
    
    success = protector.protect_package()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
