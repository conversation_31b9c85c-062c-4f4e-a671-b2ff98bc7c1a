"""
QuizAIGen SaaS Integration Module
===============================

Seamless integration of QuizAIGen commercial library with SaaS application.
Handles license validation, feature gating, and usage tracking.
"""

import os
import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from functools import wraps
import json

import redis
from celery import Celery
from django.conf import settings
from django.core.cache import cache
from django.db import models
from django.contrib.auth.models import User

# QuizAIGen imports (commercial package)
try:
    import quizaigen
    from quizaigen import (
        QuestionGenerator, 
        BatchProcessor, 
        ExportManager,
        get_license_info,
        get_current_tier,
        ModelTier,
        LicenseError
    )
    QUIZAIGEN_AVAILABLE = True
except ImportError as e:
    logging.warning(f"QuizAIGen not available: {e}")
    QUIZAIGEN_AVAILABLE = False

logger = logging.getLogger(__name__)

# Celery app for background tasks
celery_app = Celery('quizaigen_saas')


class QuizAIGenIntegration:
    """
    Main integration class for QuizAIGen SaaS application.
    """
    
    def __init__(self):
        """Initialize QuizAIGen integration."""
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.license_cache_timeout = 3600  # 1 hour
        self.usage_tracking_enabled = getattr(settings, 'QUIZAIGEN_USAGE_TRACKING', True)
        
        # Initialize QuizAIGen if available
        if QUIZAIGEN_AVAILABLE:
            self._initialize_quizaigen()
        else:
            logger.error("QuizAIGen library not available")
    
    def _initialize_quizaigen(self) -> None:
        """Initialize QuizAIGen with license."""
        try:
            # Try to get license from environment or settings
            license_key = (
                os.getenv('QUIZAIGEN_LICENSE_KEY') or
                getattr(settings, 'QUIZAIGEN_LICENSE_KEY', None)
            )
            
            if license_key:
                quizaigen.initialize_license(license_key)
                logger.info("QuizAIGen initialized with license")
            else:
                logger.warning("No QuizAIGen license key found")
                
        except Exception as e:
            logger.error(f"Failed to initialize QuizAIGen: {e}")
    
    def get_license_status(self) -> Dict[str, Any]:
        """Get current license status with caching."""
        cache_key = "quizaigen_license_status"
        
        # Try cache first
        cached_status = cache.get(cache_key)
        if cached_status:
            return cached_status
        
        # Get fresh status
        if QUIZAIGEN_AVAILABLE:
            status = get_license_info()
        else:
            status = {"status": "unavailable", "tier": "free"}
        
        # Cache for future use
        cache.set(cache_key, status, self.license_cache_timeout)
        
        return status
    
    def check_feature_access(self, feature: str, user: Optional[User] = None) -> bool:
        """
        Check if user has access to specific feature.
        
        Args:
            feature: Feature name to check
            user: User instance (for user-specific checks)
            
        Returns:
            bool: True if feature is accessible
        """
        if not QUIZAIGEN_AVAILABLE:
            return False
        
        try:
            license_status = self.get_license_status()
            
            # Check license tier
            current_tier = get_current_tier()
            
            # Feature mapping
            feature_requirements = {
                'basic_mcq': ModelTier.FREE,
                'basic_boolean': ModelTier.FREE,
                'basic_faq': ModelTier.FREE,
                'fill_blank': ModelTier.PREMIUM,
                'advanced_export': ModelTier.PREMIUM,
                'batch_processing': ModelTier.PREMIUM,
                'custom_training': ModelTier.ENTERPRISE,
                'multilingual': ModelTier.ENTERPRISE,
                'api_access': ModelTier.PREMIUM,
                'white_label': ModelTier.ENTERPRISE
            }
            
            required_tier = feature_requirements.get(feature, ModelTier.ENTERPRISE)
            
            # Check tier hierarchy
            tier_hierarchy = {
                ModelTier.FREE: 0,
                ModelTier.PREMIUM: 1,
                ModelTier.ENTERPRISE: 2
            }
            
            has_access = tier_hierarchy[current_tier] >= tier_hierarchy[required_tier]
            
            # Log access attempt
            if self.usage_tracking_enabled:
                self._track_feature_access(feature, user, has_access)
            
            return has_access
            
        except Exception as e:
            logger.error(f"Error checking feature access: {e}")
            return False
    
    def _track_feature_access(self, feature: str, user: Optional[User], granted: bool) -> None:
        """Track feature access for analytics."""
        try:
            tracking_data = {
                'timestamp': datetime.now().isoformat(),
                'feature': feature,
                'user_id': user.id if user else None,
                'access_granted': granted,
                'license_tier': get_current_tier().value if QUIZAIGEN_AVAILABLE else 'unknown'
            }
            
            # Store in Redis for batch processing
            self.redis_client.lpush('feature_access_log', json.dumps(tracking_data))
            
        except Exception as e:
            logger.error(f"Error tracking feature access: {e}")


class QuizAIGenService:
    """
    Service class for QuizAIGen operations in SaaS context.
    """
    
    def __init__(self, integration: QuizAIGenIntegration):
        """Initialize service with integration instance."""
        self.integration = integration
        self.generator = None
        self.batch_processor = None
        self.export_manager = None
        
        if QUIZAIGEN_AVAILABLE:
            self._initialize_components()
    
    def _initialize_components(self) -> None:
        """Initialize QuizAIGen components."""
        try:
            self.generator = QuestionGenerator()
            self.batch_processor = BatchProcessor()
            self.export_manager = ExportManager()
            logger.info("QuizAIGen components initialized")
        except Exception as e:
            logger.error(f"Failed to initialize QuizAIGen components: {e}")
    
    async def generate_questions(self, 
                               text: str, 
                               question_type: str, 
                               num_questions: int = 5,
                               user: Optional[User] = None,
                               **kwargs) -> Dict[str, Any]:
        """
        Generate questions with license validation.
        
        Args:
            text: Input text
            question_type: Type of questions to generate
            num_questions: Number of questions
            user: User making the request
            **kwargs: Additional parameters
            
        Returns:
            Dict containing questions or error information
        """
        if not QUIZAIGEN_AVAILABLE or not self.generator:
            return {
                'success': False,
                'error': 'QuizAIGen not available',
                'questions': []
            }
        
        # Check feature access
        feature_map = {
            'mcq': 'basic_mcq',
            'boolean': 'basic_boolean',
            'faq': 'basic_faq',
            'fill_blank': 'fill_blank'
        }
        
        required_feature = feature_map.get(question_type, 'basic_mcq')
        
        if not self.integration.check_feature_access(required_feature, user):
            return {
                'success': False,
                'error': f'Feature {question_type} not available with current license',
                'questions': [],
                'upgrade_required': True
            }
        
        try:
            # Generate questions
            if question_type == 'mcq':
                questions = self.generator.generate_mcq(text, num_questions, **kwargs)
            elif question_type == 'boolean':
                questions = self.generator.generate_boolean(text, num_questions, **kwargs)
            elif question_type == 'faq':
                questions = self.generator.generate_faq(text, num_questions, **kwargs)
            elif question_type == 'fill_blank':
                questions = self.generator.generate_fill_blank(text, num_questions, **kwargs)
            else:
                return {
                    'success': False,
                    'error': f'Unsupported question type: {question_type}',
                    'questions': []
                }
            
            # Track usage
            self._track_generation_usage(user, question_type, len(questions))
            
            return {
                'success': True,
                'questions': [q.to_dict() for q in questions],
                'count': len(questions),
                'license_tier': get_current_tier().value
            }
            
        except LicenseError as e:
            return {
                'success': False,
                'error': str(e),
                'questions': [],
                'license_error': True
            }
        except Exception as e:
            logger.error(f"Question generation failed: {e}")
            return {
                'success': False,
                'error': 'Question generation failed',
                'questions': []
            }
    
    def _track_generation_usage(self, user: Optional[User], question_type: str, count: int) -> None:
        """Track question generation usage."""
        if not self.integration.usage_tracking_enabled:
            return
        
        try:
            usage_data = {
                'timestamp': datetime.now().isoformat(),
                'user_id': user.id if user else None,
                'question_type': question_type,
                'questions_generated': count,
                'license_tier': get_current_tier().value if QUIZAIGEN_AVAILABLE else 'unknown'
            }
            
            self.integration.redis_client.lpush('generation_usage_log', json.dumps(usage_data))
            
        except Exception as e:
            logger.error(f"Error tracking generation usage: {e}")


# Django Models for SaaS Integration
class QuizAIGenUsage(models.Model):
    """Track QuizAIGen usage in SaaS application."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    feature = models.CharField(max_length=100)
    usage_count = models.IntegerField(default=0)
    last_used = models.DateTimeField(auto_now=True)
    license_tier = models.CharField(max_length=50, default='free')
    
    class Meta:
        unique_together = ['user', 'feature']
    
    def __str__(self):
        return f"{self.user.username} - {self.feature}: {self.usage_count}"


class QuizAIGenSession(models.Model):
    """Track QuizAIGen sessions for billing and analytics."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_id = models.CharField(max_length=100, unique=True)
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    questions_generated = models.IntegerField(default=0)
    features_used = models.JSONField(default=list)
    license_tier = models.CharField(max_length=50)
    
    def __str__(self):
        return f"Session {self.session_id} - {self.user.username}"


# Decorators for feature gating
def require_quizaigen_feature(feature: str):
    """Decorator to require specific QuizAIGen feature access."""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            integration = QuizAIGenIntegration()
            
            if not integration.check_feature_access(feature, request.user):
                from django.http import JsonResponse
                return JsonResponse({
                    'error': f'Feature {feature} not available with current license',
                    'upgrade_required': True
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


# Celery tasks for background processing
@celery_app.task
def process_batch_questions(batch_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process batch question generation in background."""
    try:
        integration = QuizAIGenIntegration()
        service = QuizAIGenService(integration)
        
        results = []
        for item in batch_data['items']:
            result = asyncio.run(service.generate_questions(
                text=item['text'],
                question_type=item['question_type'],
                num_questions=item['num_questions'],
                user=None  # Background task
            ))
            results.append(result)
        
        return {
            'success': True,
            'results': results,
            'total_items': len(results)
        }
        
    except Exception as e:
        logger.error(f"Batch processing failed: {e}")
        return {
            'success': False,
            'error': str(e)
        }


@celery_app.task
def sync_license_status():
    """Periodic task to sync license status."""
    try:
        integration = QuizAIGenIntegration()
        status = integration.get_license_status()
        
        # Update cached status
        cache.set('quizaigen_license_status', status, 3600)
        
        # Log status change if needed
        previous_status = cache.get('quizaigen_previous_status')
        if previous_status and previous_status != status:
            logger.info(f"License status changed: {previous_status} -> {status}")
        
        cache.set('quizaigen_previous_status', status, 3600)
        
        return {'success': True, 'status': status}
        
    except Exception as e:
        logger.error(f"License sync failed: {e}")
        return {'success': False, 'error': str(e)}


@celery_app.task
def process_usage_analytics():
    """Process usage analytics from Redis logs."""
    try:
        integration = QuizAIGenIntegration()
        
        # Process feature access logs
        while True:
            log_data = integration.redis_client.rpop('feature_access_log')
            if not log_data:
                break
            
            data = json.loads(log_data)
            # Process and store in database or analytics service
            logger.info(f"Processed feature access: {data}")
        
        # Process generation usage logs
        while True:
            log_data = integration.redis_client.rpop('generation_usage_log')
            if not log_data:
                break
            
            data = json.loads(log_data)
            # Process and store in database or analytics service
            logger.info(f"Processed generation usage: {data}")
        
        return {'success': True}
        
    except Exception as e:
        logger.error(f"Analytics processing failed: {e}")
        return {'success': False, 'error': str(e)}


# Global integration instance
_integration_instance = None


def get_quizaigen_integration() -> QuizAIGenIntegration:
    """Get global QuizAIGen integration instance."""
    global _integration_instance
    if _integration_instance is None:
        _integration_instance = QuizAIGenIntegration()
    return _integration_instance


def get_quizaigen_service() -> QuizAIGenService:
    """Get QuizAIGen service instance."""
    integration = get_quizaigen_integration()
    return QuizAIGenService(integration)
