{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "collapsed_sections": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🎯 QuizAIGen - Getting Started\n", "\n", "Welcome to QuizAIGen! This notebook will guide you through the basics of generating questions using AI.\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/yourusername/quizaigen/blob/main/notebooks/QuizAIGen_Getting_Started.ipynb)\n", "\n", "## What is Quiz<PERSON><PERSON><PERSON>?\n", "\n", "QuizAIGen is a Python library that uses AI to automatically generate various types of questions from text content:\n", "- Multiple Choice Questions (MCQ)\n", "- <PERSON><PERSON><PERSON> (True/False) Questions\n", "- FAQ Questions\n", "- Fill-in-the-blank Questions\n", "- Short Answer Questions\n", "\n", "## Features\n", "- 🤖 AI-powered question generation\n", "- 🌍 Multi-language support\n", "- 📄 Document processing (PDF, Word, etc.)\n", "- 📊 Quality control and filtering\n", "- 📤 Multiple export formats (JSON, CSV, QTI, Moodle XML)\n", "- ⚡ Batch processing capabilities"]}, {"cell_type": "markdown", "metadata": {"id": "installation"}, "source": ["## 📦 Installation\n", "\n", "Let's start by installing QuizAIGen and its dependencies:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install"}, "outputs": [], "source": ["# Install QuizAIGen\n", "!pip install quizaigen\n", "\n", "# Install additional dependencies for Colab\n", "!pip install spacy\n", "!python -m spacy download en_core_web_sm\n", "\n", "# Install system dependencies (if needed)\n", "!apt-get update\n", "!apt-get install -y tesseract-ocr tesseract-ocr-eng poppler-utils"]}, {"cell_type": "markdown", "metadata": {"id": "basic_usage"}, "source": ["## 🚀 Basic Usage\n", "\n", "Let's start with a simple example of generating questions from text:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "import"}, "outputs": [], "source": ["# Import QuizAIGen\n", "from quizaigen import QuestionGenerator\n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "sample_text"}, "outputs": [], "source": ["# Sample text about Python programming\n", "sample_text = \"\"\"\n", "Python is a high-level, interpreted programming language with dynamic semantics.\n", "Its high-level built-in data structures, combined with dynamic typing and dynamic binding,\n", "make it very attractive for Rapid Application Development, as well as for use as a\n", "scripting or glue language to connect existing components together.\n", "\n", "Python's simple, easy to learn syntax emphasizes readability and therefore reduces\n", "the cost of program maintenance. Python supports modules and packages, which encourages\n", "program modularity and code reuse. The Python interpreter and the extensive standard\n", "library are available in source or binary form without charge for all major platforms,\n", "and can be freely distributed.\n", "\"\"\"\n", "\n", "print(\"Sample text loaded!\")\n", "print(f\"Text length: {len(sample_text)} characters\")"]}, {"cell_type": "markdown", "metadata": {"id": "mcq_generation"}, "source": ["### 🎯 Multiple Choice Questions (MCQ)\n", "\n", "Let's generate some multiple choice questions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "generate_mcq"}, "outputs": [], "source": ["# Initialize the question generator\n", "generator = QuestionGenerator()\n", "\n", "# Generate MCQ questions\n", "print(\"🎯 Generating Multiple Choice Questions...\")\n", "mcq_questions = generator.generate_mcq(sample_text, num_questions=3)\n", "\n", "# Display the questions\n", "for i, question in enumerate(mcq_questions, 1):\n", "    print(f\"\\n--- Question {i} ---\")\n", "    print(f\"Q: {question.question}\")\n", "    \n", "    for j, option in enumerate(question.options, 1):\n", "        marker = \"✓\" if option == question.answer else \" \"\n", "        print(f\"  {j}. {option} {marker}\")\n", "    \n", "    print(f\"Answer: {question.answer}\")\n", "    if question.explanation:\n", "        print(f\"Explanation: {question.explanation}\")"]}, {"cell_type": "markdown", "metadata": {"id": "boolean_generation"}, "source": ["### ✅ Boolean Questions (True/False)\n", "\n", "Now let's generate some true/false questions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "generate_boolean"}, "outputs": [], "source": ["# Generate Boolean questions\n", "print(\"✅ Generating Boolean Questions...\")\n", "boolean_questions = generator.generate_boolean(sample_text, num_questions=3)\n", "\n", "# Display the questions\n", "for i, question in enumerate(boolean_questions, 1):\n", "    print(f\"\\n--- Question {i} ---\")\n", "    print(f\"Q: {question.question}\")\n", "    print(f\"Answer: {question.answer}\")\n", "    if question.explanation:\n", "        print(f\"Explanation: {question.explanation}\")"]}, {"cell_type": "markdown", "metadata": {"id": "faq_generation"}, "source": ["### ❓ FAQ Questions\n", "\n", "Let's generate some FAQ-style questions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "generate_faq"}, "outputs": [], "source": ["# Generate FAQ questions\n", "print(\"❓ Generating FAQ Questions...\")\n", "faq_questions = generator.generate_faq(sample_text, num_questions=2)\n", "\n", "# Display the questions\n", "for i, question in enumerate(faq_questions, 1):\n", "    print(f\"\\n--- Question {i} ---\")\n", "    print(f\"Q: {question.question}\")\n", "    print(f\"A: {question.answer}\")"]}, {"cell_type": "markdown", "metadata": {"id": "export_section"}, "source": ["## 📤 Exporting Questions\n", "\n", "QuizAIGen supports multiple export formats. Let's export our questions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "export_questions"}, "outputs": [], "source": ["from quizaigen import ExportManager\n", "\n", "# Combine all questions\n", "all_questions = mcq_questions + boolean_questions + faq_questions\n", "\n", "print(f\"Total questions generated: {len(all_questions)}\")\n", "\n", "# Initialize export manager\n", "exporter = ExportManager()\n", "\n", "# Export to JSON\n", "json_output = exporter.export_to_json(all_questions)\n", "print(\"\\n📄 JSON Export:\")\n", "print(json.dumps(json_output, indent=2)[:500] + \"...\")\n", "\n", "# Save to file\n", "with open('generated_questions.json', 'w') as f:\n", "    json.dump(json_output, f, indent=2)\n", "\n", "print(\"\\n✅ Questions exported to 'generated_questions.json'\")"]}, {"cell_type": "markdown", "metadata": {"id": "advanced_features"}, "source": ["## 🔧 Advanced Features\n", "\n", "### Quality Control\n", "\n", "QuizAIGen includes quality control features to ensure high-quality questions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "quality_control"}, "outputs": [], "source": ["# Generate questions with quality control\n", "print(\"🔍 Generating questions with quality control...\")\n", "\n", "high_quality_questions = generator.generate_mcq(\n", "    sample_text,\n", "    num_questions=5,\n", "    min_quality_score=0.7,  # Minimum quality threshold\n", "    remove_duplicates=True   # Remove duplicate questions\n", ")\n", "\n", "print(f\"Generated {len(high_quality_questions)} high-quality questions\")\n", "\n", "# Display quality scores\n", "for i, question in enumerate(high_quality_questions, 1):\n", "    quality_score = question.metadata.get('quality_score', 'N/A')\n", "    print(f\"Question {i}: Quality Score = {quality_score}\")\n", "    print(f\"  {question.question[:60]}...\")"]}, {"cell_type": "markdown", "metadata": {"id": "batch_processing"}, "source": ["### Batch Processing\n", "\n", "Process multiple texts at once:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "batch_processing_code"}, "outputs": [], "source": ["from quizaigen import BatchProcessor\n", "\n", "# Multiple texts to process\n", "texts = [\n", "    \"Machine learning is a subset of artificial intelligence that focuses on algorithms.\",\n", "    \"Deep learning uses neural networks with multiple layers to model data.\",\n", "    \"Natural language processing enables computers to understand human language.\"\n", "]\n", "\n", "# Initialize batch processor\n", "batch_processor = BatchProcessor()\n", "\n", "print(\"⚡ Processing multiple texts...\")\n", "\n", "# Process all texts\n", "batch_results = []\n", "for i, text in enumerate(texts, 1):\n", "    print(f\"Processing text {i}/{len(texts)}...\")\n", "    questions = batch_processor.process_text(\n", "        text,\n", "        question_types=[\"mcq\", \"boolean\"],\n", "        num_questions_per_type=1\n", "    )\n", "    batch_results.extend(questions)\n", "\n", "print(f\"\\n✅ Batch processing complete! Generated {len(batch_results)} questions\")"]}, {"cell_type": "markdown", "metadata": {"id": "document_processing"}, "source": ["### Document Processing\n", "\n", "QuizAIGen can process various document formats:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "document_demo"}, "outputs": [], "source": ["# Create a sample text file\n", "sample_document = \"\"\"\n", "Artificial Intelligence Overview\n", "\n", "Artificial Intelligence (AI) is intelligence demonstrated by machines,\n", "in contrast to the natural intelligence displayed by humans and animals.\n", "Leading AI textbooks define the field as the study of \"intelligent agents\":\n", "any device that perceives its environment and takes actions that maximize\n", "its chance of successfully achieving its goals.\n", "\n", "Applications of AI include:\n", "- Machine learning and data analysis\n", "- Natural language processing\n", "- Computer vision and image recognition\n", "- Robotics and automation\n", "- Expert systems and decision support\n", "\"\"\"\n", "\n", "# Save to file\n", "with open('sample_document.txt', 'w') as f:\n", "    f.write(sample_document)\n", "\n", "# Process the document\n", "from quizaigen.inputs import DocumentProcessor\n", "\n", "doc_processor = DocumentProcessor()\n", "processed_text = doc_processor.process_file('sample_document.txt')\n", "\n", "print(\"📄 Document processed successfully!\")\n", "print(f\"Extracted text length: {len(processed_text)} characters\")\n", "\n", "# Generate questions from the document\n", "doc_questions = generator.generate_mcq(processed_text, num_questions=2)\n", "print(f\"\\n🎯 Generated {len(doc_questions)} questions from document\")\n", "\n", "for i, question in enumerate(doc_questions, 1):\n", "    print(f\"\\nDocument Question {i}:\")\n", "    print(f\"Q: {question.question}\")\n", "    print(f\"A: {question.answer}\")"]}, {"cell_type": "markdown", "metadata": {"id": "next_steps"}, "source": ["## 🎉 Next Steps\n", "\n", "Congratulations! You've learned the basics of QuizAIGen. Here are some next steps:\n", "\n", "### 📚 More Notebooks\n", "- **Advanced Features**: Explore multilingual support, custom models, and advanced configurations\n", "- **Document Processing**: Learn to process PDFs, Word documents, and web content\n", "- **Export Formats**: Master different export formats for various LMS platforms\n", "- **Quality Control**: Deep dive into quality assessment and filtering\n", "\n", "### 🔗 Resources\n", "- [GitHub Repository](https://github.com/yourusername/quizaigen)\n", "- [Documentation](https://quizaigen.readthedocs.io)\n", "- [API Reference](https://quizaigen.readthedocs.io/en/latest/api/)\n", "- [Examples](https://github.com/yourusername/quizaigen/tree/main/examples)\n", "\n", "### 💡 Tips\n", "- Use longer, well-structured texts for better question quality\n", "- Experiment with different question types for variety\n", "- Apply quality filters to ensure high-quality output\n", "- Use batch processing for efficiency with multiple documents\n", "\n", "Happy question generating! 🚀"]}, {"cell_type": "markdown", "metadata": {"id": "cleanup"}, "source": ["## 🧹 Cleanup\n", "\n", "Clean up temporary files:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cleanup_code"}, "outputs": [], "source": ["import os\n", "\n", "# List generated files\n", "files_to_clean = ['generated_questions.json', 'sample_document.txt']\n", "\n", "for file in files_to_clean:\n", "    if os.path.exists(file):\n", "        print(f\"📁 Found: {file}\")\n", "        # Uncomment the next line to delete files\n", "        # os.remove(file)\n", "        # print(f\"🗑️ Deleted: {file}\")\n", "\n", "print(\"\\n✅ Cleanup complete!\")"]}]}