"""
QuizAIGen License Management API
===============================

FastAPI-based license management system for QuizAIGen commercial distribution.
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
import structlog

from .database import init_db, get_db
from .models import Customer, License, LicenseKey
from .auth import verify_token, create_access_token
from .license_manager import LicenseManager
from .schemas import (
    CustomerCreate, CustomerResponse,
    LicenseCreate, LicenseResponse,
    LicenseValidationRequest, LicenseValidationResponse,
    TokenResponse
)

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Security
security = HTTPBearer()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting QuizAIGen License API")
    await init_db()
    yield
    # Shutdown
    logger.info("Shutting down QuizAIGen License API")


# Create FastAPI app
app = FastAPI(
    title="QuizAIGen License Management API",
    description="Commercial license management system for QuizAIGen",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://portal.quizaigen.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize license manager
license_manager = LicenseManager()


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "license-api"}


@app.post("/auth/login", response_model=TokenResponse)
async def login(email: str, password: str, db=Depends(get_db)):
    """Authenticate customer and return access token."""
    try:
        customer = await Customer.authenticate(db, email, password)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        token = create_access_token({"sub": str(customer.id), "email": customer.email})
        
        logger.info("Customer login successful", customer_id=customer.id, email=email)
        
        return TokenResponse(
            access_token=token,
            token_type="bearer",
            customer_id=customer.id
        )
        
    except Exception as e:
        logger.error("Login failed", email=email, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@app.post("/customers", response_model=CustomerResponse)
async def create_customer(customer_data: CustomerCreate, db=Depends(get_db)):
    """Create new customer account."""
    try:
        # Check if customer already exists
        existing = await Customer.get_by_email(db, customer_data.email)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Customer already exists"
            )
        
        # Create customer
        customer = await Customer.create(db, customer_data)
        
        logger.info("Customer created", customer_id=customer.id, email=customer.email)
        
        return CustomerResponse.from_orm(customer)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Customer creation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Customer creation failed"
        )


@app.get("/customers/me", response_model=CustomerResponse)
async def get_current_customer(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db=Depends(get_db)
):
    """Get current customer information."""
    try:
        payload = verify_token(credentials.credentials)
        customer_id = payload.get("sub")
        
        customer = await Customer.get_by_id(db, customer_id)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Customer not found"
            )
        
        return CustomerResponse.from_orm(customer)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get customer", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get customer information"
        )


@app.post("/licenses", response_model=LicenseResponse)
async def create_license(
    license_data: LicenseCreate,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db=Depends(get_db)
):
    """Create new license for customer."""
    try:
        payload = verify_token(credentials.credentials)
        customer_id = payload.get("sub")
        
        # Verify customer exists
        customer = await Customer.get_by_id(db, customer_id)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Customer not found"
            )
        
        # Create license
        license_data.customer_id = customer_id
        license_obj = await License.create(db, license_data)
        
        # Generate license key
        license_key = await license_manager.generate_license_key(
            customer_id=customer_id,
            license_id=license_obj.id,
            tier=license_data.tier,
            features=license_data.features,
            expires_at=license_data.expires_at
        )
        
        logger.info(
            "License created",
            customer_id=customer_id,
            license_id=license_obj.id,
            tier=license_data.tier
        )
        
        return LicenseResponse.from_orm(license_obj)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("License creation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="License creation failed"
        )


@app.post("/licenses/validate", response_model=LicenseValidationResponse)
async def validate_license(request: LicenseValidationRequest):
    """Validate license key and return license information."""
    try:
        validation_result = await license_manager.validate_license_key(
            license_key=request.license_key,
            hardware_fingerprint=request.hardware_fingerprint,
            product=request.product,
            version=request.version
        )
        
        if not validation_result.valid:
            logger.warning(
                "License validation failed",
                license_key=request.license_key[:16] + "...",
                reason=validation_result.error
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=validation_result.error or "Invalid license"
            )
        
        # Track usage
        await license_manager.track_usage(
            license_key=request.license_key,
            action="validation",
            metadata={
                "hardware_fingerprint": request.hardware_fingerprint,
                "product": request.product,
                "version": request.version
            }
        )
        
        logger.info(
            "License validation successful",
            customer_id=validation_result.customer_id,
            tier=validation_result.tier
        )
        
        return LicenseValidationResponse(
            valid=True,
            customer_id=validation_result.customer_id,
            tier=validation_result.tier,
            features=validation_result.features,
            expires_at=validation_result.expires_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("License validation error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="License validation failed"
        )


@app.post("/usage")
async def track_usage(
    license_key: str,
    action: str,
    metadata: Dict[str, Any] = None
):
    """Track license usage for analytics."""
    try:
        await license_manager.track_usage(
            license_key=license_key,
            action=action,
            metadata=metadata or {}
        )
        
        return {"status": "success"}
        
    except Exception as e:
        logger.error("Usage tracking failed", error=str(e))
        # Don't fail the request for usage tracking errors
        return {"status": "error", "message": str(e)}


@app.get("/licenses/me")
async def get_my_licenses(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db=Depends(get_db)
):
    """Get all licenses for current customer."""
    try:
        payload = verify_token(credentials.credentials)
        customer_id = payload.get("sub")
        
        licenses = await License.get_by_customer_id(db, customer_id)
        
        return [LicenseResponse.from_orm(license) for license in licenses]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get licenses", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get licenses"
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
