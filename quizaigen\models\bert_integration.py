"""
BERT Model Integration for QuizAIGen

Provides BERT-based answer validation and question quality assessment.
Focuses on improving answer accuracy and question-answer relevance.
"""

import time
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from .base_model import BaseModel, ModelConfig, ModelOutput, ModelTier
from ..core.exceptions import ProcessingError

try:
    from transformers import Bert<PERSON>okenizer, BertForSequenceClassification, BertModel
    import torch
    import torch.nn.functional as F
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False


@dataclass
class AnswerValidationResult:
    """Result of answer validation."""
    is_valid: bool
    confidence: float
    relevance_score: float
    semantic_similarity: float
    explanation: str
    metadata: Dict[str, Any]


class BERTAnswerValidator(BaseModel):
    """
    BERT-based answer validator for premium features.
    
    Capabilities:
    - Answer-context relevance validation
    - Semantic similarity assessment
    - Question-answer pair quality scoring
    - Distractor quality evaluation (for MCQs)
    """
    
    # Model configurations for different tiers
    TIER_MODELS = {
        ModelTier.PREMIUM: {
            "model_name": "bert-base-uncased",
            "max_length": 256,
            "batch_size": 4
        },
        ModelTier.ENTERPRISE: {
            "model_name": "bert-large-uncased",
            "max_length": 512,
            "batch_size": 8
        }
    }
    
    def __init__(self, config: Optional[ModelConfig] = None, tier: ModelTier = ModelTier.PREMIUM):
        """
        Initialize BERT answer validator.
        
        Args:
            config: Model configuration (optional)
            tier: Model tier for feature access
        """
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError(
                "BERT integration requires transformers and torch. "
                "Install with: pip install transformers torch"
            )
        
        # Use tier-specific defaults if no config provided
        if config is None:
            tier_config = self.TIER_MODELS.get(tier, self.TIER_MODELS[ModelTier.PREMIUM])
            config = ModelConfig(
                model_name=tier_config["model_name"],
                max_length=tier_config["max_length"],
                batch_size=tier_config["batch_size"],
                tier=tier
            )
        
        super().__init__(config)
        self.tier = tier
        self.bert_model = None  # For embeddings
    
    def load_model(self) -> None:
        """Load BERT model and tokenizer."""
        try:
            self.log_info(f"Loading BERT model: {self.config.model_name}")
            
            # Load tokenizer
            self.tokenizer = BertTokenizer.from_pretrained(
                self.config.model_name,
                cache_dir=self.config.cache_dir
            )
            
            # Load BERT model for embeddings
            self.bert_model = BertModel.from_pretrained(
                self.config.model_name,
                cache_dir=self.config.cache_dir
            )
            
            # Move to specified device
            if self.config.device != "cpu" and torch.cuda.is_available():
                self.bert_model = self.bert_model.to(self.config.device)
                self.log_info(f"Model moved to {self.config.device}")
            
            self.is_loaded = True
            self.log_info("BERT model loaded successfully")
            
        except Exception as e:
            self.log_error(f"Failed to load BERT model: {str(e)}")
            raise ProcessingError(
                stage="model_loading",
                message=f"Failed to load BERT model: {str(e)}"
            )
    
    def _inference(self, input_text: str, **kwargs) -> ModelOutput:
        """
        Basic BERT inference (for compatibility with base class).
        
        Args:
            input_text: Input text
            **kwargs: Additional parameters
            
        Returns:
            Model output
        """
        # This is a placeholder - actual functionality is in specific methods
        start_time = time.time()
        
        embeddings = self._get_embeddings(input_text)
        
        processing_time = time.time() - start_time
        
        return ModelOutput(
            text=input_text,
            confidence=0.8,
            metadata={
                "embeddings_shape": embeddings.shape if embeddings is not None else None,
                "model_name": self.config.model_name
            },
            processing_time=processing_time,
            model_name=self.config.model_name
        )
    
    def validate_answer(self, question: str, answer: str, context: str = "") -> AnswerValidationResult:
        """
        Validate if an answer is appropriate for a question.
        
        Args:
            question: The question text
            answer: The proposed answer
            context: Optional context text
            
        Returns:
            Answer validation result
        """
        self._check_tier_access()
        
        if not self.is_loaded:
            self.load_model()
        
        try:
            # Get embeddings for question, answer, and context
            question_emb = self._get_embeddings(question)
            answer_emb = self._get_embeddings(answer)
            context_emb = self._get_embeddings(context) if context else None
            
            # Calculate semantic similarity
            qa_similarity = self._cosine_similarity(question_emb, answer_emb)
            
            # Calculate context relevance if context provided
            context_relevance = 0.5  # Default
            if context_emb is not None:
                context_relevance = max(
                    self._cosine_similarity(context_emb, question_emb),
                    self._cosine_similarity(context_emb, answer_emb)
                )
            
            # Determine validity based on thresholds
            similarity_threshold = 0.3 if self.tier == ModelTier.PREMIUM else 0.4
            is_valid = qa_similarity > similarity_threshold
            
            # Calculate overall confidence
            confidence = self._calculate_validation_confidence(
                qa_similarity, context_relevance, question, answer
            )
            
            # Generate explanation
            explanation = self._generate_validation_explanation(
                is_valid, qa_similarity, context_relevance
            )
            
            return AnswerValidationResult(
                is_valid=is_valid,
                confidence=confidence,
                relevance_score=context_relevance,
                semantic_similarity=qa_similarity,
                explanation=explanation,
                metadata={
                    "model_name": self.config.model_name,
                    "tier": self.tier.value,
                    "similarity_threshold": similarity_threshold
                }
            )
            
        except Exception as e:
            self.log_error(f"Answer validation failed: {str(e)}")
            return AnswerValidationResult(
                is_valid=False,
                confidence=0.0,
                relevance_score=0.0,
                semantic_similarity=0.0,
                explanation=f"Validation failed: {str(e)}",
                metadata={"error": str(e)}
            )
    
    def score_question_quality(self, question: str, context: str = "") -> Dict[str, float]:
        """
        Score the quality of a question.
        
        Args:
            question: Question text
            context: Optional context text
            
        Returns:
            Quality scores dictionary
        """
        self._check_tier_access()
        
        if not self.is_loaded:
            self.load_model()
        
        try:
            # Get embeddings
            question_emb = self._get_embeddings(question)
            context_emb = self._get_embeddings(context) if context else None
            
            # Calculate various quality metrics
            scores = {
                "clarity": self._score_clarity(question),
                "complexity": self._score_complexity(question),
                "context_relevance": 0.5,  # Default
                "semantic_coherence": self._score_semantic_coherence(question_emb),
                "overall_quality": 0.0
            }
            
            # Context relevance if available
            if context_emb is not None:
                scores["context_relevance"] = self._cosine_similarity(question_emb, context_emb)
            
            # Calculate overall quality
            weights = {
                "clarity": 0.3,
                "complexity": 0.2,
                "context_relevance": 0.3,
                "semantic_coherence": 0.2
            }
            
            scores["overall_quality"] = sum(
                scores[metric] * weight for metric, weight in weights.items()
            )
            
            return scores
            
        except Exception as e:
            self.log_error(f"Question quality scoring failed: {str(e)}")
            return {
                "clarity": 0.0,
                "complexity": 0.0,
                "context_relevance": 0.0,
                "semantic_coherence": 0.0,
                "overall_quality": 0.0,
                "error": str(e)
            }
    
    def evaluate_distractors(self, question: str, correct_answer: str, 
                           distractors: List[str], context: str = "") -> List[Dict[str, Any]]:
        """
        Evaluate the quality of MCQ distractors.
        
        Args:
            question: Question text
            correct_answer: Correct answer
            distractors: List of distractor options
            context: Optional context text
            
        Returns:
            List of distractor evaluations
        """
        self._check_tier_access()
        
        if not self.is_loaded:
            self.load_model()
        
        evaluations = []
        
        try:
            # Get embeddings
            question_emb = self._get_embeddings(question)
            correct_emb = self._get_embeddings(correct_answer)
            
            for i, distractor in enumerate(distractors):
                distractor_emb = self._get_embeddings(distractor)
                
                # Calculate similarities
                dist_question_sim = self._cosine_similarity(distractor_emb, question_emb)
                dist_correct_sim = self._cosine_similarity(distractor_emb, correct_emb)
                
                # Good distractors should be:
                # 1. Somewhat related to question (not too low similarity)
                # 2. Different from correct answer (not too high similarity)
                plausibility = dist_question_sim
                distinctiveness = 1.0 - dist_correct_sim
                
                # Overall distractor quality
                quality = (plausibility + distinctiveness) / 2.0
                
                evaluations.append({
                    "distractor": distractor,
                    "index": i,
                    "plausibility": plausibility,
                    "distinctiveness": distinctiveness,
                    "quality_score": quality,
                    "question_similarity": dist_question_sim,
                    "correct_similarity": dist_correct_sim
                })
            
            # Sort by quality
            evaluations.sort(key=lambda x: x["quality_score"], reverse=True)
            
            return evaluations
            
        except Exception as e:
            self.log_error(f"Distractor evaluation failed: {str(e)}")
            return [{"error": str(e)} for _ in distractors]
    
    def _get_embeddings(self, text: str) -> Optional[np.ndarray]:
        """
        Get BERT embeddings for text.
        
        Args:
            text: Input text
            
        Returns:
            Text embeddings
        """
        if not text.strip():
            return None
        
        try:
            # Tokenize
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                max_length=self.config.max_length,
                truncation=True,
                padding=True
            )
            
            # Move to device
            if self.config.device != "cpu":
                inputs = {k: v.to(self.config.device) for k, v in inputs.items()}
            
            # Get embeddings
            with torch.no_grad():
                outputs = self.bert_model(**inputs)
                # Use [CLS] token embedding
                embeddings = outputs.last_hidden_state[:, 0, :].cpu().numpy()
            
            return embeddings[0]  # Return first (and only) embedding
            
        except Exception as e:
            self.log_warning(f"Failed to get embeddings for text: {str(e)}")
            return None
    
    def _cosine_similarity(self, emb1: np.ndarray, emb2: np.ndarray) -> float:
        """Calculate cosine similarity between embeddings."""
        if emb1 is None or emb2 is None:
            return 0.0
        
        # Normalize embeddings
        emb1_norm = emb1 / np.linalg.norm(emb1)
        emb2_norm = emb2 / np.linalg.norm(emb2)
        
        # Calculate cosine similarity
        similarity = np.dot(emb1_norm, emb2_norm)
        
        # Ensure result is in [0, 1] range
        return max(0.0, min(1.0, (similarity + 1.0) / 2.0))
    
    def _calculate_validation_confidence(self, qa_similarity: float, 
                                       context_relevance: float,
                                       question: str, answer: str) -> float:
        """Calculate confidence for answer validation."""
        confidence = qa_similarity * 0.6 + context_relevance * 0.4
        
        # Adjust based on text lengths
        if len(question) < 10 or len(answer) < 2:
            confidence *= 0.7
        
        # Tier-based confidence boost
        if self.tier == ModelTier.ENTERPRISE:
            confidence = min(0.95, confidence * 1.1)
        
        return confidence
    
    def _generate_validation_explanation(self, is_valid: bool, 
                                       qa_similarity: float,
                                       context_relevance: float) -> str:
        """Generate explanation for validation result."""
        if is_valid:
            if qa_similarity > 0.7:
                return "Answer shows strong semantic relevance to the question."
            elif qa_similarity > 0.5:
                return "Answer shows moderate semantic relevance to the question."
            else:
                return "Answer shows basic semantic relevance to the question."
        else:
            if qa_similarity < 0.2:
                return "Answer shows very low semantic relevance to the question."
            else:
                return "Answer shows insufficient semantic relevance to the question."
    
    def _score_clarity(self, question: str) -> float:
        """Score question clarity based on linguistic features."""
        # Simple heuristics for clarity
        score = 0.5
        
        # Length factor
        word_count = len(question.split())
        if 5 <= word_count <= 20:
            score += 0.2
        elif word_count > 30:
            score -= 0.1
        
        # Question mark
        if question.strip().endswith('?'):
            score += 0.1
        
        # Complexity indicators
        if any(word in question.lower() for word in ['what', 'how', 'why', 'when', 'where']):
            score += 0.1
        
        return min(1.0, score)
    
    def _score_complexity(self, question: str) -> float:
        """Score question complexity."""
        # Simple complexity scoring
        word_count = len(question.split())
        
        if word_count < 5:
            return 0.2  # Too simple
        elif word_count < 10:
            return 0.6  # Moderate
        elif word_count < 20:
            return 0.8  # Good complexity
        else:
            return 0.4  # Potentially too complex
    
    def _score_semantic_coherence(self, embeddings: np.ndarray) -> float:
        """Score semantic coherence of question."""
        if embeddings is None:
            return 0.0
        
        # Simple coherence based on embedding magnitude
        magnitude = np.linalg.norm(embeddings)
        
        # Normalize to [0, 1] range (rough heuristic)
        return min(1.0, magnitude / 100.0)
