"""
Multilingual Question Generator for QuizAIGen

Integrates language detection, multilingual templates, and NLP models
to generate questions in multiple languages with proper localization.
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import random

from .base import Question
from ..core.language_detector import LanguageDetector, LanguageDetectionResult
from ..templates.multilingual_templates import MultilingualTemplates, QuestionType
from ..models.multilingual_nlp import MultilingualNLPManager
from ..utils.logger import LoggerMixin
from ..core.exceptions import ProcessingError, ValidationError


@dataclass
class MultilingualGenerationConfig:
    """Configuration for multilingual question generation."""
    auto_detect_language: bool = True
    target_language: Optional[str] = None
    fallback_language: str = 'en'
    min_confidence: float = 0.7
    preserve_entities: bool = True
    cultural_adaptation: bool = True
    
    def to_dict(self) -> Dict:
        """Convert to dictionary."""
        return {
            'auto_detect_language': self.auto_detect_language,
            'target_language': self.target_language,
            'fallback_language': self.fallback_language,
            'min_confidence': self.min_confidence,
            'preserve_entities': self.preserve_entities,
            'cultural_adaptation': self.cultural_adaptation
        }


class MultilingualQuestionGenerator(LoggerMixin):
    """
    Multilingual question generator that supports multiple languages.
    
    Features:
    - Automatic language detection
    - Language-specific question templates
    - Cultural adaptations
    - Entity preservation across languages
    - Fallback mechanisms for unsupported languages
    """
    
    def __init__(self, config: Optional[MultilingualGenerationConfig] = None):
        """
        Initialize multilingual question generator.

        Args:
            config: Configuration for multilingual generation
        """
        super().__init__()
        self.config = config or MultilingualGenerationConfig()
        
        # Initialize components
        self.language_detector = LanguageDetector(
            min_confidence=self.config.min_confidence
        )
        self.templates = MultilingualTemplates()
        self.nlp_manager = MultilingualNLPManager()
        
        self.log_info("Multilingual question generator initialized")

    def generate_questions(
        self, 
        text: str, 
        question_type: str = "mcq",
        num_questions: int = 5,
        target_language: Optional[str] = None,
        **kwargs
    ) -> List[Question]:
        """
        Generate questions in specified or detected language.
        
        Args:
            text: Source text for question generation
            question_type: Type of questions to generate
            num_questions: Number of questions to generate
            target_language: Target language (overrides config)
            **kwargs: Additional generation parameters
            
        Returns:
            List of generated questions
        """
        if not text or not text.strip():
            raise ValidationError("Text cannot be empty")
        
        # Determine target language
        if target_language:
            # Check if target language is supported by checking if templates exist
            templates_available = self.templates.get_templates(target_language, question_type)
            if not templates_available or (target_language not in ['en', 'es', 'fr', 'de', 'it', 'pt']):
                self.log_warning(f"Unsupported target language: {target_language}, falling back to auto-detection")
                detection_result = self.language_detector.detect_language(
                    text, self.config.fallback_language
                )
                language = detection_result.language
            else:
                language = target_language
                detection_result = None
        elif self.config.auto_detect_language:
            detection_result = self.language_detector.detect_language(
                text, self.config.fallback_language
            )
            language = detection_result.language
        else:
            language = self.config.target_language or self.config.fallback_language
            detection_result = None
        
        self.log_info(f"Generating {num_questions} {question_type} questions in {language}")
        
        # Process text with language-specific NLP
        processed_text = self._process_text_for_language(text, language)
        
        # Generate questions based on type
        if question_type == "mcq":
            questions = self._generate_mcq_questions(
                processed_text, language, num_questions, **kwargs
            )
        elif question_type == "boolean":
            questions = self._generate_boolean_questions(
                processed_text, language, num_questions, **kwargs
            )
        elif question_type == "faq":
            questions = self._generate_faq_questions(
                processed_text, language, num_questions, **kwargs
            )
        elif question_type == "fill_blank":
            questions = self._generate_fill_blank_questions(
                processed_text, language, num_questions, **kwargs
            )
        else:
            raise ValidationError(f"Unsupported question type: {question_type}")
        
        # Add language metadata
        for question in questions:
            question.metadata = question.metadata or {}
            question.metadata.update({
                'language': language,
                'detection_result': detection_result.to_dict() if detection_result else None,
                'generation_config': self.config.to_dict()
            })
        
        self.log_info(f"Generated {len(questions)} questions successfully")
        return questions
    
    def _process_text_for_language(self, text: str, language: str) -> Dict[str, Any]:
        """Process text using language-specific NLP models."""
        try:
            # Get sentences
            sentences = self.nlp_manager.get_sentences(text, language)
            
            # Extract entities
            entities = self.nlp_manager.extract_entities(text, language)
            
            # Get noun phrases
            noun_phrases = self.nlp_manager.get_noun_phrases(text, language)
            
            # Get POS tags
            pos_tags = self.nlp_manager.get_pos_tags(text, language)
            
            return {
                'original_text': text,
                'sentences': sentences,
                'entities': entities,
                'noun_phrases': noun_phrases,
                'pos_tags': pos_tags,
                'language': language
            }
            
        except Exception as e:
            self.log_warning(f"NLP processing failed for {language}: {str(e)}")
            # Fallback to basic processing
            return {
                'original_text': text,
                'sentences': [text],
                'entities': [],
                'noun_phrases': [],
                'pos_tags': [],
                'language': language
            }
    
    def _generate_mcq_questions(
        self, 
        processed_text: Dict[str, Any], 
        language: str, 
        num_questions: int,
        **kwargs
    ) -> List[Question]:
        """Generate multiple choice questions in specified language."""
        questions = []
        templates = self.templates.get_templates(language, QuestionType.MCQ.value)
        
        if not templates:
            self.log_warning(f"No MCQ templates available for language: {language}")
            return questions
        
        sentences = processed_text['sentences']
        entities = processed_text['entities']
        
        for i in range(min(num_questions, len(sentences))):
            sentence = sentences[i]
            template = random.choice(templates)
            
            # Extract key concepts from sentence
            concepts = self._extract_concepts_from_sentence(sentence, entities)
            
            if concepts:
                concept = random.choice(concepts)

                # Create template arguments based on placeholders
                template_args = {}
                for placeholder in template.placeholders:
                    if placeholder in ['concept', 'subject', 'term']:
                        template_args[placeholder] = concept
                    elif placeholder == 'statement':
                        template_args[placeholder] = f"{concept} is mentioned in the text"
                    else:
                        template_args[placeholder] = concept

                try:
                    question_text = template.format(**template_args)
                except KeyError as e:
                    self.log_warning(f"Template formatting failed: {e}")
                    continue
                
                # Generate answer options
                options = self._generate_mcq_options(concept, language)
                
                question = Question(
                    question=question_text,
                    type="mcq",
                    answer=options[0] if options else concept,
                    options=options,
                    explanation=f"Based on the text about {concept}",
                    difficulty="medium",
                    keywords=[concept],
                    source_text=sentence,
                    confidence=0.8,
                    metadata={'template_used': template.pattern}
                )
                questions.append(question)
        
        return questions
    
    def _generate_boolean_questions(
        self, 
        processed_text: Dict[str, Any], 
        language: str, 
        num_questions: int,
        **kwargs
    ) -> List[Question]:
        """Generate boolean questions in specified language."""
        questions = []
        templates = self.templates.get_templates(language, QuestionType.BOOLEAN.value)
        
        if not templates:
            self.log_warning(f"No Boolean templates available for language: {language}")
            return questions
        
        sentences = processed_text['sentences']
        
        for i in range(min(num_questions, len(sentences))):
            sentence = sentences[i]
            template = random.choice(templates)
            
            # Create statement from sentence
            statement = self._create_statement_from_sentence(sentence)
            
            if statement:
                question_text = template.format(statement=statement)
                
                question = Question(
                    question=question_text,
                    type="boolean",
                    answer="True",  # Assuming statement is true based on text
                    options=["True", "False"],
                    explanation=f"According to the text: {sentence}",
                    difficulty="easy",
                    keywords=self._extract_keywords_from_sentence(sentence),
                    source_text=sentence,
                    confidence=0.7,
                    metadata={'template_used': template.pattern}
                )
                questions.append(question)
        
        return questions
    
    def _generate_faq_questions(
        self, 
        processed_text: Dict[str, Any], 
        language: str, 
        num_questions: int,
        **kwargs
    ) -> List[Question]:
        """Generate FAQ questions in specified language."""
        questions = []
        templates = self.templates.get_templates(language, QuestionType.FAQ.value)
        
        if not templates:
            self.log_warning(f"No FAQ templates available for language: {language}")
            return questions
        
        sentences = processed_text['sentences']
        entities = processed_text['entities']
        
        for i in range(min(num_questions, len(sentences))):
            sentence = sentences[i]
            template = random.choice(templates)
            
            # Find suitable entities for question
            sentence_entities = [
                ent for ent in entities 
                if ent[2] >= 0 and ent[3] <= len(sentence)  # Entity within sentence bounds
            ]
            
            if sentence_entities:
                entity = random.choice(sentence_entities)
                entity_text = entity[0]
                
                # Create template arguments based on placeholders
                template_args = {}
                for placeholder in template.placeholders:
                    template_args[placeholder] = entity_text

                try:
                    question_text = template.format(**template_args)
                except KeyError as e:
                    self.log_warning(f"FAQ template formatting failed: {e}")
                    continue
                
                question = Question(
                    question=question_text,
                    type="faq",
                    answer=sentence,
                    options=[],
                    explanation=f"Information about {entity_text}",
                    difficulty="medium",
                    keywords=[entity_text],
                    source_text=sentence,
                    confidence=0.8,
                    metadata={'template_used': template.pattern, 'entity_used': entity}
                )
                questions.append(question)
        
        return questions
    
    def _generate_fill_blank_questions(
        self, 
        processed_text: Dict[str, Any], 
        language: str, 
        num_questions: int,
        **kwargs
    ) -> List[Question]:
        """Generate fill-in-the-blank questions in specified language."""
        questions = []
        templates = self.templates.get_templates(language, QuestionType.FILL_BLANK.value)
        
        if not templates:
            self.log_warning(f"No Fill-blank templates available for language: {language}")
            return questions
        
        sentences = processed_text['sentences']
        
        for i in range(min(num_questions, len(sentences))):
            sentence = sentences[i]
            
            # Find important words to blank out
            important_words = self._find_important_words(sentence, processed_text['pos_tags'])
            
            if important_words:
                word_to_blank = random.choice(important_words)
                blank_sentence = sentence.replace(word_to_blank, "_____", 1)
                
                question = Question(
                    question=f"Fill in the blank: {blank_sentence}",
                    type="fill_blank",
                    answer=word_to_blank,
                    options=[],
                    explanation=f"The missing word is '{word_to_blank}'",
                    difficulty="medium",
                    keywords=[word_to_blank],
                    source_text=sentence,
                    confidence=0.7,
                    metadata={'blanked_word': word_to_blank}
                )
                questions.append(question)
        
        return questions
    
    def _extract_concepts_from_sentence(self, sentence: str, entities: List[Tuple]) -> List[str]:
        """Extract key concepts from a sentence."""
        concepts = []
        
        # Add entities as concepts
        for entity in entities:
            if entity[0] and len(entity[0]) > 2:  # Filter out very short entities
                concepts.append(entity[0])
        
        # Add some basic noun extraction as fallback
        words = sentence.split()
        for word in words:
            if word.istitle() and len(word) > 3:  # Capitalized words (likely nouns)
                concepts.append(word)
        
        return list(set(concepts))  # Remove duplicates
    
    def _generate_mcq_options(self, correct_answer: str, language: str) -> List[str]:
        """Generate multiple choice options for a given answer."""
        options = [correct_answer]
        
        # Add some generic distractors based on language
        distractors = {
            'en': ['Alternative option', 'Different choice', 'Other possibility'],
            'es': ['Opción alternativa', 'Elección diferente', 'Otra posibilidad'],
            'fr': ['Option alternative', 'Choix différent', 'Autre possibilité'],
            'de': ['Alternative Option', 'Andere Wahl', 'Andere Möglichkeit']
        }
        
        lang_distractors = distractors.get(language, distractors['en'])
        options.extend(lang_distractors[:3])  # Add up to 3 distractors
        
        random.shuffle(options)
        return options
    
    def _create_statement_from_sentence(self, sentence: str) -> str:
        """Create a statement from a sentence for boolean questions."""
        # Remove question marks and make it a statement
        statement = sentence.replace('?', '').strip()
        
        # Ensure it doesn't start with question words
        question_starters = ['what', 'how', 'why', 'when', 'where', 'who', 'which']
        words = statement.lower().split()
        
        if words and words[0] in question_starters:
            # Try to rephrase as statement
            if words[0] == 'what':
                statement = statement[5:].strip()  # Remove "what "
            elif words[0] == 'how':
                statement = statement[4:].strip()   # Remove "how "
        
        return statement
    
    def _extract_keywords_from_sentence(self, sentence: str) -> List[str]:
        """Extract keywords from a sentence."""
        # Simple keyword extraction
        words = sentence.split()
        keywords = []
        
        for word in words:
            # Filter out common words and keep meaningful ones
            if (len(word) > 3 and 
                word.lower() not in ['this', 'that', 'with', 'from', 'they', 'have', 'been']):
                keywords.append(word.strip('.,!?;:'))
        
        return keywords[:5]  # Return top 5 keywords
    
    def _find_important_words(self, sentence: str, pos_tags: List[Tuple]) -> List[str]:
        """Find important words in a sentence for fill-in-the-blank questions."""
        important_words = []
        
        # Look for nouns, verbs, and adjectives in POS tags
        important_pos = ['NOUN', 'VERB', 'ADJ', 'PROPN']
        
        for word, pos in pos_tags:
            if pos in important_pos and len(word) > 3:
                important_words.append(word)
        
        # Fallback: find capitalized words and longer words
        if not important_words:
            words = sentence.split()
            for word in words:
                clean_word = word.strip('.,!?;:')
                if (clean_word.istitle() or len(clean_word) > 5) and clean_word.isalpha():
                    important_words.append(clean_word)
        
        return important_words
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return self.language_detector.get_supported_languages()
    
    def is_language_supported(self, language: str) -> bool:
        """Check if a language is supported."""
        return self.language_detector.is_language_supported(language)
    
    def detect_text_language(self, text: str) -> LanguageDetectionResult:
        """Detect the language of given text."""
        return self.language_detector.detect_language(text)
