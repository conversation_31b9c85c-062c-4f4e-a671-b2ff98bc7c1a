"""
Integration tests for Multi-Language Support system

Tests the complete multilingual workflow from text input to question generation
across multiple languages with real-world scenarios.
"""

import pytest
import unittest
from typing import List

from quizaigen.generators.multilingual_generator import (
    MultilingualQuestionGenerator, 
    MultilingualGenerationConfig
)
from quizaigen.generators.base import Question


class TestMultilingualIntegration(unittest.TestCase):
    """Integration tests for multilingual question generation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = MultilingualGenerationConfig(
            auto_detect_language=True,
            min_confidence=0.6,  # Lower threshold for testing
            preserve_entities=True,
            cultural_adaptation=True
        )
        self.generator = MultilingualQuestionGenerator(self.config)
    
    def test_english_text_processing(self):
        """Test complete workflow with English text."""
        text = """
        Python is a high-level, interpreted programming language with dynamic semantics.
        Its high-level built-in data structures, combined with dynamic typing and dynamic binding,
        make it very attractive for Rapid Application Development, as well as for use as a
        scripting or glue language to connect existing components together.
        """
        
        # Test MCQ generation
        mcq_questions = self.generator.generate_questions(text, "mcq", 2)
        self.assertEqual(len(mcq_questions), 2)
        
        for question in mcq_questions:
            self.assertEqual(question.type, "mcq")
            self.assertIsInstance(question.question, str)
            self.assertIsInstance(question.options, list)
            self.assertEqual(len(question.options), 4)
            self.assertIn(question.answer, question.options)
            self.assertIn('language', question.metadata)
            self.assertEqual(question.metadata['language'], 'en')
        
        # Test Boolean generation
        boolean_questions = self.generator.generate_questions(text, "boolean", 2)
        self.assertEqual(len(boolean_questions), 2)
        
        for question in boolean_questions:
            self.assertEqual(question.type, "boolean")
            self.assertIn(question.answer, ["True", "False"])
            self.assertEqual(question.metadata['language'], 'en')
    
    def test_spanish_text_processing(self):
        """Test complete workflow with Spanish text."""
        text = """
        Python es un lenguaje de programación interpretado cuya filosofía hace hincapié
        en la legibilidad de su código. Se trata de un lenguaje de programación
        multiparadigma, ya que soporta orientación a objetos, programación imperativa
        y, en menor medida, programación funcional.
        """
        
        # Test with auto-detection
        questions = self.generator.generate_questions(text, "mcq", 1)
        self.assertEqual(len(questions), 1)
        self.assertEqual(questions[0].metadata['language'], 'es')
        
        # Test with explicit language specification
        questions_explicit = self.generator.generate_questions(
            text, "boolean", 1, target_language='es'
        )
        self.assertEqual(len(questions_explicit), 1)
        self.assertEqual(questions_explicit[0].metadata['language'], 'es')
        
        # Verify Spanish question patterns
        question_text = questions_explicit[0].question.lower()
        # Should contain Spanish question indicators
        spanish_indicators = ['¿', '?', 'verdadero', 'falso', 'cierto']
        has_spanish_indicator = any(indicator in question_text for indicator in spanish_indicators)
        self.assertTrue(has_spanish_indicator, f"Question should contain Spanish indicators: {question_text}")
    
    def test_french_text_processing(self):
        """Test complete workflow with French text."""
        text = """
        Python est un langage de programmation interprété, multi-paradigme et
        multiplateformes. Il favorise la programmation impérative structurée,
        fonctionnelle et orientée objet. Il est doté d'un typage dynamique fort,
        d'une gestion automatique de la mémoire par ramasse-miettes et d'un système
        de gestion d'exception.
        """
        
        questions = self.generator.generate_questions(text, "faq", 1, target_language='fr')
        self.assertEqual(len(questions), 1)
        self.assertEqual(questions[0].type, "faq")
        self.assertEqual(questions[0].metadata['language'], 'fr')
        
        # Verify French question patterns
        question_text = questions[0].question.lower()
        french_indicators = ['qu\'est-ce que', 'comment', 'pourquoi', 'que', 'quoi']
        has_french_indicator = any(indicator in question_text for indicator in french_indicators)
        self.assertTrue(has_french_indicator or '?' in question_text, 
                       f"Question should contain French indicators: {question_text}")
    
    def test_german_text_processing(self):
        """Test complete workflow with German text."""
        text = """
        Python ist eine universelle, üblicherweise interpretierte, höhere Programmiersprache.
        Sie hat den Anspruch, einen gut lesbaren, knappen Programmierstil zu fördern.
        So werden beispielsweise Blöcke nicht durch geschweifte Klammern, sondern durch
        Einrückungen strukturiert.
        """
        
        questions = self.generator.generate_questions(text, "fill_blank", 1, target_language='de')
        self.assertEqual(len(questions), 1)
        self.assertEqual(questions[0].type, "fill_blank")
        self.assertEqual(questions[0].metadata['language'], 'de')
        
        # Verify fill-in-the-blank format
        self.assertIn("_____", questions[0].question)
        self.assertIsInstance(questions[0].answer, str)
        self.assertGreater(len(questions[0].answer), 0)
    
    def test_mixed_language_batch_processing(self):
        """Test processing multiple texts in different languages."""
        texts_and_languages = [
            ("Python is a programming language.", "en"),
            ("Python es un lenguaje de programación.", "es"),
            ("Python est un langage de programmation.", "fr"),
            ("Python ist eine Programmiersprache.", "de")
        ]
        
        all_questions = []
        for text, expected_lang in texts_and_languages:
            questions = self.generator.generate_questions(text, "mcq", 1)
            all_questions.extend(questions)
            
            # Verify language detection
            self.assertEqual(len(questions), 1)
            detected_lang = questions[0].metadata['language']
            self.assertEqual(detected_lang, expected_lang, 
                           f"Expected {expected_lang}, got {detected_lang} for text: {text}")
        
        # Verify we have questions in all languages
        languages_found = set(q.metadata['language'] for q in all_questions)
        expected_languages = {'en', 'es', 'fr', 'de'}
        self.assertEqual(languages_found, expected_languages)
    
    def test_language_fallback_mechanism(self):
        """Test fallback to English when target language is unsupported."""
        text = "Python is a programming language."
        
        # Test with unsupported language - should fallback to detected language (English)
        questions = self.generator.generate_questions(text, "mcq", 1, target_language='xyz')
        self.assertEqual(len(questions), 1)
        # Should fallback to detected language (English)
        self.assertEqual(questions[0].metadata['language'], 'en')
    
    def test_confidence_threshold_filtering(self):
        """Test that low-confidence language detection is handled properly."""
        # Very short text that might have low confidence
        text = "Hi."
        
        questions = self.generator.generate_questions(text, "boolean", 1)
        # Should still generate questions even with low confidence
        self.assertGreaterEqual(len(questions), 0)
        
        if questions:  # If questions were generated
            self.assertIn('language', questions[0].metadata)
            self.assertIn('detection_result', questions[0].metadata)
            self.assertIn('confidence', questions[0].metadata['detection_result'])
    
    def test_entity_preservation_across_languages(self):
        """Test that named entities are preserved across different languages."""
        texts = [
            "Microsoft developed Visual Studio Code.",
            "Microsoft desarrolló Visual Studio Code.",
            "Microsoft a développé Visual Studio Code.",
            "Microsoft entwickelte Visual Studio Code."
        ]
        
        for text in texts:
            questions = self.generator.generate_questions(text, "mcq", 1)
            self.assertEqual(len(questions), 1)
            
            question = questions[0]
            # Check that entities like "Microsoft" and "Visual Studio Code" are preserved
            question_content = question.question + " " + " ".join(question.options or [])
            self.assertTrue(
                "Microsoft" in question_content or "Visual Studio Code" in question_content,
                f"Entities should be preserved in: {question_content}"
            )
    
    def test_cultural_adaptation(self):
        """Test that cultural adaptations are applied correctly."""
        # Test with content that might have cultural elements
        text_en = "The price is $100.50 and the date is 12/25/2023."
        text_fr = "Le prix est de 100,50€ et la date est le 25/12/2023."
        
        questions_en = self.generator.generate_questions(text_en, "mcq", 1, target_language='en')
        questions_fr = self.generator.generate_questions(text_fr, "mcq", 1, target_language='fr')
        
        self.assertEqual(len(questions_en), 1)
        self.assertEqual(len(questions_fr), 1)
        
        # Both should generate valid questions with appropriate cultural context
        self.assertEqual(questions_en[0].metadata['language'], 'en')
        self.assertEqual(questions_fr[0].metadata['language'], 'fr')
    
    def test_question_quality_metrics(self):
        """Test that generated questions meet quality standards."""
        text = """
        Artificial Intelligence (AI) is intelligence demonstrated by machines,
        in contrast to the natural intelligence displayed by humans and animals.
        Leading AI textbooks define the field as the study of "intelligent agents":
        any device that perceives its environment and takes actions that maximize
        its chance of successfully achieving its goals.
        """
        
        questions = self.generator.generate_questions(text, "mcq", 3)
        self.assertEqual(len(questions), 3)
        
        for question in questions:
            # Check basic quality metrics
            self.assertGreater(len(question.question), 10, "Question should be substantial")
            self.assertLess(len(question.question), 200, "Question should not be too long")
            
            if question.options:
                self.assertEqual(len(question.options), 4, "MCQ should have 4 options")
                self.assertIn(question.answer, question.options, "Answer should be in options")
            
            if question.confidence:
                self.assertGreaterEqual(question.confidence, 0.0, "Confidence should be non-negative")
                self.assertLessEqual(question.confidence, 1.0, "Confidence should not exceed 1.0")
            
            # Check metadata completeness
            self.assertIn('language', question.metadata)
            self.assertIn('detection_result', question.metadata)
            self.assertIn('template_used', question.metadata)


if __name__ == '__main__':
    unittest.main()
