"""
Test suite for Enhanced Document Processor

This module contains comprehensive tests for the enhanced document processing capabilities
including PDF processing, Word document processing, OCR, and quality analysis.
"""

import unittest
from unittest.mock import Mock, patch, mock_open
import tempfile
import os
from pathlib import Path
import sys

# Add the parent directory to the path to import quizaigen
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from quizaigen.inputs.document_processor import DocumentProcessor
from quizaigen.core.exceptions import ProcessingError, ValidationError


class TestEnhancedDocumentProcessor(unittest.TestCase):
    """Test cases for Enhanced Document Processor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = DocumentProcessor()
        
        # Sample text content for testing
        self.sample_text = """
        This is a sample document for testing purposes.
        It contains multiple paragraphs and sentences.
        
        The document processor should be able to extract this text
        and perform quality analysis on it.
        
        This text has sufficient length for readability analysis
        and contains various sentence structures for testing.
        """
        
        # Create temporary test files
        self.temp_dir = tempfile.mkdtemp()
        self.test_txt_file = os.path.join(self.temp_dir, "test.txt")
        with open(self.test_txt_file, 'w', encoding='utf-8') as f:
            f.write(self.sample_text)
    
    def tearDown(self):
        """Clean up test fixtures."""
        # Clean up temporary files
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """Test DocumentProcessor initialization."""
        processor = DocumentProcessor()
        
        self.assertIsInstance(processor, DocumentProcessor)
        self.assertIsInstance(processor.supported_extensions, set)
        self.assertIsInstance(processor.capabilities, dict)
        self.assertGreater(processor.max_file_size, 0)
    
    def test_initialization_with_config(self):
        """Test DocumentProcessor initialization with custom config."""
        config = {
            'max_file_size_mb': 100,
            'enable_ocr': False,
            'ocr_language': 'fra',
            'analyze_readability': False
        }
        
        processor = DocumentProcessor(config)
        
        self.assertEqual(processor.max_file_size, 100 * 1024 * 1024)
        self.assertEqual(processor.enable_ocr, False)
        self.assertEqual(processor.ocr_language, 'fra')
        self.assertEqual(processor.analyze_readability, False)
    
    def test_validate_file_success(self):
        """Test successful file validation."""
        result = self.processor.validate_file(self.test_txt_file)
        # Note: txt files might not be in supported_extensions, so this might fail
        # Let's test with a mock PDF file

        with patch.object(Path, 'exists', return_value=True), \
             patch.object(Path, 'is_file', return_value=True), \
             patch.object(Path, 'suffix', '.pdf'), \
             patch.object(Path, 'stat') as mock_stat, \
             patch('os.access', return_value=True):

            mock_stat.return_value.st_size = 1024  # 1KB file
            result = self.processor.validate_file("test.pdf")

            if '.pdf' in self.processor.supported_extensions:
                self.assertTrue(result)
    
    def test_validate_file_not_exists(self):
        """Test file validation with non-existent file."""
        result = self.processor.validate_file("nonexistent.pdf")
        self.assertFalse(result)
    
    def test_validate_file_too_large(self):
        """Test file validation with oversized file."""
        with patch.object(Path, 'exists', return_value=True), \
             patch.object(Path, 'is_file', return_value=True), \
             patch.object(Path, 'suffix', '.pdf'), \
             patch.object(Path, 'stat') as mock_stat, \
             patch('os.access', return_value=True):

            mock_stat.return_value.st_size = self.processor.max_file_size + 1
            result = self.processor.validate_file("large.pdf")
            self.assertFalse(result)

    def test_validate_file_unsupported_format(self):
        """Test file validation with unsupported format."""
        with patch.object(Path, 'exists', return_value=True), \
             patch.object(Path, 'is_file', return_value=True), \
             patch.object(Path, 'suffix', '.xyz'), \
             patch.object(Path, 'stat') as mock_stat, \
             patch('os.access', return_value=True):

            mock_stat.return_value.st_size = 1024
            result = self.processor.validate_file("test.xyz")
            self.assertFalse(result)
    
    def test_get_supported_formats(self):
        """Test getting supported file formats."""
        formats = self.processor.get_supported_formats()
        
        self.assertIsInstance(formats, list)
        self.assertGreater(len(formats), 0)
        
        # Check that all formats start with '.'
        for fmt in formats:
            self.assertTrue(fmt.startswith('.'))
    
    def test_get_processing_capabilities(self):
        """Test getting processing capabilities information."""
        capabilities = self.processor.get_processing_capabilities()
        
        self.assertIsInstance(capabilities, dict)
        self.assertIn('supported_formats', capabilities)
        self.assertIn('capabilities', capabilities)
        self.assertIn('max_file_size_mb', capabilities)
        self.assertIn('advanced_features', capabilities)
        
        # Check advanced features
        advanced = capabilities['advanced_features']
        self.assertIn('table_extraction', advanced)
        self.assertIn('structure_analysis', advanced)
        self.assertIn('quality_assessment', advanced)
        self.assertIn('batch_processing', advanced)
    
    def test_calculate_file_hash(self):
        """Test file hash calculation."""
        hash_value = self.processor._calculate_file_hash(Path(self.test_txt_file))
        
        self.assertIsInstance(hash_value, str)
        self.assertEqual(len(hash_value), 64)  # SHA-256 hash length
        self.assertNotEqual(hash_value, "unknown")
    
    def test_clean_extracted_text(self):
        """Test text cleaning functionality."""
        dirty_text = "  This   has    excessive   spaces  \n\n\n\n  And multiple newlines  \n\n\n  "
        cleaned = self.processor._clean_extracted_text(dirty_text)
        
        self.assertNotIn("   ", cleaned)  # No triple spaces
        self.assertNotIn("\n\n\n", cleaned)  # No triple newlines
        self.assertEqual(cleaned.strip(), cleaned)  # No leading/trailing whitespace
    
    def test_analyze_text_quality(self):
        """Test text quality analysis."""
        quality_metrics = self.processor._analyze_text_quality(self.sample_text)
        
        self.assertIsInstance(quality_metrics, dict)
        self.assertIn('overall_score', quality_metrics)
        self.assertIn('character_count', quality_metrics)
        self.assertIn('word_count', quality_metrics)
        self.assertIn('sentence_count', quality_metrics)
        self.assertIn('issues', quality_metrics)
        
        # Quality score should be between 0 and 1
        self.assertGreaterEqual(quality_metrics['overall_score'], 0.0)
        self.assertLessEqual(quality_metrics['overall_score'], 1.0)
    
    def test_analyze_text_quality_empty_text(self):
        """Test text quality analysis with empty text."""
        quality_metrics = self.processor._analyze_text_quality("")
        
        self.assertEqual(quality_metrics['overall_score'], 0.0)
        self.assertIn('empty_text', quality_metrics['issues'])
    
    def test_analyze_content_structure(self):
        """Test content structure analysis."""
        structure = self.processor._analyze_content_structure(self.sample_text)
        
        self.assertIsInstance(structure, dict)
        self.assertIn('sentence_count', structure)
        self.assertIn('paragraph_count', structure)
        self.assertIn('avg_sentences_per_paragraph', structure)
        self.assertIn('short_sentences', structure)
        self.assertIn('long_sentences', structure)
        
        self.assertGreater(structure['sentence_count'], 0)
        self.assertGreater(structure['paragraph_count'], 0)
    
    def test_determine_reading_level(self):
        """Test reading level determination."""
        # Test various Flesch scores
        self.assertEqual(self.processor._determine_reading_level(95), 'very_easy')
        self.assertEqual(self.processor._determine_reading_level(85), 'easy')
        self.assertEqual(self.processor._determine_reading_level(75), 'fairly_easy')
        self.assertEqual(self.processor._determine_reading_level(65), 'standard')
        self.assertEqual(self.processor._determine_reading_level(55), 'fairly_difficult')
        self.assertEqual(self.processor._determine_reading_level(35), 'difficult')
        self.assertEqual(self.processor._determine_reading_level(25), 'very_difficult')
    
    def test_batch_process_empty_list(self):
        """Test batch processing with empty file list."""
        results = self.processor.batch_process([])
        self.assertEqual(results, [])
    
    def test_get_document_info_nonexistent(self):
        """Test getting document info for non-existent file."""
        info = self.processor.get_document_info("nonexistent.pdf")
        self.assertIn('error', info)
        self.assertEqual(info['error'], 'File not found')

    @patch('quizaigen.inputs.document_processor.PDF_AVAILABLE', True)
    def test_extract_with_pypdf2(self):
        """Test PDF text extraction with PyPDF2."""
        with patch('builtins.open', mock_open(read_data=b'fake pdf data')), \
             patch('PyPDF2.PdfReader') as mock_reader_class:

            # Mock PyPDF2 components
            mock_page = Mock()
            mock_page.extract_text.return_value = "Sample PDF text content"

            mock_reader = Mock()
            mock_reader.pages = [mock_page]

            mock_reader_class.return_value = mock_reader

            # Test extraction
            result = self.processor._extract_with_pypdf2(Path("test.pdf"))

            self.assertIn('text', result)
            self.assertIn('metadata', result)
            self.assertEqual(len(result['text']), 1)
            self.assertEqual(result['text'][0], "Sample PDF text content")
            self.assertEqual(result['metadata']['pages'], 1)

    @patch('quizaigen.inputs.document_processor.PDFPLUMBER_AVAILABLE', True)
    def test_extract_with_pdfplumber(self):
        """Test PDF text extraction with pdfplumber."""
        with patch('pdfplumber.open') as mock_open:
            # Mock pdfplumber components
            mock_page = Mock()
            mock_page.extract_text.return_value = "Sample pdfplumber text"
            mock_page.extract_tables.return_value = [
                [['Header1', 'Header2'], ['Row1Col1', 'Row1Col2']]
            ]

            mock_pdf = Mock()
            mock_pdf.pages = [mock_page]
            mock_pdf.__enter__ = Mock(return_value=mock_pdf)
            mock_pdf.__exit__ = Mock(return_value=None)

            mock_open.return_value = mock_pdf

            # Test extraction
            result = self.processor._extract_with_pdfplumber(Path("test.pdf"), extract_tables=True)

            self.assertIn('text', result)
            self.assertIn('tables', result)
            self.assertIn('metadata', result)
            self.assertEqual(len(result['text']), 1)
            self.assertEqual(result['text'][0], "Sample pdfplumber text")
            self.assertEqual(len(result['tables']), 1)

    @patch('quizaigen.inputs.document_processor.DOCX_AVAILABLE', True)
    def test_process_word_enhanced(self):
        """Test enhanced Word document processing."""
        with patch('docx.Document') as mock_document, \
             patch.object(Path, 'stat') as mock_stat:

            # Mock file stats
            mock_stat.return_value.st_size = 1024

            # Mock Word document components
            mock_para1 = Mock()
            mock_para1.text = "First paragraph text"
            mock_para1.style.name = "Normal"

            mock_para2 = Mock()
            mock_para2.text = "Second paragraph text"
            mock_para2.style.name = "Heading 1"

            mock_cell1 = Mock()
            mock_cell1.text = "Cell 1"
            mock_cell2 = Mock()
            mock_cell2.text = "Cell 2"

            mock_row = Mock()
            mock_row.cells = [mock_cell1, mock_cell2]

            mock_table = Mock()
            mock_table.rows = [mock_row]

            mock_doc = Mock()
            mock_doc.paragraphs = [mock_para1, mock_para2]
            mock_doc.tables = [mock_table]

            mock_document.return_value = mock_doc

            # Test processing
            result = self.processor._process_word_enhanced(
                Path("test.docx"),
                extract_tables=True,
                analyze_structure=True
            )

            self.assertTrue(result['success'])
            self.assertIn('text', result)
            self.assertIn('tables', result)
            self.assertIn('structure', result)
            self.assertIn('metadata', result)

            # Check structure analysis
            structure = result['structure']
            self.assertEqual(structure['paragraphs'], 2)
            self.assertEqual(len(structure['headings']), 1)
            self.assertEqual(structure['headings'][0]['level'], 'Heading 1')

    def test_enhance_processing_result(self):
        """Test processing result enhancement."""
        base_result = {
            'text': self.sample_text,
            'success': True,
            'metadata': {}
        }

        enhanced = self.processor._enhance_processing_result(base_result, quality_threshold=0.5)

        self.assertIn('quality_metrics', enhanced)
        self.assertIn('content_analysis', enhanced)
        self.assertIn('quality_passed', enhanced)

        # Quality should pass for good sample text
        self.assertTrue(enhanced['quality_passed'])

    def test_enhance_processing_result_failed(self):
        """Test processing result enhancement with failed processing."""
        base_result = {
            'text': '',
            'success': False,
            'metadata': {}
        }

        enhanced = self.processor._enhance_processing_result(base_result, quality_threshold=0.5)

        # Should return unchanged for failed processing
        self.assertEqual(enhanced, base_result)

    @patch('quizaigen.inputs.document_processor.OCR_AVAILABLE', True)
    def test_extract_with_ocr(self):
        """Test OCR text extraction."""
        with patch('pdf2image.convert_from_path') as mock_convert, \
             patch('pytesseract.image_to_string') as mock_ocr:

            # Mock image conversion and OCR
            mock_image = Mock()
            mock_convert.return_value = [mock_image]
            mock_ocr.return_value = "OCR extracted text"

            # Test OCR extraction
            result = self.processor._extract_with_ocr(Path("test.pdf"))

            self.assertIn('text', result)
            self.assertIn('metadata', result)
            self.assertEqual(len(result['text']), 1)
            self.assertEqual(result['text'][0], "OCR extracted text")
            self.assertEqual(result['metadata']['pages'], 1)
            self.assertEqual(result['metadata']['ocr_method'], 'pytesseract')


if __name__ == '__main__':
    unittest.main()
