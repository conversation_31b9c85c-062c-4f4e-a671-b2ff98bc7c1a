# QuizAIGen API Documentation

## Overview

This document provides detailed API specifications for integrating QuizAIGen into your applications. It covers REST API endpoints, request/response formats, authentication, and error handling.

## Base URL

```
Production: https://api.quizaigen.com/v1
Staging: https://staging-api.quizaigen.com/v1
Development: http://localhost:8000/api/v1
```

## Authentication

### API Key Authentication

```http
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

### License Validation

```http
X-License-Key: YOUR_LICENSE_KEY
X-License-Tier: premium
```

---

## Endpoints

### 1. Generate Questions

Generate questions from text input.

**Endpoint:** `POST /generate`

**Request Body:**
```json
{
    "text": "Python is a high-level programming language...",
    "type": "mcq",
    "num_questions": 5,
    "difficulty": "medium",
    "options": {
        "include_explanation": true,
        "min_confidence": 0.7,
        "keywords": ["python", "programming"]
    }
}
```

**Parameters:**

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `text` | string | Yes | - | Input text for question generation |
| `type` | string | No | "mcq" | Question type: mcq, boolean, faq, fill_blank, paraphrased, qa |
| `num_questions` | integer | No | 5 | Number of questions to generate (max 10 for free, 100 for premium) |
| `difficulty` | string | No | "medium" | Difficulty level: easy, medium, hard |
| `options.include_explanation` | boolean | No | true | Include explanations in responses |
| `options.min_confidence` | float | No | 0.5 | Minimum confidence score (0.0-1.0) |
| `options.keywords` | array | No | [] | Keywords to focus on |

**Response:**
```json
{
    "success": true,
    "data": {
        "questions": [
            {
                "id": "q_001",
                "question": "What is Python primarily used for?",
                "type": "mcq",
                "answer": 0,
                "options": [
                    "Programming and software development",
                    "Web design only",
                    "Hardware manufacturing",
                    "Database management only"
                ],
                "explanation": "Python is a versatile programming language used for various applications including web development, data science, AI, and more.",
                "difficulty": "medium",
                "confidence": 0.92,
                "keywords": ["python", "programming", "software"],
                "metadata": {
                    "created_at": "2025-06-27T20:31:02Z",
                    "processing_time_ms": 1250,
                    "model_version": "t5-base-v1.2"
                }
            }
        ],
        "total_questions": 1,
        "processing_time_ms": 1250,
        "tier_used": "free"
    },
    "metadata": {
        "request_id": "req_abc123",
        "timestamp": "2025-06-27T20:31:02Z",
        "api_version": "1.0.0"
    }
}
```

### 2. Generate from Document

Generate questions from uploaded documents (Premium feature).

**Endpoint:** `POST /generate/document`

**Request:** Multipart form data
```
Content-Type: multipart/form-data

file: [PDF/Word document]
type: mcq
num_questions: 10
extract_pages: 1-5
```

**Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file` | file | Yes | PDF or Word document |
| `type` | string | No | Question type |
| `num_questions` | integer | No | Number of questions |
| `extract_pages` | string | No | Page range (e.g., "1-5", "all") |

**Response:** Same format as `/generate` endpoint

### 3. Generate from URL

Generate questions from web content (Premium feature).

**Endpoint:** `POST /generate/url`

**Request Body:**
```json
{
    "url": "https://example.com/article",
    "type": "mixed",
    "num_questions": 8,
    "extract_options": {
        "include_images": false,
        "max_content_length": 10000,
        "exclude_navigation": true
    }
}
```

### 4. Export Questions

Export questions to various formats.

**Endpoint:** `POST /export`

**Request Body:**
```json
{
    "questions": [
        {
            "question": "What is Python?",
            "type": "mcq",
            "answer": 0,
            "options": ["Programming language", "Snake", "Tool", "Framework"]
        }
    ],
    "format": "qti",
    "options": {
        "include_metadata": true,
        "quiz_title": "Python Basics Quiz",
        "instructions": "Choose the best answer for each question."
    }
}
```

**Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `questions` | array | Yes | Array of question objects |
| `format` | string | Yes | Export format: json, csv, xml, qti, moodle, aiken, respondus, gift |
| `options.include_metadata` | boolean | No | Include metadata in export |
| `options.quiz_title` | string | No | Title for the quiz |
| `options.instructions` | string | No | Instructions for quiz takers |

**Response:**
```json
{
    "success": true,
    "data": {
        "content": "<?xml version=\"1.0\"?>...",
        "format": "qti",
        "size_bytes": 2323,
        "download_url": "https://api.quizaigen.com/downloads/quiz_abc123.qti",
        "expires_at": "2025-06-28T20:31:02Z"
    },
    "metadata": {
        "request_id": "req_def456",
        "timestamp": "2025-06-27T20:31:02Z"
    }
}
```

### 5. Batch Processing

Process multiple requests in a single API call (Premium feature).

**Endpoint:** `POST /batch`

**Request Body:**
```json
{
    "requests": [
        {
            "id": "batch_001",
            "type": "generate",
            "data": {
                "text": "First text content...",
                "type": "mcq",
                "num_questions": 3
            }
        },
        {
            "id": "batch_002",
            "type": "generate",
            "data": {
                "text": "Second text content...",
                "type": "boolean",
                "num_questions": 2
            }
        }
    ]
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "results": [
            {
                "id": "batch_001",
                "success": true,
                "data": {
                    "questions": [...],
                    "total_questions": 3
                }
            },
            {
                "id": "batch_002",
                "success": true,
                "data": {
                    "questions": [...],
                    "total_questions": 2
                }
            }
        ],
        "total_requests": 2,
        "successful_requests": 2,
        "failed_requests": 0,
        "processing_time_ms": 3500
    }
}
```

### 6. License Information

Get current license information and feature availability.

**Endpoint:** `GET /license`

**Response:**
```json
{
    "success": true,
    "data": {
        "tier": "premium",
        "status": "active",
        "expires_at": "2025-12-31T23:59:59Z",
        "features": {
            "question_types": ["mcq", "boolean", "faq", "fill_blank", "paraphrased", "qa"],
            "input_types": ["text", "pdf", "word", "url"],
            "export_formats": ["json", "csv", "xml", "qti", "moodle", "aiken", "respondus", "gift"],
            "max_questions_per_request": 100,
            "batch_processing": true,
            "priority_support": true
        },
        "usage": {
            "requests_this_month": 1250,
            "questions_generated": 8750,
            "requests_remaining": 8750
        }
    }
}
```

### 7. Health Check

Check API health and status.

**Endpoint:** `GET /health`

**Response:**
```json
{
    "success": true,
    "data": {
        "status": "healthy",
        "version": "1.0.0",
        "uptime_seconds": 86400,
        "services": {
            "database": "healthy",
            "ai_models": "healthy",
            "file_storage": "healthy",
            "license_service": "healthy"
        },
        "performance": {
            "avg_response_time_ms": 850,
            "requests_per_minute": 45,
            "error_rate_percent": 0.2
        }
    },
    "timestamp": "2025-06-27T20:31:02Z"
}
```

---

## Error Responses

### Error Format

```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid input parameters",
        "details": {
            "field": "num_questions",
            "value": 150,
            "constraint": "Maximum 100 questions for premium tier"
        }
    },
    "metadata": {
        "request_id": "req_error123",
        "timestamp": "2025-06-27T20:31:02Z"
    }
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Invalid request parameters |
| `AUTHENTICATION_ERROR` | 401 | Invalid or missing API key |
| `LICENSE_ERROR` | 403 | License validation failed |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `PROCESSING_ERROR` | 500 | Internal processing error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

---

## Rate Limiting

### Limits by Tier

| Tier | Requests/Minute | Requests/Hour | Requests/Day |
|------|-----------------|---------------|--------------|
| Free | 10 | 100 | 1,000 |
| Premium | 100 | 1,000 | 10,000 |
| Enterprise | 1,000 | 10,000 | 100,000 |

### Rate Limit Headers

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
X-RateLimit-Tier: premium
```

---

## Webhooks

### Webhook Events

Configure webhooks to receive notifications about processing completion.

**Supported Events:**
- `question.generated` - Questions generated successfully
- `export.completed` - Export process completed
- `batch.completed` - Batch processing completed
- `license.expiring` - License expiring soon

**Webhook Payload:**
```json
{
    "event": "question.generated",
    "data": {
        "request_id": "req_abc123",
        "questions_count": 5,
        "processing_time_ms": 2500,
        "tier": "premium"
    },
    "timestamp": "2025-06-27T20:31:02Z",
    "signature": "sha256=abc123..."
}
```

---

## SDKs and Libraries

### Python SDK

```bash
pip install quizaigen-sdk
```

```python
from quizaigen_sdk import QuizAIGenClient

client = QuizAIGenClient(
    api_key="your-api-key",
    license_key="your-license-key",
    base_url="https://api.quizaigen.com/v1"
)

# Generate questions
questions = client.generate_questions(
    text="Your text here",
    type="mcq",
    num_questions=5
)

# Export questions
export_data = client.export_questions(
    questions=questions,
    format="qti"
)
```

### JavaScript SDK

```bash
npm install quizaigen-js
```

```javascript
import { QuizAIGenClient } from 'quizaigen-js';

const client = new QuizAIGenClient({
    apiKey: 'your-api-key',
    licenseKey: 'your-license-key',
    baseUrl: 'https://api.quizaigen.com/v1'
});

// Generate questions
const questions = await client.generateQuestions({
    text: 'Your text here',
    type: 'mcq',
    numQuestions: 5
});

// Export questions
const exportData = await client.exportQuestions({
    questions: questions,
    format: 'qti'
});
```

---

## Testing

### Test API Key

Use the following test API key for development:
```
test_sk_1234567890abcdef
```

### Sample Requests

```bash
# Generate MCQ questions
curl -X POST https://api.quizaigen.com/v1/generate \
  -H "Authorization: Bearer test_sk_1234567890abcdef" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Python is a programming language",
    "type": "mcq",
    "num_questions": 2
  }'

# Export questions
curl -X POST https://api.quizaigen.com/v1/export \
  -H "Authorization: Bearer test_sk_1234567890abcdef" \
  -H "Content-Type: application/json" \
  -d '{
    "questions": [...],
    "format": "json"
  }'
```

---

*For additional API documentation and examples, visit [docs.quizaigen.com](https://docs.quizaigen.com)*
