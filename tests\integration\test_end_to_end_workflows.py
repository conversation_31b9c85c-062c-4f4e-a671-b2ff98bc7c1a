"""
End-to-End Integration Tests for QuizAIGen

Tests complete workflows from input processing to question generation
and export across different question types and input formats.
"""

import pytest
import tempfile
import os
import json
from pathlib import Path
from typing import List, Dict, Any

from quizaigen import QuestionGenerator, ExportManager, BatchProcessor
from quizaigen.inputs import TextProcessor, DocumentProcessor, URLProcessor
from quizaigen.generators.base import Question


class TestEndToEndWorkflows:
    """Test complete end-to-end workflows."""
    
    @pytest.fixture
    def sample_text(self):
        """Sample text for testing."""
        return """
        Python is a high-level, interpreted programming language with dynamic semantics.
        Its high-level built-in data structures, combined with dynamic typing and dynamic binding,
        make it very attractive for Rapid Application Development, as well as for use as a
        scripting or glue language to connect existing components together.
        
        Python's simple, easy to learn syntax emphasizes readability and therefore reduces
        the cost of program maintenance. Python supports modules and packages, which encourages
        program modularity and code reuse. The Python interpreter and the extensive standard
        library are available in source or binary form without charge for all major platforms,
        and can be freely distributed.
        """
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    def test_complete_mcq_workflow(self, sample_text, temp_dir):
        """Test complete MCQ generation workflow."""
        # Initialize components
        generator = QuestionGenerator()
        exporter = ExportManager()
        
        # Generate MCQ questions
        questions = generator.generate_mcq(sample_text, num_questions=3)
        
        # Validate questions
        assert len(questions) >= 1
        assert all(isinstance(q, Question) for q in questions)
        assert all(q.type == "mcq" for q in questions)
        assert all(len(q.options) >= 2 for q in questions)
        assert all(q.answer for q in questions)
        
        # Export to different formats
        output_files = []
        
        # JSON export
        json_file = os.path.join(temp_dir, "mcq_questions.json")
        exporter.export_questions(questions, json_file, format="json")
        output_files.append(json_file)
        
        # CSV export
        csv_file = os.path.join(temp_dir, "mcq_questions.csv")
        exporter.export_questions(questions, csv_file, format="csv")
        output_files.append(csv_file)
        
        # Verify files were created
        for file_path in output_files:
            assert os.path.exists(file_path)
            assert os.path.getsize(file_path) > 0
        
        # Verify JSON content
        with open(json_file, 'r', encoding='utf-8') as f:
            exported_data = json.load(f)
            assert 'questions' in exported_data
            assert len(exported_data['questions']) >= 1
    
    def test_complete_boolean_workflow(self, sample_text, temp_dir):
        """Test complete Boolean question generation workflow."""
        generator = QuestionGenerator()
        exporter = ExportManager()
        
        # Generate Boolean questions
        questions = generator.generate_boolean(sample_text, num_questions=2)
        
        # Validate questions
        assert len(questions) >= 1
        assert all(isinstance(q, Question) for q in questions)
        assert all(q.type == "boolean" for q in questions)
        assert all(q.answer.lower() in ['true', 'false'] for q in questions)
        
        # Export to QTI format
        qti_file = os.path.join(temp_dir, "boolean_questions.xml")
        exporter.export_questions(questions, qti_file, format="qti")
        
        # Verify file creation
        assert os.path.exists(qti_file)
        assert os.path.getsize(qti_file) > 0
    
    def test_complete_faq_workflow(self, sample_text, temp_dir):
        """Test complete FAQ generation workflow."""
        generator = QuestionGenerator()
        exporter = ExportManager()
        
        # Generate FAQ questions
        questions = generator.generate_faq(sample_text, num_questions=2)
        
        # Validate questions
        assert len(questions) >= 1
        assert all(isinstance(q, Question) for q in questions)
        assert all(q.type == "faq" for q in questions)
        assert all(q.answer for q in questions)
        
        # Export to Moodle XML format
        moodle_file = os.path.join(temp_dir, "faq_questions.xml")
        exporter.export_questions(questions, moodle_file, format="moodle")
        
        # Verify file creation
        assert os.path.exists(moodle_file)
        assert os.path.getsize(moodle_file) > 0
    
    def test_batch_processing_workflow(self, temp_dir):
        """Test batch processing workflow with multiple texts."""
        # Create sample texts
        texts = [
            "Machine learning is a subset of artificial intelligence that focuses on algorithms.",
            "Deep learning uses neural networks with multiple layers to model data.",
            "Natural language processing enables computers to understand human language."
        ]
        
        # Initialize batch processor
        batch_processor = BatchProcessor()
        
        # Process batch
        all_questions = []
        for i, text in enumerate(texts):
            questions = batch_processor.process_text(
                text, 
                question_types=["mcq", "boolean"], 
                num_questions_per_type=1
            )
            all_questions.extend(questions)
        
        # Validate batch results
        assert len(all_questions) >= len(texts)  # At least one question per text
        
        # Export batch results
        exporter = ExportManager()
        batch_file = os.path.join(temp_dir, "batch_questions.json")
        exporter.export_questions(all_questions, batch_file, format="json")
        
        # Verify batch export
        assert os.path.exists(batch_file)
        with open(batch_file, 'r', encoding='utf-8') as f:
            batch_data = json.load(f)
            assert 'questions' in batch_data
            assert len(batch_data['questions']) >= len(texts)
    
    def test_document_processing_workflow(self, temp_dir):
        """Test document processing workflow."""
        # Create a sample text file
        sample_content = """
        Artificial Intelligence (AI) is intelligence demonstrated by machines,
        in contrast to the natural intelligence displayed by humans and animals.
        Leading AI textbooks define the field as the study of "intelligent agents":
        any device that perceives its environment and takes actions that maximize
        its chance of successfully achieving its goals.
        """
        
        # Create temporary text file
        text_file = os.path.join(temp_dir, "sample.txt")
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(sample_content)
        
        # Process document
        doc_processor = DocumentProcessor()
        processed_text = doc_processor.process_file(text_file)
        
        # Generate questions from processed document
        generator = QuestionGenerator()
        questions = generator.generate_mcq(processed_text, num_questions=2)
        
        # Validate document processing workflow
        assert len(questions) >= 1
        assert all(isinstance(q, Question) for q in questions)
        
        # Export results
        exporter = ExportManager()
        output_file = os.path.join(temp_dir, "document_questions.json")
        exporter.export_questions(questions, output_file, format="json")
        
        assert os.path.exists(output_file)
    
    def test_quality_control_integration(self, sample_text):
        """Test integration with quality control features."""
        generator = QuestionGenerator()
        
        # Generate questions with quality control
        questions = generator.generate_mcq(
            sample_text, 
            num_questions=5,
            min_quality_score=0.7,
            remove_duplicates=True
        )
        
        # Validate quality control
        assert len(questions) >= 1
        assert all(q.metadata.get('quality_score', 0) >= 0.7 for q in questions)
        
        # Check for duplicates (should be none)
        question_texts = [q.question for q in questions]
        assert len(question_texts) == len(set(question_texts))
    
    def test_multilingual_workflow(self, temp_dir):
        """Test multilingual question generation workflow."""
        # Test with different languages
        test_cases = [
            ("Python is a programming language.", "en"),
            ("Python es un lenguaje de programación.", "es"),
            ("Python est un langage de programmation.", "fr")
        ]
        
        generator = QuestionGenerator()
        exporter = ExportManager()
        
        all_questions = []
        
        for text, expected_lang in test_cases:
            try:
                questions = generator.generate_mcq(text, num_questions=1)
                if questions:
                    all_questions.extend(questions)
                    # Verify language detection worked
                    detected_lang = questions[0].metadata.get('language', 'en')
                    # Language detection might not be perfect, so we just check it's reasonable
                    assert detected_lang in ['en', 'es', 'fr', 'de', 'it', 'pt']
            except Exception as e:
                # Multilingual features might not be fully available in all environments
                pytest.skip(f"Multilingual test skipped due to: {str(e)}")
        
        if all_questions:
            # Export multilingual results
            output_file = os.path.join(temp_dir, "multilingual_questions.json")
            exporter.export_questions(all_questions, output_file, format="json")
            assert os.path.exists(output_file)
    
    def test_error_handling_workflow(self):
        """Test error handling in complete workflows."""
        generator = QuestionGenerator()
        
        # Test with empty text
        questions = generator.generate_mcq("", num_questions=1)
        assert len(questions) == 0
        
        # Test with very short text
        questions = generator.generate_mcq("Hi", num_questions=1)
        assert len(questions) == 0
        
        # Test with invalid parameters
        questions = generator.generate_mcq("Valid text here", num_questions=0)
        assert len(questions) == 0
        
        questions = generator.generate_mcq("Valid text here", num_questions=-1)
        assert len(questions) == 0
