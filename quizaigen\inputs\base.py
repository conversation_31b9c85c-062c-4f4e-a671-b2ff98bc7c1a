"""
Base Input Processor

This module provides the base class for all input processors.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Union
from pathlib import Path

from ..core.config import Config
from ..utils.logger import LoggerMixin
from ..core.exceptions import InputError, ProcessingError


class BaseInputProcessor(LoggerMixin, ABC):
    """Base class for all input processors."""
    
    def __init__(self, config: Optional[Union[Dict[str, Any], Config]] = None):
        """
        Initialize the base input processor.

        Args:
            config: Configuration dictionary or Config object
        """
        super().__init__()
        if isinstance(config, Config):
            self.config = config
        else:
            self.config = Config(config) if config else Config()
        self.log_info(f"Initialized {self.__class__.__name__}")
    
    @abstractmethod
    def process(self, input_data: Any, **kwargs) -> Dict[str, Any]:
        """
        Process input data.
        
        Args:
            input_data: Input data to process
            **kwargs: Additional processing options
        
        Returns:
            Dictionary containing processed results
        
        Raises:
            ProcessingError: If processing fails
        """
        pass
    
    def validate_input(self, input_data: Any) -> bool:
        """
        Validate input data.
        
        Args:
            input_data: Input data to validate
        
        Returns:
            True if valid, False otherwise
        """
        return input_data is not None
    
    def get_supported_formats(self) -> list:
        """
        Get list of supported input formats.
        
        Returns:
            List of supported formats
        """
        return []
    
    def _handle_error(self, error: Exception, context: str = "") -> Dict[str, Any]:
        """
        Handle processing errors.
        
        Args:
            error: The exception that occurred
            context: Additional context information
        
        Returns:
            Error result dictionary
        """
        error_msg = f"{context}: {str(error)}" if context else str(error)
        self.log_error(error_msg)
        
        return {
            'success': False,
            'error': error_msg,
            'error_type': type(error).__name__,
            'text': '',
            'metadata': {}
        }
    
    def _create_success_result(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a successful processing result.
        
        Args:
            text: Processed text
            metadata: Additional metadata
        
        Returns:
            Success result dictionary
        """
        return {
            'success': True,
            'text': text,
            'metadata': metadata or {},
            'processor': self.__class__.__name__
        }
