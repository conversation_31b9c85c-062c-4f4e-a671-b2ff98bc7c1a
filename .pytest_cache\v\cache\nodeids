["tests/test_advanced_generators.py::TestFillBlankGenerator::test_basic_generation", "tests/test_advanced_generators.py::TestFillBlankGenerator::test_blank_candidate_selection", "tests/test_advanced_generators.py::TestFillBlankGenerator::test_blank_position_metadata", "tests/test_advanced_generators.py::TestFillBlankGenerator::test_confidence_calculation", "tests/test_advanced_generators.py::TestFillBlankGenerator::test_difficulty_assessment", "tests/test_advanced_generators.py::TestFillBlankGenerator::test_sentence_filtering", "tests/test_advanced_generators.py::TestGeneratorIntegration::test_error_handling", "tests/test_advanced_generators.py::TestGeneratorIntegration::test_license_tier_integration", "tests/test_advanced_generators.py::TestQuestionParaphraser::test_answer_preservation", "tests/test_advanced_generators.py::TestQuestionParaphraser::test_basic_paraphrasing", "tests/test_advanced_generators.py::TestQuestionParaphraser::test_confidence_adjustment", "tests/test_advanced_generators.py::TestQuestionParaphraser::test_multiple_variations", "tests/test_advanced_generators.py::TestQuestionParaphraser::test_question_type_preservation", "tests/test_advanced_generators.py::TestShortAnswerGenerator::test_basic_generation", "tests/test_advanced_generators.py::TestShortAnswerGenerator::test_empty_text_handling", "tests/test_advanced_generators.py::TestShortAnswerGenerator::test_entity_extraction", "tests/test_advanced_generators.py::TestShortAnswerGenerator::test_metadata_inclusion", "tests/test_advanced_generators.py::TestShortAnswerGenerator::test_question_strategies", "tests/test_ai_models.py::TestAIModelIntegration::test_bert_integration_mock", "tests/test_ai_models.py::TestAIModelIntegration::test_missing_dependencies_handling", "tests/test_ai_models.py::TestAIModelIntegration::test_model_tier_access_control", "tests/test_ai_models.py::TestAIModelIntegration::test_t5_integration_mock", "tests/test_ai_models.py::TestBaseModel::test_lightweight_model", "tests/test_ai_models.py::TestBaseModel::test_model_config_creation", "tests/test_ai_models.py::TestBaseModel::test_model_output_creation", "tests/test_ai_models.py::TestInferencePipeline::test_pipeline_initialization", "tests/test_ai_models.py::TestInferencePipeline::test_task_model_mapping", "tests/test_ai_models.py::TestModelCache::test_cache_miss", "tests/test_ai_models.py::TestModelCache::test_cache_put_get", "tests/test_ai_models.py::TestModelCache::test_cache_stats", "tests/test_ai_models.py::TestModelManager::test_model_manager_initialization", "tests/test_ai_models.py::TestModelManager::test_tier_based_model_access", "tests/test_ai_models.py::TestQualityEnhancement::test_inference_request_structure", "tests/test_ai_models.py::TestQualityEnhancement::test_quality_enhancement_structure", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_analyze_content_structure", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_analyze_text_quality", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_analyze_text_quality_empty_text", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_batch_process_empty_list", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_calculate_file_hash", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_clean_extracted_text", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_determine_reading_level", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_enhance_processing_result", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_enhance_processing_result_failed", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_extract_text_only_failed", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_extract_text_only_interface", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_get_document_info_nonexistent", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_get_processing_capabilities", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_get_supported_formats", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_initialization", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_initialization_with_config", "tests/test_document_processor_core.py::TestDocumentProcessorCore::test_validate_file_not_exists", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_analyze_content_structure", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_analyze_text_quality", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_analyze_text_quality_empty_text", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_batch_process_empty_list", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_calculate_file_hash", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_clean_extracted_text", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_determine_reading_level", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_enhance_processing_result", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_enhance_processing_result_failed", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_extract_with_ocr", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_extract_with_pdfplumber", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_extract_with_pypdf2", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_get_document_info_nonexistent", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_get_processing_capabilities", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_get_supported_formats", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_initialization", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_initialization_with_config", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_process_word_enhanced", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_validate_file_not_exists", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_validate_file_success", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_validate_file_too_large", "tests/test_enhanced_document_processor.py::TestEnhancedDocumentProcessor::test_validate_file_unsupported_format", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_basic_question_generation", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_blank_candidate_finding", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_blank_type_classification", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_compatibility_alias", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_context_importance_calculation", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_different_difficulty_levels", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_difficulty_alignment", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_enhanced_confidence_calculation", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_enhanced_sentence_extraction", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_fallback_without_transformers", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_initialization_free_tier", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_initialization_premium_tier", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_key_concept_detection", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_question_quality_calculation", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_sentence_filtering_and_ranking", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_sentence_suitability_calculation", "tests/test_enhanced_fill_blank_generator.py::TestEnhancedFillBlankGenerator::test_word_difficulty_estimation", "tests/test_multilingual_generator.py::TestMultilingualGenerationConfig::test_config_to_dict", "tests/test_multilingual_generator.py::TestMultilingualGenerationConfig::test_custom_config", "tests/test_multilingual_generator.py::TestMultilingualGenerationConfig::test_default_config", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_concept_extraction", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_empty_text_validation", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_generate_boolean_questions_spanish", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_generate_faq_questions_french", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_generate_fill_blank_questions_german", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_generate_mcq_questions_english", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_generator_initialization", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_generator_initialization_without_config", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_important_words_finding", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_keyword_extraction", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_language_detection", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_mcq_options_generation", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_statement_creation", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_supported_languages", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_text_processing_fallback", "tests/test_multilingual_generator.py::TestMultilingualQuestionGenerator::test_unsupported_question_type", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_confidence_threshold_filtering", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_cultural_adaptation", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_english_text_processing", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_entity_preservation_across_languages", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_french_text_processing", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_german_text_processing", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_language_fallback_mechanism", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_mixed_language_batch_processing", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_question_quality_metrics", "tests/test_multilingual_integration.py::TestMultilingualIntegration::test_spanish_text_processing", "tests/test_multilingual_support.py::TestLanguageDetector::test_detect_english_text", "tests/test_multilingual_support.py::TestLanguageDetector::test_detect_french_text", "tests/test_multilingual_support.py::TestLanguageDetector::test_detect_german_text", "tests/test_multilingual_support.py::TestLanguageDetector::test_detect_spanish_text", "tests/test_multilingual_support.py::TestLanguageDetector::test_detector_initialization", "tests/test_multilingual_support.py::TestLanguageDetector::test_empty_text_fallback", "tests/test_multilingual_support.py::TestLanguageDetector::test_language_names", "tests/test_multilingual_support.py::TestLanguageDetector::test_short_text_fallback", "tests/test_multilingual_support.py::TestLanguageDetector::test_supported_languages", "tests/test_multilingual_support.py::TestMultilingualIntegration::test_language_detection_and_template_selection", "tests/test_multilingual_support.py::TestMultilingualIntegration::test_multilingual_workflow", "tests/test_multilingual_support.py::TestMultilingualNLPManager::test_manager_initialization", "tests/test_multilingual_support.py::TestMultilingualNLPManager::test_model_availability_check", "tests/test_multilingual_support.py::TestMultilingualNLPManager::test_model_configuration", "tests/test_multilingual_support.py::TestMultilingualNLPManager::test_model_info_retrieval", "tests/test_multilingual_support.py::TestMultilingualNLPManager::test_model_loading_success", "tests/test_multilingual_support.py::TestMultilingualNLPManager::test_model_loading_unsupported_language", "tests/test_multilingual_support.py::TestMultilingualNLPManager::test_model_unloading", "tests/test_multilingual_support.py::TestMultilingualNLPManager::test_supported_languages", "tests/test_multilingual_support.py::TestMultilingualNLPManager::test_text_processing", "tests/test_multilingual_support.py::TestMultilingualTemplates::test_english_templates", "tests/test_multilingual_support.py::TestMultilingualTemplates::test_french_templates", "tests/test_multilingual_support.py::TestMultilingualTemplates::test_german_templates", "tests/test_multilingual_support.py::TestMultilingualTemplates::test_random_template_selection", "tests/test_multilingual_support.py::TestMultilingualTemplates::test_spanish_templates", "tests/test_multilingual_support.py::TestMultilingualTemplates::test_template_formatting", "tests/test_multilingual_support.py::TestMultilingualTemplates::test_template_initialization", "tests/test_multilingual_support.py::TestMultilingualTemplates::test_unsupported_language_fallback", "tests/test_multilingual_support.py::TestMultilingualTemplates::test_unsupported_question_type", "tests/test_quality_control.py::TestBloomClassifier::test_batch_classification", "tests/test_quality_control.py::TestBloomClassifier::test_bloom_classifier_initialization", "tests/test_quality_control.py::TestBloomClassifier::test_bloom_distribution", "tests/test_quality_control.py::TestBloomClassifier::test_classify_analyze_question", "tests/test_quality_control.py::TestBloomClassifier::test_classify_remember_question", "tests/test_quality_control.py::TestContentFilter::test_batch_filtering", "tests/test_quality_control.py::TestContentFilter::test_content_filter_initialization", "tests/test_quality_control.py::TestContentFilter::test_filter_appropriate_question", "tests/test_quality_control.py::TestContentFilter::test_filter_inappropriate_question", "tests/test_quality_control.py::TestContentFilter::test_get_appropriate_questions", "tests/test_quality_control.py::TestDifficultyAssessor::test_assess_complex_question", "tests/test_quality_control.py::TestDifficultyAssessor::test_assess_simple_question", "tests/test_quality_control.py::TestDifficultyAssessor::test_batch_assessment", "tests/test_quality_control.py::TestDifficultyAssessor::test_difficulty_assessor_initialization", "tests/test_quality_control.py::TestDifficultyAssessor::test_difficulty_distribution", "tests/test_quality_control.py::TestDuplicateDetector::test_detect_exact_duplicates", "tests/test_quality_control.py::TestDuplicateDetector::test_detect_fuzzy_duplicates", "tests/test_quality_control.py::TestDuplicateDetector::test_duplicate_detector_initialization", "tests/test_quality_control.py::TestDuplicateDetector::test_duplicate_statistics", "tests/test_quality_control.py::TestDuplicateDetector::test_group_duplicates", "tests/test_quality_control.py::TestDuplicateDetector::test_remove_duplicates", "tests/test_quality_control.py::test_quality_control_integration"]