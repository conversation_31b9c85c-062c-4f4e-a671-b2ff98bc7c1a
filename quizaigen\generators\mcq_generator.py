"""
Multiple Choice Question Generator

This module implements MCQ generation using transformer models.
"""
import re
import random
from typing import List, Dict, Any, Optional

from .base import BaseQuestionGenerator, Question
from ..core.exceptions import ProcessingError
from ..utils.text_utils import extract_sentences, extract_keywords
from ..utils.question_utils import generate_simple_distractors, calculate_question_confidence, select_unused_sentences, validate_question_quality


class MCQGenerator(BaseQuestionGenerator):
    """Generator for Multiple Choice Questions."""
    
    def _get_question_type(self) -> str:
        """Get the question type identifier."""
        return 'mcq'
    
    def _generate_questions_impl(self, text: str, num_questions: int,
                               **kwargs) -> List[Question]:
        """
        Generate MCQ questions from text.

        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of generated MCQ questions
        """
        self.log_info("Starting MCQ generation", extra_data={
            'text_length': len(text),
            'num_questions': num_questions,
            'kwargs': kwargs
        })

        # Extract sentences for question generation
        sentences = extract_sentences(text, min_length=20, max_length=200)

        if len(sentences) < num_questions:
            self.log_warning(f"Only {len(sentences)} suitable sentences found for {num_questions} questions")
            num_questions = len(sentences)

        self.log_info(f"Extracted {len(sentences)} sentences, generating {num_questions} questions")
        
        questions = []
        used_sentences = set()
        
        for i in range(num_questions):
            # Select a sentence that hasn't been used
            available_sentences = [s for s in sentences if s not in used_sentences]
            if not available_sentences:
                break
            
            sentence = random.choice(available_sentences)
            used_sentences.add(sentence)
            
            # Generate question from sentence
            question = self._generate_mcq_from_sentence(sentence, text)
            if question and self._validate_generated_question(question):
                questions.append(question)
                self.log_debug(f"Generated MCQ question {len(questions)}: {question.question[:50]}...")
            else:
                self.log_warning(f"Failed to generate valid question from sentence: {sentence[:50]}...")

        self.log_info(f"MCQ generation completed. Generated {len(questions)} questions")
        return questions
    
    def _generate_mcq_from_sentence(self, sentence: str, context: str) -> Optional[Question]:
        """
        Generate an MCQ question from a sentence.
        
        Args:
            sentence: Source sentence
            context: Full context text
        
        Returns:
            Generated MCQ question or None if generation fails
        """
        try:
            # Extract key information from sentence
            keywords = extract_keywords(sentence, top_k=5)
            if not keywords:
                return None
            
            # Select a keyword to create a question about
            target_keyword = keywords[0]
            
            # Create question by replacing the keyword with a blank
            question_text = self._create_question_text(sentence, target_keyword)
            if not question_text:
                return None
            
            # Generate answer options
            correct_answer = target_keyword
            distractors = self._generate_distractors(target_keyword, context, sentence)
            
            if len(distractors) < 2:
                # Fallback to simple distractors
                distractors = self._generate_simple_distractors(target_keyword)
            
            # Combine correct answer with distractors
            all_options = [correct_answer] + distractors[:3]  # Limit to 4 options total
            random.shuffle(all_options)
            
            # Find the position of correct answer after shuffling
            correct_index = all_options.index(correct_answer)
            correct_answer_letter = chr(ord('A') + correct_index)
            
            # Calculate confidence score
            confidence = self._calculate_confidence(sentence, target_keyword, distractors)
            
            return Question(
                question=question_text,
                type='mcq',
                answer=correct_answer_letter,
                options=all_options,
                explanation=f"The correct answer is '{correct_answer}' based on the context.",
                difficulty='medium',
                keywords=[target_keyword],
                confidence=confidence,
                metadata={
                    'source_sentence': sentence,
                    'target_keyword': target_keyword,
                    'generation_method': 'keyword_replacement'
                }
            )
            
        except Exception as e:
            self.log_error(f"Failed to generate MCQ from sentence: {str(e)}")
            return None
    
    def _create_question_text(self, sentence: str, target_keyword: str) -> Optional[str]:
        """
        Create question text by replacing target keyword with blank.
        
        Args:
            sentence: Source sentence
            target_keyword: Keyword to replace
        
        Returns:
            Question text or None if creation fails
        """
        # Find the keyword in the sentence (case-insensitive)
        pattern = re.compile(re.escape(target_keyword), re.IGNORECASE)
        match = pattern.search(sentence)
        
        if not match:
            return None
        
        # Replace the keyword with a blank
        question_text = pattern.sub("_____", sentence, count=1)
        
        # Add question format
        question_text = f"Fill in the blank: {question_text}"
        
        # Convert to multiple choice format
        question_text = question_text.replace("Fill in the blank:", "What word best completes this sentence?")
        
        return question_text
    
    def _generate_distractors(self, target_keyword: str, context: str, 
                            sentence: str) -> List[str]:
        """
        Generate distractor options for MCQ.
        
        Args:
            target_keyword: The correct answer
            context: Full text context
            sentence: Source sentence
        
        Returns:
            List of distractor options
        """
        distractors = []
        
        # Extract other keywords from context as potential distractors
        context_keywords = extract_keywords(context, top_k=20)
        
        # Filter keywords that are similar in type/category to target
        for keyword in context_keywords:
            if (keyword != target_keyword.lower() and 
                keyword != target_keyword and
                len(keyword) > 2 and
                self._is_similar_type(target_keyword, keyword)):
                distractors.append(keyword.title())
        
        # Remove duplicates and limit
        distractors = list(set(distractors))[:5]
        
        return distractors
    
    def _generate_simple_distractors(self, target_keyword: str) -> List[str]:
        """
        Generate simple distractors when context-based generation fails.
        
        Args:
            target_keyword: The correct answer
        
        Returns:
            List of simple distractor options
        """
        return generate_simple_distractors(target_keyword, 'general')[:3]
    
    def _is_similar_type(self, word1: str, word2: str) -> bool:
        """
        Check if two words are of similar type (rough heuristic).
        
        Args:
            word1: First word
            word2: Second word
        
        Returns:
            True if words are likely of similar type
        """
        # Simple heuristics for word similarity
        
        # Both are numbers
        if word1.isdigit() and word2.isdigit():
            return True
        
        # Both are capitalized (likely proper nouns)
        if word1[0].isupper() and word2[0].isupper():
            return True
        
        # Similar length (rough approximation)
        if abs(len(word1) - len(word2)) <= 2:
            return True
        
        return False
    
    def _calculate_confidence(self, sentence: str, target_keyword: str, 
                            distractors: List[str]) -> float:
        """
        Calculate confidence score for the generated question.
        
        Args:
            sentence: Source sentence
            target_keyword: Target keyword
            distractors: Generated distractors
        
        Returns:
            Confidence score between 0 and 1
        """
        confidence = 0.5  # Base confidence
        
        # Increase confidence if sentence is well-formed
        if len(sentence.split()) >= 8:
            confidence += 0.1
        
        # Increase confidence if we have good distractors
        if len(distractors) >= 3:
            confidence += 0.2
        
        # Increase confidence if target keyword is significant
        if len(target_keyword) >= 4:
            confidence += 0.1
        
        # Decrease confidence if sentence is too short or too long
        if len(sentence) < 30 or len(sentence) > 150:
            confidence -= 0.1
        
        return max(0.0, min(1.0, confidence))
    
    def get_supported_parameters(self) -> Dict[str, Any]:
        """Get supported parameters for MCQ generation."""
        base_params = super().get_supported_parameters()
        
        mcq_params = {
            'difficulty': {
                'type': 'str',
                'description': 'Question difficulty level',
                'choices': ['easy', 'medium', 'hard'],
                'default': 'medium'
            },
            'num_options': {
                'type': 'int',
                'description': 'Number of answer options',
                'default': 4,
                'min': 2,
                'max': 6
            }
        }
        
        base_params.update(mcq_params)
        return base_params
