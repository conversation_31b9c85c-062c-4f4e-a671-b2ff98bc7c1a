# Private PyPI Server Infrastructure for QuizAIGen Commercial Distribution
version: '3.8'

services:
  # Private PyPI Server using pypiserver
  pypi-server:
    image: pypiserver/pypiserver:latest
    container_name: quizaigen-pypi
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./data/packages:/data/packages
      - ./config/pypi-server:/data/config
      - ./logs/pypi:/data/logs
    environment:
      - PYPI_PASSWORDS=/data/config/htpasswd
    command: >
      -p 8080
      --hash-algo sha256
      --authenticate upload
      --passwords /data/config/htpasswd
      --disable-fallback
      --log-file /data/logs/pypi.log
      --verbose
      /data/packages
    networks:
      - quizaigen-network

  # License Management API
  license-api:
    build:
      context: ./license-api
      dockerfile: Dockerfile
    container_name: quizaigen-license-api
    restart: unless-stopped
    ports:
      - "8081:8000"
    volumes:
      - ./data/licenses:/app/data
      - ./config/license-api:/app/config
      - ./logs/license-api:/app/logs
    environment:
      - DATABASE_URL=****************************************************/license_db
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - PYPI_SERVER_URL=http://pypi-server:8080
    depends_on:
      - postgres
      - redis
    networks:
      - quizaigen-network

  # PostgreSQL Database for License Management
  postgres:
    image: postgres:15-alpine
    container_name: quizaigen-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./config/postgres:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_DB=license_db
      - POSTGRES_USER=license_user
      - POSTGRES_PASSWORD=license_pass
    networks:
      - quizaigen-network

  # Redis for Caching and Session Management
  redis:
    image: redis:7-alpine
    container_name: quizaigen-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - quizaigen-network

  # Customer Portal Web Interface
  customer-portal:
    build:
      context: ./customer-portal
      dockerfile: Dockerfile
    container_name: quizaigen-portal
    restart: unless-stopped
    ports:
      - "8082:3000"
    volumes:
      - ./config/portal:/app/config
    environment:
      - REACT_APP_API_URL=http://localhost:8081
      - REACT_APP_PYPI_URL=http://localhost:8080
    depends_on:
      - license-api
    networks:
      - quizaigen-network

  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: quizaigen-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - pypi-server
      - license-api
      - customer-portal
    networks:
      - quizaigen-network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: quizaigen-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - quizaigen-network

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: quizaigen-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    depends_on:
      - prometheus
    networks:
      - quizaigen-network

networks:
  quizaigen-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
