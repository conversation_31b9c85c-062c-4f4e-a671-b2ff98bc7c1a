#!/usr/bin/env python3
"""
QuizAIGen Basic Usage Example

This script demonstrates the basic usage of the QuizAIGen library
for generating questions from text.
"""

import sys
import os

# Add the parent directory to the path so we can import quizaigen
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quizaigen import QuestionGenerator, ExportManager, Config


def main():
    """Main function demonstrating QuizAIGen usage."""
    
    # Sample text for question generation
    sample_text = """
    Artificial Intelligence (AI) is a branch of computer science that aims to create 
    intelligent machines that work and react like humans. Some of the activities 
    computers with artificial intelligence are designed for include speech recognition, 
    learning, planning, and problem solving.
    
    Machine learning is a subset of AI that provides systems the ability to automatically 
    learn and improve from experience without being explicitly programmed. Machine learning 
    focuses on the development of computer programs that can access data and use it to 
    learn for themselves.
    
    Deep learning is a subset of machine learning that uses neural networks with three 
    or more layers. These neural networks attempt to simulate the behavior of the human 
    brain, allowing it to learn from large amounts of data. While a neural network with 
    a single layer can still make approximate predictions, additional hidden layers can 
    help to optimize and refine for accuracy.
    """
    
    print("QuizAIGen Basic Usage Example")
    print("=" * 40)
    
    # Initialize the question generator
    print("\n1. Initializing QuestionGenerator...")
    generator = QuestionGenerator()
    
    # Display available question types
    print(f"Available question types: {generator.get_available_question_types()}")
    
    # Generate MCQ questions
    print("\n2. Generating Multiple Choice Questions...")
    try:
        mcq_questions = generator.generate_mcq(sample_text, num_questions=3)
        print(f"Generated {len(mcq_questions)} MCQ questions:")
        
        for i, question in enumerate(mcq_questions, 1):
            print(f"\nQuestion {i}:")
            print(f"Q: {question['question']}")
            print(f"Options: {question.get('options', [])}")
            print(f"Answer: {question.get('answer', 'N/A')}")
            print(f"Confidence: {question.get('confidence', 'N/A')}")
    
    except Exception as e:
        print(f"Error generating MCQ questions: {e}")
    
    # Generate Boolean questions
    print("\n3. Generating Boolean Questions...")
    try:
        boolean_questions = generator.generate_boolean(sample_text, num_questions=3)
        print(f"Generated {len(boolean_questions)} Boolean questions:")
        
        for i, question in enumerate(boolean_questions, 1):
            print(f"\nQuestion {i}:")
            print(f"Q: {question['question']}")
            print(f"Answer: {question.get('answer', 'N/A')}")
            print(f"Confidence: {question.get('confidence', 'N/A')}")
    
    except Exception as e:
        print(f"Error generating Boolean questions: {e}")
    
    # Generate mixed questions
    print("\n4. Generating Mixed Questions...")
    try:
        mixed_questions = generator.generate_mixed(
            sample_text, 
            num_questions=4, 
            question_types=['mcq', 'boolean']
        )
        print(f"Generated {len(mixed_questions)} mixed questions:")
        
        for i, question in enumerate(mixed_questions, 1):
            print(f"\nQuestion {i} ({question['type']}):")
            print(f"Q: {question['question']}")
            if question['type'] == 'mcq':
                print(f"Options: {question.get('options', [])}")
            print(f"Answer: {question.get('answer', 'N/A')}")
    
    except Exception as e:
        print(f"Error generating mixed questions: {e}")
    
    # Export questions to different formats
    print("\n5. Exporting Questions...")
    if 'mixed_questions' in locals() and mixed_questions:
        export_manager = ExportManager()
        
        try:
            # Export to JSON
            export_manager.export_questions(
                mixed_questions, 
                "output/sample_questions.json", 
                format="json"
            )
            print("✓ Exported questions to JSON format")
            
            # Export to CSV
            export_manager.export_questions(
                mixed_questions, 
                "output/sample_questions.csv", 
                format="csv"
            )
            print("✓ Exported questions to CSV format")
            
            # Export to XML
            export_manager.export_questions(
                mixed_questions, 
                "output/sample_questions.xml", 
                format="xml"
            )
            print("✓ Exported questions to XML format")
        
        except Exception as e:
            print(f"Error exporting questions: {e}")
    
    # Display statistics
    print("\n6. Question Statistics...")
    if 'mixed_questions' in locals() and mixed_questions:
        stats = generator.get_statistics(mixed_questions)
        print("Statistics:")
        print(f"  Total questions: {stats['total_questions']}")
        print(f"  Questions by type: {stats['questions_by_type']}")
        if stats['average_confidence']:
            print(f"  Average confidence: {stats['average_confidence']:.2f}")
        if stats['difficulty_distribution']:
            print(f"  Difficulty distribution: {stats['difficulty_distribution']}")
    
    # Display generator information
    print("\n7. Generator Information...")
    for qtype in generator.get_available_question_types():
        info = generator.get_generator_info(qtype)
        print(f"\n{qtype.upper()} Generator:")
        print(f"  Model: {info['model_info']['model_name']}")
        print(f"  Max length: {info['model_info']['max_length']}")
        print(f"  Supported parameters: {list(info['supported_parameters'].keys())}")
    
    print("\n" + "=" * 40)
    print("Example completed successfully!")
    print("Check the 'output' directory for exported files.")


if __name__ == "__main__":
    main()
