"""
Mock responses and test utilities for QuizAIGen

Provides mock responses for external services, AI models,
and other dependencies to enable isolated testing.
"""

from typing import Dict, List, Any, Optional
from unittest.mock import Mock, MagicMock
import json


class MockResponses:
    """Mock responses for testing."""
    
    @staticmethod
    def mock_ai_model_response(question_type: str = "mcq") -> Dict[str, Any]:
        """
        Generate mock AI model response.
        
        Args:
            question_type: Type of question to mock
            
        Returns:
            Mock model response
        """
        responses = {
            'mcq': {
                'question': 'What is Python primarily used for?',
                'options': [
                    'Web development and data science',
                    'Only web development', 
                    'Only mobile apps',
                    'Only desktop applications'
                ],
                'answer': 'Web development and data science',
                'confidence': 0.85
            },
            'boolean': {
                'question': 'Python is an interpreted programming language.',
                'answer': 'True',
                'confidence': 0.92
            },
            'faq': {
                'question': 'What are the main features of Python?',
                'answer': 'Python features include simple syntax, dynamic typing, extensive libraries, and cross-platform compatibility.',
                'confidence': 0.88
            },
            'fill_blank': {
                'question': 'Python is a _____ programming language that emphasizes _____.',
                'answer': 'high-level, readability',
                'confidence': 0.79
            }
        }
        
        return responses.get(question_type, responses['mcq'])
    
    @staticmethod
    def mock_language_detection_response(language: str = "en") -> Dict[str, Any]:
        """
        Generate mock language detection response.
        
        Args:
            language: Language code to mock
            
        Returns:
            Mock detection response
        """
        return {
            'language': language,
            'confidence': 0.95,
            'detected_by': 'mock_detector',
            'alternatives': [
                ('es', 0.03),
                ('fr', 0.02)
            ],
            'is_reliable': True,
            'text_length': 100
        }
    
    @staticmethod
    def mock_quality_assessment_response(score: float = 0.8) -> Dict[str, Any]:
        """
        Generate mock quality assessment response.
        
        Args:
            score: Quality score to mock
            
        Returns:
            Mock quality assessment
        """
        return {
            'quality_score': score,
            'factors': {
                'clarity': 0.85,
                'relevance': 0.82,
                'difficulty': 0.75,
                'grammar': 0.90
            },
            'issues': [],
            'suggestions': ['Consider adding more context']
        }
    
    @staticmethod
    def mock_document_processing_response() -> Dict[str, Any]:
        """Generate mock document processing response."""
        return {
            'text': 'Extracted text from document',
            'metadata': {
                'file_type': 'pdf',
                'pages': 5,
                'word_count': 1250,
                'processing_time': 2.3
            },
            'structure': {
                'headings': ['Introduction', 'Methods', 'Results'],
                'paragraphs': 15,
                'tables': 2
            }
        }
    
    @staticmethod
    def mock_export_response(format_type: str = "json") -> Dict[str, Any]:
        """
        Generate mock export response.
        
        Args:
            format_type: Export format to mock
            
        Returns:
            Mock export response
        """
        return {
            'format': format_type,
            'file_size': 1024,
            'questions_exported': 5,
            'export_time': 0.5,
            'success': True
        }
    
    @staticmethod
    def create_mock_model_manager() -> Mock:
        """Create mock model manager for testing."""
        mock_manager = Mock()
        mock_manager.load_model.return_value = True
        mock_manager.is_model_loaded.return_value = True
        mock_manager.get_model_info.return_value = {
            'name': 'mock-model',
            'version': '1.0.0',
            'type': 'transformer'
        }
        mock_manager.generate_text.return_value = "Mock generated text"
        return mock_manager
    
    @staticmethod
    def create_mock_question_generator() -> Mock:
        """Create mock question generator for testing."""
        mock_generator = Mock()
        
        # Mock MCQ generation
        mock_generator.generate_mcq.return_value = [
            Mock(
                question="What is Python?",
                type="mcq",
                options=["A programming language", "A snake", "A tool", "A framework"],
                answer="A programming language",
                explanation="Python is a high-level programming language.",
                metadata={'quality_score': 0.85}
            )
        ]
        
        # Mock Boolean generation
        mock_generator.generate_boolean.return_value = [
            Mock(
                question="Python is object-oriented.",
                type="boolean",
                answer="True",
                explanation="Python supports object-oriented programming.",
                metadata={'quality_score': 0.90}
            )
        ]
        
        return mock_generator
    
    @staticmethod
    def create_mock_export_manager() -> Mock:
        """Create mock export manager for testing."""
        mock_exporter = Mock()
        mock_exporter.export_questions.return_value = {
            'success': True,
            'file_path': '/mock/path/questions.json',
            'questions_exported': 5
        }
        mock_exporter.get_supported_formats.return_value = [
            'json', 'csv', 'qti', 'moodle', 'aiken'
        ]
        return mock_exporter
    
    @staticmethod
    def create_mock_batch_processor() -> Mock:
        """Create mock batch processor for testing."""
        mock_processor = Mock()
        mock_processor.process_texts.return_value = [
            Mock(question="Mock question 1", type="mcq"),
            Mock(question="Mock question 2", type="boolean")
        ]
        mock_processor.get_processing_stats.return_value = {
            'texts_processed': 3,
            'questions_generated': 6,
            'processing_time': 5.2,
            'success_rate': 0.95
        }
        return mock_processor
    
    @staticmethod
    def mock_file_operations() -> Dict[str, Mock]:
        """Create mocks for file operations."""
        mocks = {}
        
        # Mock file reading
        mock_read = Mock()
        mock_read.return_value = "Mock file content"
        mocks['read_file'] = mock_read
        
        # Mock file writing
        mock_write = Mock()
        mock_write.return_value = True
        mocks['write_file'] = mock_write
        
        # Mock file existence check
        mock_exists = Mock()
        mock_exists.return_value = True
        mocks['file_exists'] = mock_exists
        
        return mocks
    
    @staticmethod
    def mock_network_responses() -> Dict[str, Any]:
        """Create mock network responses."""
        return {
            'url_content': {
                'status_code': 200,
                'content': 'Mock web page content for testing',
                'headers': {'content-type': 'text/html'},
                'url': 'https://example.com'
            },
            'api_response': {
                'status': 'success',
                'data': {'result': 'mock data'},
                'timestamp': '2024-01-01T00:00:00Z'
            }
        }
    
    @staticmethod
    def create_test_configuration() -> Dict[str, Any]:
        """Create test configuration."""
        return {
            'models': {
                'mcq': {'name': 'mock-t5', 'cache': False},
                'boolean': {'name': 'mock-bert', 'cache': False}
            },
            'processing': {
                'max_questions': 10,
                'min_quality_score': 0.5,
                'remove_duplicates': True
            },
            'export': {
                'default_format': 'json',
                'include_metadata': True
            },
            'testing': {
                'use_mocks': True,
                'mock_ai_responses': True,
                'skip_model_loading': True
            }
        }
