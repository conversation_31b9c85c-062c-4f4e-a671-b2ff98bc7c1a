"""
GPU Acceleration Support for QuizAIGen

This module provides comprehensive GPU acceleration capabilities for AI model
inference, including CUDA detection, device management, memory optimization,
and performance monitoring for GPU-accelerated operations.
"""

import os
import time
import threading
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging

from .logger import LoggerMixin

# Check for PyTorch availability
try:
    import torch
    import torch.cuda
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# Check for TensorFlow availability
try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

# Check for transformers availability
try:
    from transformers import pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False


class DeviceType(Enum):
    """Supported device types."""
    CPU = "cpu"
    CUDA = "cuda"
    MPS = "mps"  # Apple Metal Performance Shaders
    AUTO = "auto"


@dataclass
class GPUInfo:
    """GPU information structure."""
    device_id: int
    name: str
    total_memory: int  # in bytes
    free_memory: int   # in bytes
    used_memory: int   # in bytes
    compute_capability: Optional[Tuple[int, int]] = None
    temperature: Optional[float] = None
    utilization: Optional[float] = None
    power_usage: Optional[float] = None


@dataclass
class DeviceCapabilities:
    """Device capabilities information."""
    device_type: DeviceType
    device_count: int
    supports_fp16: bool = False
    supports_bf16: bool = False
    supports_int8: bool = False
    max_memory_gb: float = 0.0
    compute_capability: Optional[Tuple[int, int]] = None


@dataclass
class AccelerationConfig:
    """Configuration for GPU acceleration."""
    device: DeviceType = DeviceType.AUTO
    device_id: Optional[int] = None
    use_fp16: bool = False
    use_bf16: bool = False
    use_int8: bool = False
    batch_size: int = 8
    max_memory_fraction: float = 0.8
    enable_memory_growth: bool = True
    enable_mixed_precision: bool = False
    optimization_level: str = "O1"  # O0, O1, O2, O3


class GPUAccelerator(LoggerMixin):
    """Comprehensive GPU acceleration manager."""
    
    def __init__(self, config: Optional[AccelerationConfig] = None):
        """Initialize GPU accelerator."""
        super().__init__()
        
        self.config = config or AccelerationConfig()
        self.device_capabilities = None
        self.current_device = None
        self.gpu_info_cache = {}
        self.performance_stats = {
            'inference_times': [],
            'memory_usage': [],
            'gpu_utilization': []
        }
        
        # Initialize device detection
        self._detect_devices()
        self._select_optimal_device()
        
        self.log_info(f"GPUAccelerator initialized with device: {self.current_device}")
    
    def _detect_devices(self):
        """Detect available devices and their capabilities."""
        capabilities = DeviceCapabilities(
            device_type=DeviceType.CPU,
            device_count=1
        )
        
        # Check CUDA availability
        cuda_available = False
        if TORCH_AVAILABLE:
            try:
                if torch.cuda.is_available():
                    cuda_count = torch.cuda.device_count()
                    capabilities = DeviceCapabilities(
                        device_type=DeviceType.CUDA,
                        device_count=cuda_count,
                        supports_fp16=True,
                        supports_bf16=torch.cuda.is_bf16_supported() if hasattr(torch.cuda, 'is_bf16_supported') else False,
                        supports_int8=True,
                        max_memory_gb=torch.cuda.get_device_properties(0).total_memory / (1024**3) if cuda_count > 0 else 0.0,
                        compute_capability=torch.cuda.get_device_capability(0) if cuda_count > 0 else None
                    )
                    self.log_info(f"CUDA detected: {cuda_count} device(s)")

                    # Log GPU details
                    for i in range(cuda_count):
                        props = torch.cuda.get_device_properties(i)
                        self.log_info(f"GPU {i}: {props.name}, {props.total_memory / (1024**3):.1f}GB")
                    cuda_available = True
                else:
                    self.log_info("CUDA not available, using CPU")
            except Exception as e:
                self.log_warning(f"CUDA detection failed: {e}. Falling back to CPU")
                # Keep CPU capabilities as default

        # Check MPS availability (Apple Silicon) - only if CUDA is not available
        if not cuda_available and TORCH_AVAILABLE:
            try:
                if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                    capabilities = DeviceCapabilities(
                        device_type=DeviceType.MPS,
                        device_count=1,
                        supports_fp16=True,
                        supports_bf16=False,  # MPS doesn't support bfloat16
                        supports_int8=False,  # MPS has limited int8 support
                        max_memory_gb=8.0,    # Estimate for Apple Silicon
                        compute_capability=None
                    )
                    self.log_info("Apple MPS detected")
            except Exception as e:
                self.log_warning(f"MPS detection failed: {e}. Using CPU")

        # Check TensorFlow GPU - only if neither CUDA nor MPS is available
        if capabilities.device_type == DeviceType.CPU and TF_AVAILABLE:
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                capabilities = DeviceCapabilities(
                    device_type=DeviceType.CUDA,
                    device_count=len(gpus),
                    supports_fp16=True,
                    supports_bf16=True,
                    supports_int8=True
                )
                self.log_info(f"TensorFlow GPU detected: {len(gpus)} device(s)")
        
        self.device_capabilities = capabilities
    
    def _select_optimal_device(self):
        """Select the optimal device based on configuration and availability."""
        if self.config.device == DeviceType.AUTO:
            # Auto-select best available device
            if self.device_capabilities.device_type == DeviceType.CUDA:
                self.current_device = f"cuda:{self.config.device_id or 0}"
            elif self.device_capabilities.device_type == DeviceType.MPS:
                self.current_device = "mps"
            else:
                self.current_device = "cpu"
        else:
            # Use specified device
            if self.config.device == DeviceType.CUDA:
                if self.device_capabilities.device_type == DeviceType.CUDA:
                    device_id = self.config.device_id or 0
                    if device_id < self.device_capabilities.device_count:
                        self.current_device = f"cuda:{device_id}"
                    else:
                        self.log_warning(f"CUDA device {device_id} not available, falling back to CPU")
                        self.current_device = "cpu"
                else:
                    self.log_warning("CUDA not available, falling back to CPU")
                    self.current_device = "cpu"
            elif self.config.device == DeviceType.MPS:
                if self.device_capabilities.device_type == DeviceType.MPS:
                    self.current_device = "mps"
                else:
                    self.log_warning("MPS not available, falling back to CPU")
                    self.current_device = "cpu"
            else:
                self.current_device = "cpu"
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get comprehensive device information."""
        info = {
            'current_device': self.current_device,
            'device_type': self.device_capabilities.device_type.value,
            'device_count': self.device_capabilities.device_count,
            'capabilities': {
                'fp16': self.device_capabilities.supports_fp16,
                'bf16': self.device_capabilities.supports_bf16,
                'int8': self.device_capabilities.supports_int8,
                'max_memory_gb': self.device_capabilities.max_memory_gb,
                'compute_capability': self.device_capabilities.compute_capability
            }
        }
        
        # Add GPU-specific information
        if self.current_device.startswith('cuda') and TORCH_AVAILABLE:
            gpu_info = self._get_gpu_info()
            if gpu_info:
                info['gpu_info'] = gpu_info
        
        return info
    
    def _get_gpu_info(self) -> Optional[List[GPUInfo]]:
        """Get detailed GPU information."""
        if not TORCH_AVAILABLE or not torch.cuda.is_available():
            return None
        
        gpu_infos = []
        for i in range(torch.cuda.device_count()):
            try:
                props = torch.cuda.get_device_properties(i)
                
                # Get memory info
                torch.cuda.set_device(i)
                total_memory = torch.cuda.get_device_properties(i).total_memory
                free_memory = torch.cuda.memory_reserved(i)
                used_memory = total_memory - free_memory
                
                gpu_info = GPUInfo(
                    device_id=i,
                    name=props.name,
                    total_memory=total_memory,
                    free_memory=free_memory,
                    used_memory=used_memory,
                    compute_capability=(props.major, props.minor)
                )
                
                # Try to get additional info (may not be available on all systems)
                try:
                    import pynvml
                    pynvml.nvmlInit()
                    handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                    
                    # Temperature
                    try:
                        temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                        gpu_info.temperature = temp
                    except:
                        pass
                    
                    # Utilization
                    try:
                        util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                        gpu_info.utilization = util.gpu
                    except:
                        pass
                    
                    # Power usage
                    try:
                        power = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # Convert to watts
                        gpu_info.power_usage = power
                    except:
                        pass
                
                except ImportError:
                    # pynvml not available, skip additional metrics
                    pass
                except Exception as e:
                    self.log_warning(f"Failed to get additional GPU metrics: {e}")
                
                gpu_infos.append(gpu_info)
                
            except Exception as e:
                self.log_error(f"Failed to get info for GPU {i}: {e}")
        
        return gpu_infos
    
    def optimize_model_for_device(self, model, **kwargs):
        """Optimize model for the current device."""
        if not TORCH_AVAILABLE:
            self.log_warning("PyTorch not available, cannot optimize model")
            return model
        
        try:
            # Move model to device
            if hasattr(model, 'to'):
                model = model.to(self.current_device)
                self.log_info(f"Model moved to device: {self.current_device}")
            
            # Apply precision optimizations
            if self.config.use_fp16 and self.device_capabilities.supports_fp16:
                if hasattr(model, 'half'):
                    model = model.half()
                    self.log_info("Applied FP16 optimization")
            
            elif self.config.use_bf16 and self.device_capabilities.supports_bf16:
                if hasattr(model, 'bfloat16'):
                    model = model.bfloat16()
                    self.log_info("Applied BF16 optimization")
            
            # Enable mixed precision if supported
            if self.config.enable_mixed_precision and self.current_device.startswith('cuda'):
                try:
                    from torch.cuda.amp import autocast
                    self.log_info("Mixed precision training enabled")
                except ImportError:
                    self.log_warning("Mixed precision not available")
            
            # Compile model if available (PyTorch 2.0+)
            if hasattr(torch, 'compile') and self.current_device.startswith('cuda'):
                try:
                    model = torch.compile(model)
                    self.log_info("Model compiled for optimization")
                except Exception as e:
                    self.log_warning(f"Model compilation failed: {e}")
            
            return model
            
        except Exception as e:
            self.log_error(f"Model optimization failed: {e}")
            return model
    
    def create_optimized_pipeline(self, task: str, model_name: str, **kwargs):
        """Create an optimized transformers pipeline."""
        if not TRANSFORMERS_AVAILABLE:
            raise RuntimeError("Transformers library not available")
        
        # Configure device and precision
        device_map = None
        torch_dtype = None
        
        if self.current_device.startswith('cuda'):
            device_map = {"": int(self.current_device.split(':')[1])}
            
            if self.config.use_fp16:
                torch_dtype = torch.float16
            elif self.config.use_bf16:
                torch_dtype = torch.bfloat16
        
        elif self.current_device == 'mps':
            device_map = {"": "mps"}
        
        # Create pipeline with optimizations
        pipeline_kwargs = {
            'task': task,
            'model': model_name,
            'device_map': device_map,
            'torch_dtype': torch_dtype,
            **kwargs
        }
        
        # Remove None values
        pipeline_kwargs = {k: v for k, v in pipeline_kwargs.items() if v is not None}
        
        try:
            pipe = pipeline(**pipeline_kwargs)
            self.log_info(f"Created optimized pipeline for {task} on {self.current_device}")
            return pipe
        except Exception as e:
            self.log_error(f"Failed to create optimized pipeline: {e}")
            # Fallback to CPU
            return pipeline(task=task, model=model_name, device=-1)
    
    def benchmark_device(self, num_iterations: int = 10) -> Dict[str, float]:
        """Benchmark device performance."""
        if not TORCH_AVAILABLE:
            return {'error': 'PyTorch not available'}

        self.log_info(f"Benchmarking device: {self.current_device}")

        try:
            # Create test tensors
            size = (1000, 1000)
            device = torch.device(self.current_device)

            # Check if device is actually available
            if device.type == 'cuda' and not torch.cuda.is_available():
                return {'error': 'CUDA device requested but CUDA not available'}

            # Warm up
            for _ in range(3):
                a = torch.randn(size, device=device)
                b = torch.randn(size, device=device)
                c = torch.matmul(a, b)
                if device.type == 'cuda':
                    torch.cuda.synchronize()

            # Benchmark matrix multiplication
            times = []
            for _ in range(num_iterations):
                start_time = time.time()

                a = torch.randn(size, device=device)
                b = torch.randn(size, device=device)
                c = torch.matmul(a, b)

                if device.type == 'cuda':
                    torch.cuda.synchronize()

                end_time = time.time()
                times.append(end_time - start_time)

        except Exception as e:
            self.log_error(f"Benchmark failed: {e}")
            return {'error': f'Benchmark failed: {str(e)}'}
        
        # Calculate statistics
        avg_time = sum(times) / len(times) if times else 0.0
        min_time = min(times) if times else 0.0
        max_time = max(times) if times else 0.0

        # Calculate FLOPS (approximate)
        flops = 2 * size[0] * size[1] * size[1]  # Matrix multiplication FLOPS
        gflops = flops / (avg_time * 1e9) if avg_time > 0 else 0.0
        
        results = {
            'device': self.current_device,
            'avg_time_ms': avg_time * 1000,
            'min_time_ms': min_time * 1000,
            'max_time_ms': max_time * 1000,
            'gflops': gflops,
            'iterations': num_iterations
        }
        
        self.log_info(f"Benchmark results: {gflops:.2f} GFLOPS, {avg_time*1000:.2f}ms avg")
        
        return results
    
    def monitor_gpu_usage(self, duration: float = 60.0, interval: float = 1.0):
        """Monitor GPU usage for a specified duration."""
        if not self.current_device.startswith('cuda'):
            self.log_warning("GPU monitoring only available for CUDA devices")
            return
        
        self.log_info(f"Starting GPU monitoring for {duration}s")
        
        def monitor_loop():
            start_time = time.time()
            while time.time() - start_time < duration:
                gpu_info = self._get_gpu_info()
                if gpu_info:
                    for info in gpu_info:
                        self.performance_stats['memory_usage'].append(
                            info.used_memory / info.total_memory * 100
                        )
                        if info.utilization is not None:
                            self.performance_stats['gpu_utilization'].append(info.utilization)
                
                time.sleep(interval)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        
        return monitor_thread
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        stats = {
            'device': self.current_device,
            'inference_count': len(self.performance_stats['inference_times']),
            'avg_inference_time_ms': 0.0,
            'avg_memory_usage_percent': 0.0,
            'avg_gpu_utilization_percent': 0.0
        }
        
        if self.performance_stats['inference_times']:
            stats['avg_inference_time_ms'] = (
                sum(self.performance_stats['inference_times']) / 
                len(self.performance_stats['inference_times']) * 1000
            )
        
        if self.performance_stats['memory_usage']:
            stats['avg_memory_usage_percent'] = (
                sum(self.performance_stats['memory_usage']) / 
                len(self.performance_stats['memory_usage'])
            )
        
        if self.performance_stats['gpu_utilization']:
            stats['avg_gpu_utilization_percent'] = (
                sum(self.performance_stats['gpu_utilization']) / 
                len(self.performance_stats['gpu_utilization'])
            )
        
        return stats
    
    def clear_cache(self):
        """Clear GPU cache to free memory."""
        if TORCH_AVAILABLE and torch.cuda.is_available():
            torch.cuda.empty_cache()
            self.log_info("GPU cache cleared")
        
        if TF_AVAILABLE:
            try:
                tf.keras.backend.clear_session()
                self.log_info("TensorFlow session cleared")
            except:
                pass


# Global GPU accelerator instance
_global_accelerator = None


def get_gpu_accelerator(config: Optional[AccelerationConfig] = None) -> GPUAccelerator:
    """Get the global GPU accelerator instance."""
    global _global_accelerator
    if _global_accelerator is None:
        _global_accelerator = GPUAccelerator(config)
    return _global_accelerator


def gpu_accelerated(config: Optional[AccelerationConfig] = None):
    """Decorator to enable GPU acceleration for a function."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            accelerator = get_gpu_accelerator(config)
            
            # Record start time
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                
                # Record inference time
                inference_time = time.time() - start_time
                accelerator.performance_stats['inference_times'].append(inference_time)
                
                return result
            except Exception as e:
                accelerator.log_error(f"GPU-accelerated function {func.__name__} failed: {e}")
                raise
        
        return wrapper
    return decorator
