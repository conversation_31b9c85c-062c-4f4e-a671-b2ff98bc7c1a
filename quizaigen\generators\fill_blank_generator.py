"""
Enhanced Fill-in-the-Blank Question Generator

This module generates advanced fill-in-the-blank questions from input text
with AI-powered word selection, context analysis, and quality optimization.
Features T5 model integration, BERT-based context understanding, and intelligent
blank selection algorithms.
"""

import re
import random
from typing import List, Dict, Any, Optional, Tuple, Union
import numpy as np

from .base import BaseQuestionGenerator, Question
from ..utils.text_utils import extract_sentences, extract_keywords, clean_text
from ..utils.question_utils import calculate_question_confidence, select_unused_sentences, validate_question_quality
from ..models.t5_integration import T5QuestionGenerator, ModelTier
from ..models.base_model import ModelConfig
from ..core.exceptions import ProcessingError

try:
    from transformers import AutoTokenizer, AutoModel
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False


class EnhancedFillBlankGenerator(BaseQuestionGenerator):
    """
    Enhanced generator for fill-in-the-blank questions with AI integration.

    Features:
    - T5-based question optimization
    - BERT-based context understanding
    - Advanced blank selection algorithms
    - Quality scoring and validation
    - Multi-tier model support (Premium/Enterprise)
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None, tier: ModelTier = ModelTier.FREE):
        """
        Initialize Enhanced FillBlankGenerator.

        Args:
            config: Configuration dictionary
            tier: Model tier (FREE, PREMIUM, ENTERPRISE)
        """
        super().__init__(config)
        self.tier = tier

        # POS tags that are good candidates for blanks
        self.target_pos_tags = {
            'NOUN', 'PROPN',  # Nouns and proper nouns
            'VERB',           # Verbs
            'ADJ',            # Adjectives
            'NUM',            # Numbers
            'DATE'            # Dates
        }

        # Words to avoid making into blanks
        self.avoid_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'is', 'are',
            'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does',
            'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must'
        }

        # Minimum word length for blanks
        self.min_word_length = 3

        # AI Model Integration (Premium/Enterprise only)
        self.t5_generator = None
        self.bert_model = None
        self.bert_tokenizer = None
        self.spacy_nlp = None

        # Initialize AI models based on tier
        self._initialize_ai_models()

        self.log_info(f"Initialized Enhanced FillBlankGenerator (Tier: {tier.value})")

    def _initialize_ai_models(self):
        """Initialize AI models based on tier."""
        try:
            # T5 Integration for Premium/Enterprise
            if self.tier in [ModelTier.PREMIUM, ModelTier.ENTERPRISE]:
                if TRANSFORMERS_AVAILABLE:
                    self.t5_generator = T5QuestionGenerator(tier=self.tier)
                    self.log_info("T5 model initialized for fill-blank optimization")
                else:
                    self.log_warning("Transformers not available, T5 integration disabled")

            # BERT Integration for context understanding (Premium/Enterprise)
            if self.tier in [ModelTier.PREMIUM, ModelTier.ENTERPRISE]:
                if TRANSFORMERS_AVAILABLE:
                    self._initialize_bert_model()
                else:
                    self.log_warning("Transformers not available, BERT integration disabled")

            # spaCy for advanced NLP (All tiers)
            if SPACY_AVAILABLE:
                self._initialize_spacy_model()
            else:
                self.log_warning("spaCy not available, using basic NLP")

        except Exception as e:
            self.log_warning(f"AI model initialization failed: {str(e)}")

    def _initialize_bert_model(self):
        """Initialize BERT model for context understanding."""
        try:
            model_name = "bert-base-uncased"
            self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.bert_model = AutoModel.from_pretrained(model_name)
            self.bert_model.eval()
            self.log_info("BERT model initialized for context analysis")
        except Exception as e:
            self.log_warning(f"BERT initialization failed: {str(e)}")

    def _initialize_spacy_model(self):
        """Initialize spaCy model for NLP processing."""
        try:
            self.spacy_nlp = spacy.load("en_core_web_sm")
            self.log_info("spaCy model initialized")
        except OSError:
            try:
                # Try alternative model
                self.spacy_nlp = spacy.load("en_core_web_md")
                self.log_info("spaCy medium model initialized")
            except OSError:
                self.log_warning("spaCy models not available, using basic processing")

    def _get_question_type(self) -> str:
        """Get the question type identifier."""
        return "fill_blank"

    def _generate_questions_impl(self, text: str, num_questions: int, **kwargs) -> List[Question]:
        """
        Generate enhanced fill-in-the-blank questions from text with AI optimization.

        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters (use_ai_optimization, difficulty_level, etc.)

        Returns:
            List of enhanced fill-in-the-blank questions
        """
        use_ai_optimization = kwargs.get('use_ai_optimization', True)
        difficulty_level = kwargs.get('difficulty_level', 'medium')

        # Extract sentences with enhanced processing
        sentences = self._extract_enhanced_sentences(text)

        if not sentences:
            self.log_warning("No sentences found in text")
            return []

        # Filter and rank sentences by suitability
        suitable_sentences = self._filter_and_rank_sentences(sentences, difficulty_level)

        if not suitable_sentences:
            self.log_warning("No suitable sentences found for fill-in-blank questions")
            return []

        questions = []
        used_sentences = set()

        for _ in range(min(num_questions, len(suitable_sentences))):
            # Get available sentences (prioritize higher-ranked ones)
            available_sentences = [s for s in suitable_sentences if s['sentence'] not in used_sentences]
            if not available_sentences:
                break

            # Select best sentence based on ranking
            sentence_data = available_sentences[0]
            sentence = sentence_data['sentence']
            used_sentences.add(sentence)

            # Generate fill-in-blank question with AI enhancement
            if use_ai_optimization and self.tier in [ModelTier.PREMIUM, ModelTier.ENTERPRISE]:
                question_data = self._create_ai_enhanced_question(sentence, sentence_data, difficulty_level)
            else:
                question_data = self._create_fill_blank_question(sentence)

            if question_data:
                question = Question(
                    question=question_data['question'],
                    answer=question_data['answer'],
                    type='fill_blank',
                    confidence=question_data['confidence'],
                    metadata={
                        'original_sentence': sentence,
                        'blank_position': question_data['blank_position'],
                        'context': question_data.get('context', ''),
                        'difficulty': question_data.get('difficulty', difficulty_level),
                        'ai_enhanced': question_data.get('ai_enhanced', False),
                        'quality_score': question_data.get('quality_score', 0.5),
                        'blank_type': question_data.get('blank_type', 'unknown')
                    }
                )
                questions.append(question)

        # Post-process questions with AI optimization if available
        if use_ai_optimization and self.t5_generator and questions:
            questions = self._optimize_questions_with_t5(questions, text)

        self.log_info(f"Generated {len(questions)} enhanced fill-in-blank questions")
        return questions

    def _extract_enhanced_sentences(self, text: str) -> List[str]:
        """
        Extract sentences with enhanced processing using spaCy if available.

        Args:
            text: Input text

        Returns:
            List of processed sentences
        """
        if self.spacy_nlp:
            # Use spaCy for better sentence segmentation
            doc = self.spacy_nlp(text)
            sentences = [sent.text.strip() for sent in doc.sents if len(sent.text.strip()) > 20]
        else:
            # Fallback to basic sentence extraction
            sentences = extract_sentences(text)

        return sentences

    def _filter_and_rank_sentences(self, sentences: List[str], difficulty_level: str) -> List[Dict[str, Any]]:
        """
        Filter and rank sentences by suitability for fill-in-blank questions.

        Args:
            sentences: List of sentences
            difficulty_level: Target difficulty level

        Returns:
            List of sentence data dictionaries sorted by suitability score
        """
        sentence_data = []

        for sentence in sentences:
            # Basic filtering
            word_count = len(sentence.split())
            char_count = len(sentence)

            # Skip sentences that are too short or too long
            if word_count < 8 or word_count > 25 or char_count < 50 or char_count > 300:
                continue

            # Calculate suitability score
            score = self._calculate_sentence_suitability(sentence, difficulty_level)

            if score > 0.3:  # Minimum threshold
                sentence_data.append({
                    'sentence': sentence,
                    'score': score,
                    'word_count': word_count,
                    'char_count': char_count
                })

        # Sort by score (highest first)
        sentence_data.sort(key=lambda x: x['score'], reverse=True)
        return sentence_data

    def _calculate_sentence_suitability(self, sentence: str, difficulty_level: str) -> float:
        """
        Calculate how suitable a sentence is for fill-in-blank questions.

        Args:
            sentence: Input sentence
            difficulty_level: Target difficulty level

        Returns:
            Suitability score (0.0 to 1.0)
        """
        score = 0.5  # Base score

        # Word count factor
        word_count = len(sentence.split())
        if 10 <= word_count <= 20:
            score += 0.2
        elif 8 <= word_count <= 25:
            score += 0.1

        # Check for good blank candidates
        candidates = self._find_blank_candidates(sentence)
        if candidates:
            score += min(0.3, len(candidates) * 0.1)

        # Complexity factor based on difficulty level
        if difficulty_level == 'easy':
            # Prefer simpler sentences
            if word_count <= 15:
                score += 0.1
        elif difficulty_level == 'hard':
            # Prefer more complex sentences
            if word_count >= 15:
                score += 0.1

        # Penalize sentences with too many punctuation marks
        punct_count = sum(1 for c in sentence if c in '.,;:!?')
        if punct_count > 3:
            score -= 0.1

        return min(1.0, max(0.0, score))

    def _create_ai_enhanced_question(self, sentence: str, sentence_data: Dict[str, Any],
                                   difficulty_level: str) -> Optional[Dict[str, Any]]:
        """
        Create AI-enhanced fill-in-blank question using advanced algorithms.

        Args:
            sentence: Input sentence
            sentence_data: Sentence metadata
            difficulty_level: Target difficulty level

        Returns:
            Enhanced question data dictionary
        """
        # Use BERT for context understanding if available
        if self.bert_model and self.bert_tokenizer:
            candidates = self._find_bert_enhanced_candidates(sentence)
        else:
            candidates = self._find_blank_candidates(sentence)

        if not candidates:
            return self._create_fill_blank_question(sentence)

        # Select best candidate using AI scoring
        best_candidate = self._select_ai_enhanced_candidate(candidates, sentence, difficulty_level)

        if not best_candidate:
            return self._create_fill_blank_question(sentence)

        word, start_pos, end_pos = best_candidate

        # Create enhanced question
        question_text = sentence[:start_pos] + "______" + sentence[end_pos:]
        question_text = re.sub(r'\s+', ' ', question_text).strip()

        # Calculate enhanced confidence and quality scores
        confidence = self._calculate_ai_enhanced_confidence(word, sentence, sentence_data)
        quality_score = self._calculate_question_quality(word, sentence, question_text)
        blank_type = self._classify_blank_type(word, sentence)

        return {
            'question': question_text,
            'answer': word,
            'blank_position': start_pos,
            'confidence': confidence,
            'difficulty': difficulty_level,
            'context': sentence,
            'ai_enhanced': True,
            'quality_score': quality_score,
            'blank_type': blank_type
        }

    def _create_fill_blank_question(self, sentence: str) -> Optional[Dict[str, Any]]:
        """
        Create a fill-in-blank question from a sentence.
        
        Args:
            sentence: Input sentence
        
        Returns:
            Question data dictionary or None if no suitable blank found
        """
        # Find potential blank candidates
        candidates = self._find_blank_candidates(sentence)
        
        if not candidates:
            return None
        
        # Select the best candidate
        best_candidate = self._select_best_candidate(candidates, sentence)
        
        if not best_candidate:
            return None
        
        word, start_pos, end_pos = best_candidate
        
        # Create the question with blank
        question_text = sentence[:start_pos] + "______" + sentence[end_pos:]
        
        # Clean up extra spaces
        question_text = re.sub(r'\s+', ' ', question_text).strip()
        
        # Calculate confidence based on word characteristics
        confidence = self._calculate_blank_confidence(word, sentence)
        
        # Determine difficulty
        difficulty = self._determine_difficulty(word, sentence)
        
        return {
            'question': question_text,
            'answer': word,
            'blank_position': start_pos,
            'confidence': confidence,
            'difficulty': difficulty,
            'context': sentence
        }
    
    def _find_blank_candidates(self, sentence: str) -> List[Tuple[str, int, int]]:
        """
        Find potential words that can be made into blanks.
        
        Args:
            sentence: Input sentence
        
        Returns:
            List of (word, start_position, end_position) tuples
        """
        candidates = []
        
        # Simple word extraction with positions
        words = re.finditer(r'\b\w+\b', sentence)
        
        for match in words:
            word = match.group().lower()
            start_pos = match.start()
            end_pos = match.end()
            
            # Check if word is a good candidate
            if self._is_good_blank_candidate(word, sentence):
                candidates.append((match.group(), start_pos, end_pos))
        
        return candidates
    
    def _is_good_blank_candidate(self, word: str, sentence: str) -> bool:
        """
        Check if a word is a good candidate for a blank.
        
        Args:
            word: Word to check
            sentence: Full sentence context
        
        Returns:
            True if word is a good candidate
        """
        word_lower = word.lower()
        
        # Check minimum length
        if len(word) < self.min_word_length:
            return False
        
        # Avoid common words
        if word_lower in self.avoid_words:
            return False
        
        # Avoid words that are too common
        if word_lower in ['this', 'that', 'these', 'those', 'here', 'there', 'when', 'where']:
            return False
        
        # Prefer nouns, verbs, adjectives, and numbers
        if self._looks_like_noun(word, sentence):
            return True
        
        if self._looks_like_verb(word, sentence):
            return True
        
        if self._looks_like_adjective(word, sentence):
            return True
        
        if self._looks_like_number_or_date(word):
            return True
        
        # Check if it's a proper noun (capitalized)
        if word[0].isupper() and not sentence.strip().startswith(word):
            return True
        
        return False
    
    def _looks_like_noun(self, word: str, sentence: str) -> bool:
        """Check if word appears to be a noun based on context."""
        word_lower = word.lower()
        
        # Common noun patterns
        if word_lower.endswith(('tion', 'sion', 'ment', 'ness', 'ity', 'er', 'or', 'ist')):
            return True
        
        # Check if preceded by articles or adjectives
        word_pattern = r'\b(?:the|a|an|this|that|some|many|few|several|most|all)\s+\w*\s*' + re.escape(word) + r'\b'
        if re.search(word_pattern, sentence, re.IGNORECASE):
            return True
        
        return False
    
    def _looks_like_verb(self, word: str, sentence: str) -> bool:
        """Check if word appears to be a verb based on context."""
        word_lower = word.lower()
        
        # Common verb patterns
        if word_lower.endswith(('ed', 'ing', 'es', 's')):
            return True
        
        # Check if it's in a verb position (after subject)
        # This is a simple heuristic
        words = sentence.split()
        try:
            word_index = words.index(word)
            if word_index > 0 and word_index < len(words) - 1:
                prev_word = words[word_index - 1].lower()
                if prev_word in ['i', 'you', 'he', 'she', 'it', 'we', 'they']:
                    return True
        except ValueError:
            pass
        
        return False
    
    def _looks_like_adjective(self, word: str, sentence: str) -> bool:
        """Check if word appears to be an adjective based on context."""
        word_lower = word.lower()
        
        # Common adjective patterns
        if word_lower.endswith(('ful', 'less', 'ous', 'ive', 'able', 'ible', 'al', 'ic')):
            return True
        
        # Check if it's before a noun
        word_pattern = re.escape(word) + r'\s+\w+'
        if re.search(word_pattern, sentence, re.IGNORECASE):
            return True
        
        return False
    
    def _looks_like_number_or_date(self, word: str) -> bool:
        """Check if word is a number or date."""
        # Check for numbers
        if word.isdigit():
            return True
        
        # Check for years
        if len(word) == 4 and word.isdigit() and 1800 <= int(word) <= 2100:
            return True
        
        # Check for months
        months = ['january', 'february', 'march', 'april', 'may', 'june',
                 'july', 'august', 'september', 'october', 'november', 'december']
        if word.lower() in months:
            return True
        
        return False
    
    def _select_best_candidate(self, candidates: List[Tuple[str, int, int]], 
                             sentence: str) -> Optional[Tuple[str, int, int]]:
        """
        Select the best candidate for a blank from the list.
        
        Args:
            candidates: List of candidate words with positions
            sentence: Original sentence
        
        Returns:
            Best candidate tuple or None
        """
        if not candidates:
            return None
        
        # Score each candidate
        scored_candidates = []
        
        for word, start_pos, end_pos in candidates:
            score = self._score_candidate(word, sentence, start_pos)
            scored_candidates.append((score, word, start_pos, end_pos))
        
        # Sort by score (highest first)
        scored_candidates.sort(reverse=True)
        
        # Return the best candidate
        _, word, start_pos, end_pos = scored_candidates[0]
        return (word, start_pos, end_pos)
    
    def _score_candidate(self, word: str, sentence: str, position: int) -> float:
        """
        Score a candidate word for blank suitability.
        
        Args:
            word: Candidate word
            sentence: Full sentence
            position: Position of word in sentence
        
        Returns:
            Score (higher is better)
        """
        score = 0.0
        word_lower = word.lower()
        
        # Length bonus (longer words are generally better)
        score += len(word) * 0.1
        
        # Position bonus (middle of sentence is better)
        sentence_length = len(sentence)
        relative_position = position / sentence_length
        if 0.2 <= relative_position <= 0.8:
            score += 1.0
        
        # Content word bonus
        if self._looks_like_noun(word, sentence):
            score += 2.0
        elif self._looks_like_verb(word, sentence):
            score += 1.5
        elif self._looks_like_adjective(word, sentence):
            score += 1.0
        
        # Proper noun bonus
        if word[0].isupper() and not sentence.strip().startswith(word):
            score += 1.5
        
        # Number/date bonus
        if self._looks_like_number_or_date(word):
            score += 1.0
        
        # Avoid very common words
        common_words = ['said', 'says', 'made', 'make', 'get', 'got', 'put', 'take', 'took']
        if word_lower in common_words:
            score -= 1.0
        
        return score
    
    def _calculate_blank_confidence(self, word: str, sentence: str) -> float:
        """
        Calculate confidence score for a blank question.
        
        Args:
            word: The word that was blanked
            sentence: Original sentence
        
        Returns:
            Confidence score between 0 and 1
        """
        confidence = 0.5  # Base confidence
        
        # Length factor
        if len(word) >= 5:
            confidence += 0.1
        
        # Content type factor
        if self._looks_like_noun(word, sentence):
            confidence += 0.2
        elif self._looks_like_verb(word, sentence):
            confidence += 0.15
        
        # Proper noun factor
        if word[0].isupper():
            confidence += 0.1
        
        # Sentence quality factor
        if 10 <= len(sentence.split()) <= 20:
            confidence += 0.1
        
        return min(0.95, confidence)

    def _find_bert_enhanced_candidates(self, sentence: str) -> List[Tuple[str, int, int]]:
        """
        Find blank candidates using BERT-based context understanding.

        Args:
            sentence: Input sentence

        Returns:
            List of enhanced candidate tuples
        """
        if not self.bert_model or not self.bert_tokenizer:
            return self._find_blank_candidates(sentence)

        try:
            # Tokenize sentence
            inputs = self.bert_tokenizer(sentence, return_tensors="pt", padding=True, truncation=True)

            with torch.no_grad():
                outputs = self.bert_model(**inputs)
                hidden_states = outputs.last_hidden_state[0]  # [seq_len, hidden_size]

            # Get token positions and scores
            tokens = self.bert_tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
            candidates = []

            # Find word boundaries and calculate importance scores
            current_word = ""
            word_start = 0
            word_tokens = []

            for i, token in enumerate(tokens):
                if token.startswith('##'):
                    # Continuation of current word
                    current_word += token[2:]
                    word_tokens.append(i)
                elif token in ['[CLS]', '[SEP]', '[PAD]']:
                    # Special tokens, skip
                    continue
                else:
                    # Process previous word if exists
                    if current_word and self._is_good_blank_candidate(current_word, sentence):
                        # Calculate importance score using BERT embeddings
                        word_embeddings = hidden_states[word_tokens]
                        importance_score = torch.norm(word_embeddings, dim=-1).mean().item()

                        # Find actual position in original sentence
                        start_pos, end_pos = self._find_word_position(current_word, sentence, word_start)
                        if start_pos != -1:
                            candidates.append((current_word, start_pos, end_pos, importance_score))

                    # Start new word
                    current_word = token
                    word_start = i
                    word_tokens = [i]

            # Sort by importance score and return top candidates
            candidates.sort(key=lambda x: x[3], reverse=True)
            return [(word, start, end) for word, start, end, _ in candidates[:10]]

        except Exception as e:
            self.log_warning(f"BERT candidate finding failed: {str(e)}")
            return self._find_blank_candidates(sentence)

    def _find_word_position(self, word: str, sentence: str, approximate_pos: int) -> Tuple[int, int]:
        """
        Find the actual position of a word in the sentence.

        Args:
            word: Word to find
            sentence: Original sentence
            approximate_pos: Approximate position hint

        Returns:
            Tuple of (start_position, end_position) or (-1, -1) if not found
        """
        # Simple word boundary search
        pattern = r'\b' + re.escape(word) + r'\b'
        matches = list(re.finditer(pattern, sentence, re.IGNORECASE))

        if matches:
            # Return first match for simplicity
            match = matches[0]
            return match.start(), match.end()

        return -1, -1

    def _select_ai_enhanced_candidate(self, candidates: List[Tuple[str, int, int]],
                                    sentence: str, difficulty_level: str) -> Optional[Tuple[str, int, int]]:
        """
        Select best candidate using AI-enhanced scoring.

        Args:
            candidates: List of candidate tuples
            sentence: Original sentence
            difficulty_level: Target difficulty level

        Returns:
            Best candidate tuple or None
        """
        if not candidates:
            return None

        scored_candidates = []

        for word, start_pos, end_pos in candidates:
            # Calculate comprehensive score
            base_score = self._score_candidate(word, sentence, start_pos)

            # Add AI-specific scoring factors
            context_score = self._calculate_context_importance(word, sentence, start_pos)
            difficulty_score = self._calculate_difficulty_alignment(word, difficulty_level)

            total_score = base_score * 0.5 + context_score * 0.3 + difficulty_score * 0.2
            scored_candidates.append((total_score, word, start_pos, end_pos))

        # Sort by total score
        scored_candidates.sort(reverse=True)

        # Return best candidate
        _, word, start_pos, end_pos = scored_candidates[0]
        return (word, start_pos, end_pos)

    def _calculate_context_importance(self, word: str, sentence: str, position: int) -> float:
        """
        Calculate how important a word is in its context.

        Args:
            word: Target word
            sentence: Full sentence
            position: Word position

        Returns:
            Context importance score (0.0 to 1.0)
        """
        score = 0.5

        # Check if word is a key concept
        if self._is_key_concept(word, sentence):
            score += 0.3

        # Check position in sentence (middle words often more important)
        words = sentence.split()
        word_index = len(sentence[:position].split())
        relative_position = word_index / len(words) if words else 0.5

        if 0.2 <= relative_position <= 0.8:  # Middle of sentence
            score += 0.2

        return min(1.0, score)

    def _calculate_difficulty_alignment(self, word: str, difficulty_level: str) -> float:
        """
        Calculate how well a word aligns with target difficulty.

        Args:
            word: Target word
            difficulty_level: Target difficulty

        Returns:
            Difficulty alignment score (0.0 to 1.0)
        """
        word_difficulty = self._estimate_word_difficulty(word)

        if difficulty_level == 'easy':
            return 1.0 if word_difficulty <= 0.4 else 0.5
        elif difficulty_level == 'medium':
            return 1.0 if 0.3 <= word_difficulty <= 0.7 else 0.7
        elif difficulty_level == 'hard':
            return 1.0 if word_difficulty >= 0.6 else 0.5

        return 0.5

    def _estimate_word_difficulty(self, word: str) -> float:
        """
        Estimate word difficulty based on various factors.

        Args:
            word: Target word

        Returns:
            Difficulty estimate (0.0 = easy, 1.0 = hard)
        """
        difficulty = 0.3  # Base difficulty

        # Length factor
        if len(word) > 8:
            difficulty += 0.2
        elif len(word) > 12:
            difficulty += 0.3

        # Complexity patterns
        if any(suffix in word.lower() for suffix in ['tion', 'sion', 'ment', 'ness', 'ity']):
            difficulty += 0.2

        # Capital letters (proper nouns, acronyms)
        if word.isupper() or (word[0].isupper() and len(word) > 1):
            difficulty += 0.1

        return min(1.0, difficulty)

    def _calculate_ai_enhanced_confidence(self, word: str, sentence: str,
                                        sentence_data: Dict[str, Any]) -> float:
        """
        Calculate enhanced confidence score using AI insights.

        Args:
            word: Blanked word
            sentence: Original sentence
            sentence_data: Sentence metadata

        Returns:
            Enhanced confidence score
        """
        base_confidence = self._calculate_blank_confidence(word, sentence)

        # Add AI-specific confidence factors
        context_factor = self._calculate_context_importance(word, sentence, 0) * 0.2
        quality_factor = sentence_data.get('score', 0.5) * 0.1

        enhanced_confidence = base_confidence + context_factor + quality_factor
        return min(0.98, enhanced_confidence)

    def _calculate_question_quality(self, word: str, sentence: str, question_text: str) -> float:
        """
        Calculate overall question quality score.

        Args:
            word: Answer word
            sentence: Original sentence
            question_text: Generated question

        Returns:
            Quality score (0.0 to 1.0)
        """
        quality = 0.5  # Base quality

        # Answer quality factors
        if len(word) >= 4:
            quality += 0.1
        if self._is_key_concept(word, sentence):
            quality += 0.2

        # Question clarity factors
        if len(question_text.split()) >= 8:
            quality += 0.1
        if question_text.count('______') == 1:
            quality += 0.1

        return min(1.0, quality)

    def _classify_blank_type(self, word: str, sentence: str) -> str:
        """
        Classify the type of blank based on the word.

        Args:
            word: Blanked word
            sentence: Original sentence

        Returns:
            Blank type classification
        """
        if self._looks_like_noun(word, sentence):
            return 'noun'
        elif self._looks_like_verb(word, sentence):
            return 'verb'
        elif self._looks_like_adjective(word, sentence):
            return 'adjective'
        elif word.isdigit():
            return 'number'
        elif word[0].isupper():
            return 'proper_noun'
        else:
            return 'other'

    def _is_key_concept(self, word: str, sentence: str) -> bool:
        """
        Determine if a word represents a key concept in the sentence.

        Args:
            word: Target word
            sentence: Full sentence

        Returns:
            True if word is a key concept
        """
        # Use spaCy for better concept detection if available
        if self.spacy_nlp:
            try:
                doc = self.spacy_nlp(sentence)
                for token in doc:
                    if token.text.lower() == word.lower():
                        # Check if it's a named entity or important POS
                        if token.ent_type_ or token.pos_ in ['NOUN', 'PROPN', 'VERB']:
                            return True
                        break
            except Exception:
                pass

        # Fallback to simple heuristics
        return (len(word) >= 5 and
                not word.lower() in self.avoid_words and
                (self._looks_like_noun(word, sentence) or
                 self._looks_like_verb(word, sentence)))

    def _optimize_questions_with_t5(self, questions: List[Question], context: str) -> List[Question]:
        """
        Optimize questions using T5 model.

        Args:
            questions: List of questions to optimize
            context: Original text context

        Returns:
            List of optimized questions
        """
        if not self.t5_generator:
            return questions

        optimized_questions = []

        for question in questions:
            try:
                # Use T5 to generate improved version
                result = self.t5_generator.generate_fill_blank_question(
                    question.metadata.get('original_sentence', question.question)
                )

                if result.confidence > question.confidence:
                    # Create optimized question
                    optimized = Question(
                        question=result.text,
                        answer=question.answer,
                        type='fill_blank',
                        confidence=result.confidence,
                        metadata={
                            **question.metadata,
                            "t5_optimized": True,
                            "original_question": question.question
                        }
                    )
                    optimized_questions.append(optimized)
                else:
                    optimized_questions.append(question)

            except Exception as e:
                self.log_warning(f"T5 optimization failed for question: {str(e)}")
                optimized_questions.append(question)

        return optimized_questions

    def _determine_difficulty(self, word: str, sentence: str) -> str:
        """
        Determine the difficulty level of a fill-in-blank question.

        Args:
            word: The word that was blanked
            sentence: Original sentence

        Returns:
            Difficulty level ('easy', 'medium', 'hard')
        """
        # Simple heuristics for difficulty
        word_length = len(word)
        sentence_complexity = len(sentence.split())

        if word_length <= 4 and sentence_complexity <= 12:
            return 'easy'
        elif word_length >= 8 or sentence_complexity >= 20:
            return 'hard'
        else:
            return 'medium'


# Compatibility alias for existing code
FillBlankGenerator = EnhancedFillBlankGenerator
