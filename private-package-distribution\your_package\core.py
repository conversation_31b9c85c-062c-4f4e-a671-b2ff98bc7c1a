"""
Core Package Functionality
==========================

Main business logic and core features of the commercial package.
"""

from typing import Any, Dict, List, Optional, Union
import logging
from .license import require_license, get_license_validator, LicenseError


logger = logging.getLogger(__name__)


class YourPackageCore:
    """
    Core functionality class with license-protected features.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize core package functionality.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.validator = get_license_validator()
        
        # Validate license on initialization
        if not self.validator.validate():
            logger.warning("Package initialized without valid license")
    
    @require_license()
    def basic_feature(self, data: Any) -> Dict[str, Any]:
        """
        Basic feature available to all licensed users.
        
        Args:
            data: Input data to process
            
        Returns:
            Dict containing processed results
        """
        logger.info("Executing basic feature")
        
        # Your core business logic here
        result = {
            "status": "success",
            "processed_data": f"Processed: {data}",
            "timestamp": "2024-01-01T00:00:00Z",
            "license_info": self.validator.get_license_info()
        }
        
        return result
    
    @require_license(feature="advanced")
    def advanced_feature(self, data: Any, options: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Advanced feature requiring specific license tier.
        
        Args:
            data: Input data to process
            options: Additional processing options
            
        Returns:
            Dict containing advanced processing results
        """
        logger.info("Executing advanced feature")
        
        options = options or {}
        
        # Advanced business logic here
        result = {
            "status": "success",
            "advanced_processing": True,
            "processed_data": f"Advanced processing of: {data}",
            "options_applied": options,
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        return result
    
    @require_license(feature="enterprise")
    def enterprise_feature(self, data: List[Any]) -> Dict[str, Any]:
        """
        Enterprise-only feature for bulk processing.
        
        Args:
            data: List of data items to process
            
        Returns:
            Dict containing bulk processing results
        """
        logger.info("Executing enterprise feature")
        
        # Enterprise business logic here
        processed_items = []
        for item in data:
            processed_items.append(f"Enterprise processed: {item}")
        
        result = {
            "status": "success",
            "enterprise_processing": True,
            "total_items": len(data),
            "processed_items": processed_items,
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        return result
    
    def get_available_features(self) -> List[str]:
        """
        Get list of features available with current license.
        
        Returns:
            List of available feature names
        """
        if not self.validator.validate():
            return []
        
        license_info = self.validator.get_license_info()
        return license_info.get("features", [])
    
    def get_license_status(self) -> Dict[str, Any]:
        """
        Get current license status and information.
        
        Returns:
            Dict containing license status details
        """
        return self.validator.get_license_info()
    
    def check_feature_access(self, feature: str) -> bool:
        """
        Check if specific feature is accessible with current license.
        
        Args:
            feature: Feature name to check
            
        Returns:
            bool: True if feature is accessible
        """
        return self.validator.has_feature(feature)


class LimitedAPI:
    """
    Limited API for unlicensed or trial usage.
    """
    
    def __init__(self):
        """Initialize limited API."""
        self.call_count = 0
        self.max_calls = 100  # Trial limit
    
    def limited_feature(self, data: Any) -> Dict[str, Any]:
        """
        Limited feature with usage restrictions.
        
        Args:
            data: Input data to process
            
        Returns:
            Dict containing limited processing results
        """
        self.call_count += 1
        
        if self.call_count > self.max_calls:
            raise LicenseError(
                f"Trial limit exceeded ({self.max_calls} calls). "
                "Please purchase a license to continue."
            )
        
        result = {
            "status": "trial",
            "processed_data": f"Trial processing: {data}",
            "calls_remaining": self.max_calls - self.call_count,
            "message": "This is a trial version. Purchase license for full features."
        }
        
        return result
    
    def get_trial_status(self) -> Dict[str, Any]:
        """
        Get trial usage status.
        
        Returns:
            Dict containing trial status information
        """
        return {
            "trial_active": True,
            "calls_used": self.call_count,
            "calls_remaining": max(0, self.max_calls - self.call_count),
            "max_calls": self.max_calls
        }


# Factory function for creating appropriate API instance
def create_api_instance(force_trial: bool = False) -> Union[YourPackageCore, LimitedAPI]:
    """
    Create appropriate API instance based on license status.
    
    Args:
        force_trial: Force creation of trial instance
        
    Returns:
        Either full API or limited trial API instance
    """
    if force_trial:
        return LimitedAPI()
    
    try:
        validator = get_license_validator()
        if validator.validate():
            return YourPackageCore()
        else:
            logger.warning("No valid license found, using trial API")
            return LimitedAPI()
    except Exception as e:
        logger.error(f"Error validating license: {e}")
        return LimitedAPI()


# Convenience functions for common operations
def process_data(data: Any, **kwargs) -> Dict[str, Any]:
    """
    Convenience function for data processing.
    
    Args:
        data: Data to process
        **kwargs: Additional options
        
    Returns:
        Processing results
    """
    api = create_api_instance()
    
    if isinstance(api, YourPackageCore):
        return api.basic_feature(data)
    else:
        return api.limited_feature(data)


def get_package_status() -> Dict[str, Any]:
    """
    Get overall package status including license and features.
    
    Returns:
        Dict containing package status information
    """
    api = create_api_instance()
    
    if isinstance(api, YourPackageCore):
        return {
            "package_type": "licensed",
            "license_status": api.get_license_status(),
            "available_features": api.get_available_features()
        }
    else:
        return {
            "package_type": "trial",
            "trial_status": api.get_trial_status()
        }
