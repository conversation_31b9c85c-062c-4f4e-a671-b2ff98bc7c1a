"""
<PERSON>'s Taxonomy Classifier for QuizAIGen

Provides AI-powered classification of questions according to <PERSON>'s taxonomy levels.
Helps educators understand the cognitive complexity of generated questions.
"""

from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
import re

from ..generators.base import Question
from ..models.base_model import ModelTier
from ..core.exceptions import ProcessingError, LicenseError
from ..utils.logger import LoggerMixin


class BloomLevel(Enum):
    """<PERSON>'s taxonomy levels."""
    REMEMBER = "remember"
    UNDERSTAND = "understand"
    APPLY = "apply"
    ANALYZE = "analyze"
    EVALUATE = "evaluate"
    CREATE = "create"


@dataclass
class BloomClassification:
    """Result of <PERSON>'s taxonomy classification."""
    level: BloomLevel
    confidence: float  # 0.0 to 1.0
    secondary_levels: List[BloomLevel]  # Alternative possible levels
    reasoning: str
    keywords_found: List[str]  # Keywords that influenced classification
    
    def to_dict(self) -> Dict:
        """Convert to dictionary format."""
        return {
            'level': self.level.value,
            'confidence': self.confidence,
            'secondary_levels': [level.value for level in self.secondary_levels],
            'reasoning': self.reasoning,
            'keywords_found': self.keywords_found
        }


class BloomClassifier(LoggerMixin):
    """
    AI-powered Bloom's taxonomy classifier.
    
    Classifies questions according to Bloom's taxonomy levels based on:
    - Action verbs and keywords
    - Question structure and complexity
    - Required cognitive processes
    - Answer format and expectations
    """
    
    def __init__(self, tier: ModelTier = ModelTier.FREE):
        """
        Initialize Bloom's taxonomy classifier.
        
        Args:
            tier: License tier for feature access
        """
        super().__init__()
        self.tier = tier
        
        # Bloom's taxonomy keywords for each level
        self.bloom_keywords = {
            BloomLevel.REMEMBER: [
                'define', 'identify', 'list', 'name', 'recall', 'recognize',
                'remember', 'retrieve', 'state', 'describe', 'match',
                'select', 'label', 'locate', 'memorize', 'repeat',
                'what', 'when', 'where', 'who', 'which'
            ],
            BloomLevel.UNDERSTAND: [
                'explain', 'interpret', 'summarize', 'paraphrase', 'classify',
                'compare', 'contrast', 'demonstrate', 'illustrate', 'translate',
                'convert', 'defend', 'distinguish', 'estimate', 'extend',
                'infer', 'predict', 'rewrite', 'show', 'why', 'how'
            ],
            BloomLevel.APPLY: [
                'apply', 'calculate', 'complete', 'demonstrate', 'examine',
                'illustrate', 'implement', 'modify', 'operate', 'practice',
                'predict', 'prepare', 'produce', 'relate', 'schedule',
                'sketch', 'solve', 'use', 'utilize', 'employ'
            ],
            BloomLevel.ANALYZE: [
                'analyze', 'break down', 'categorize', 'compare', 'contrast',
                'deconstruct', 'differentiate', 'discriminate', 'distinguish',
                'examine', 'experiment', 'identify', 'illustrate', 'infer',
                'outline', 'relate', 'select', 'separate', 'subdivide'
            ],
            BloomLevel.EVALUATE: [
                'appraise', 'argue', 'assess', 'choose', 'compare', 'conclude',
                'critique', 'decide', 'defend', 'determine', 'discriminate',
                'evaluate', 'judge', 'justify', 'measure', 'prioritize',
                'prove', 'rank', 'rate', 'recommend', 'select', 'support',
                'test', 'validate', 'value', 'weigh'
            ],
            BloomLevel.CREATE: [
                'assemble', 'build', 'compose', 'construct', 'create', 'design',
                'develop', 'devise', 'formulate', 'generate', 'hypothesize',
                'invent', 'make', 'originate', 'plan', 'produce', 'propose',
                'reconstruct', 'reorganize', 'revise', 'rewrite', 'synthesize',
                'write', 'combine', 'compile', 'integrate'
            ]
        }
        
        # Question type patterns that suggest specific Bloom levels
        self.question_patterns = {
            BloomLevel.REMEMBER: [
                r'^what is\b', r'^who is\b', r'^when did\b', r'^where is\b',
                r'^define\b', r'^list\b', r'^name\b', r'^identify\b'
            ],
            BloomLevel.UNDERSTAND: [
                r'^explain why\b', r'^how does\b', r'^what does.*mean\b',
                r'^summarize\b', r'^describe\b', r'^interpret\b'
            ],
            BloomLevel.APPLY: [
                r'^calculate\b', r'^solve\b', r'^use.*to\b', r'^apply\b',
                r'^demonstrate\b', r'^show how\b'
            ],
            BloomLevel.ANALYZE: [
                r'^analyze\b', r'^compare.*with\b', r'^what are the differences\b',
                r'^break down\b', r'^examine\b', r'^categorize\b'
            ],
            BloomLevel.EVALUATE: [
                r'^evaluate\b', r'^assess\b', r'^judge\b', r'^critique\b',
                r'^which is better\b', r'^justify\b', r'^argue\b'
            ],
            BloomLevel.CREATE: [
                r'^create\b', r'^design\b', r'^develop\b', r'^propose\b',
                r'^construct\b', r'^formulate\b', r'^generate\b'
            ]
        }
        
        # Confidence weights for different classification methods
        self.classification_weights = {
            'keyword_match': 0.4,
            'pattern_match': 0.3,
            'question_type': 0.2,
            'context_analysis': 0.1
        }
        
        self.log_info(f"Initialized BloomClassifier with tier: {tier.value}")
    
    def classify_question(self, question: Question, context: str = "") -> BloomClassification:
        """
        Classify a question according to Bloom's taxonomy.
        
        Args:
            question: Question to classify
            context: Optional context for classification
            
        Returns:
            BloomClassification with level and confidence
            
        Raises:
            ProcessingError: If classification fails
        """
        try:
            self.log_debug(f"Classifying Bloom level for question: {question.question[:50]}...")
            
            # Analyze question text
            question_text = question.question.lower()
            
            # Find keyword matches
            keyword_scores = self._analyze_keywords(question_text)
            
            # Find pattern matches
            pattern_scores = self._analyze_patterns(question_text)
            
            # Analyze question type
            type_scores = self._analyze_question_type(question)
            
            # Advanced context analysis for Premium/Enterprise
            context_scores = {}
            if self.tier in [ModelTier.PREMIUM, ModelTier.ENTERPRISE] and context:
                context_scores = self._analyze_context(question_text, context)
            
            # Combine scores
            combined_scores = self._combine_scores(
                keyword_scores, pattern_scores, type_scores, context_scores
            )
            
            # Determine primary and secondary levels
            primary_level = max(combined_scores.items(), key=lambda x: x[1])[0]
            
            # Find secondary levels (scores within 0.2 of primary)
            primary_score = combined_scores[primary_level]
            secondary_levels = [
                level for level, score in combined_scores.items()
                if level != primary_level and score >= primary_score - 0.2
            ]
            
            # Calculate confidence
            confidence = self._calculate_confidence(combined_scores, primary_score)
            
            # Find keywords that influenced classification
            keywords_found = self._get_influencing_keywords(question_text, primary_level)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(
                primary_level, combined_scores, keywords_found
            )
            
            classification = BloomClassification(
                level=primary_level,
                confidence=confidence,
                secondary_levels=secondary_levels,
                reasoning=reasoning,
                keywords_found=keywords_found
            )
            
            self.log_info(f"Classified as {primary_level.value} level "
                         f"(confidence: {confidence:.2f})")
            return classification
            
        except Exception as e:
            self.log_error(f"Bloom classification failed: {str(e)}")
            raise ProcessingError(
                stage="bloom_classification",
                message=f"Failed to classify question: {str(e)}"
            )
    
    def classify_questions_batch(self, questions: List[Question], 
                               context: str = "") -> List[BloomClassification]:
        """
        Classify multiple questions according to Bloom's taxonomy.
        
        Args:
            questions: List of questions to classify
            context: Optional context for classification
            
        Returns:
            List of Bloom classifications
        """
        classifications = []
        
        for question in questions:
            try:
                classification = self.classify_question(question, context)
                classifications.append(classification)
            except Exception as e:
                self.log_warning(f"Failed to classify question: {str(e)}")
                # Create default classification for failed cases
                default_classification = BloomClassification(
                    level=BloomLevel.UNDERSTAND,  # Default to understand level
                    confidence=0.0,
                    secondary_levels=[],
                    reasoning="Classification failed - using default understand level",
                    keywords_found=[]
                )
                classifications.append(default_classification)
        
        return classifications
    
    def _analyze_keywords(self, question_text: str) -> Dict[BloomLevel, float]:
        """Analyze keywords to determine Bloom level scores."""
        scores = {level: 0.0 for level in BloomLevel}
        
        for level, keywords in self.bloom_keywords.items():
            matches = 0
            for keyword in keywords:
                if re.search(r'\b' + re.escape(keyword) + r'\b', question_text):
                    matches += 1
            
            # Normalize score by number of keywords for this level
            if keywords:
                scores[level] = matches / len(keywords)
        
        return scores
    
    def _analyze_patterns(self, question_text: str) -> Dict[BloomLevel, float]:
        """Analyze question patterns to determine Bloom level scores."""
        scores = {level: 0.0 for level in BloomLevel}
        
        for level, patterns in self.question_patterns.items():
            for pattern in patterns:
                if re.search(pattern, question_text):
                    scores[level] = 1.0  # Strong indicator
                    break
        
        return scores

    def _analyze_question_type(self, question: Question) -> Dict[BloomLevel, float]:
        """Analyze question type to determine likely Bloom level."""
        scores = {level: 0.0 for level in BloomLevel}

        # Question type tendencies
        type_tendencies = {
            'boolean': BloomLevel.REMEMBER,      # Usually factual recall
            'mcq': BloomLevel.UNDERSTAND,        # Often comprehension
            'short_answer': BloomLevel.APPLY,    # Application of knowledge
            'fill_blank': BloomLevel.REMEMBER,   # Usually recall
            'essay': BloomLevel.ANALYZE,         # Higher-order thinking
            'faq': BloomLevel.UNDERSTAND         # Explanation/comprehension
        }

        if question.type in type_tendencies:
            primary_level = type_tendencies[question.type]
            scores[primary_level] = 0.5  # Moderate confidence from type alone

        return scores

    def _analyze_context(self, question_text: str, context: str) -> Dict[BloomLevel, float]:
        """
        Advanced context analysis for Premium/Enterprise tiers.

        This would use BERT/AI models for sophisticated analysis.
        For now, provides rule-based approximation.
        """
        if self.tier == ModelTier.FREE:
            return {level: 0.0 for level in BloomLevel}

        scores = {level: 0.0 for level in BloomLevel}

        # Basic context analysis
        context_lower = context.lower()

        # Look for complexity indicators in context
        if any(word in context_lower for word in ['theory', 'concept', 'principle']):
            scores[BloomLevel.UNDERSTAND] += 0.2

        if any(word in context_lower for word in ['problem', 'solution', 'method']):
            scores[BloomLevel.APPLY] += 0.2

        if any(word in context_lower for word in ['analysis', 'comparison', 'relationship']):
            scores[BloomLevel.ANALYZE] += 0.2

        return scores

    def _combine_scores(self, keyword_scores: Dict[BloomLevel, float],
                       pattern_scores: Dict[BloomLevel, float],
                       type_scores: Dict[BloomLevel, float],
                       context_scores: Dict[BloomLevel, float]) -> Dict[BloomLevel, float]:
        """Combine different scoring methods with weights."""
        combined_scores = {level: 0.0 for level in BloomLevel}

        for level in BloomLevel:
            combined_scores[level] = (
                keyword_scores[level] * self.classification_weights['keyword_match'] +
                pattern_scores[level] * self.classification_weights['pattern_match'] +
                type_scores[level] * self.classification_weights['question_type'] +
                context_scores.get(level, 0.0) * self.classification_weights['context_analysis']
            )

        return combined_scores

    def _calculate_confidence(self, scores: Dict[BloomLevel, float],
                            primary_score: float) -> float:
        """Calculate confidence in the classification."""
        if primary_score == 0.0:
            return 0.1  # Very low confidence if no indicators found

        # Calculate score spread (difference between highest and second highest)
        sorted_scores = sorted(scores.values(), reverse=True)
        if len(sorted_scores) > 1:
            score_spread = sorted_scores[0] - sorted_scores[1]
        else:
            score_spread = primary_score

        # Higher spread = higher confidence
        confidence = min(0.5 + (score_spread * 2), 1.0)

        return confidence

    def _get_influencing_keywords(self, question_text: str, level: BloomLevel) -> List[str]:
        """Get keywords that influenced the classification."""
        keywords_found = []

        if level in self.bloom_keywords:
            for keyword in self.bloom_keywords[level]:
                if re.search(r'\b' + re.escape(keyword) + r'\b', question_text):
                    keywords_found.append(keyword)

        return keywords_found[:5]  # Limit to top 5 keywords

    def _generate_reasoning(self, level: BloomLevel,
                          scores: Dict[BloomLevel, float],
                          keywords: List[str]) -> str:
        """Generate human-readable reasoning for the classification."""
        reasoning = f"Classified as {level.value} level based on "

        reasons = []

        if keywords:
            keyword_text = ", ".join(keywords[:3])  # Show top 3 keywords
            reasons.append(f"keywords: {keyword_text}")

        # Find other significant scores
        other_levels = [l for l, s in scores.items() if l != level and s > 0.2]
        if other_levels:
            level_names = [l.value for l in other_levels[:2]]  # Show top 2
            reasons.append(f"secondary indicators: {', '.join(level_names)}")

        if reasons:
            reasoning += "; ".join(reasons) + "."
        else:
            reasoning += "question structure and content analysis."

        return reasoning

    def get_bloom_distribution(self, questions: List[Question]) -> Dict[str, int]:
        """
        Get distribution of Bloom levels across questions.

        Args:
            questions: List of questions to analyze

        Returns:
            Dictionary with Bloom level counts
        """
        classifications = self.classify_questions_batch(questions)

        distribution = {level.value: 0 for level in BloomLevel}

        for classification in classifications:
            distribution[classification.level.value] += 1

        return distribution

    def filter_by_bloom_level(self, questions: List[Question],
                            target_levels: List[BloomLevel]) -> List[Question]:
        """
        Filter questions by target Bloom taxonomy levels.

        Args:
            questions: List of questions to filter
            target_levels: List of desired Bloom levels

        Returns:
            Filtered list of questions
        """
        classifications = self.classify_questions_batch(questions)

        filtered_questions = []
        for question, classification in zip(questions, classifications):
            if classification.level in target_levels:
                # Add Bloom metadata to question
                if not hasattr(question, 'metadata'):
                    question.metadata = {}
                question.metadata['bloom_taxonomy'] = classification.to_dict()
                filtered_questions.append(question)

        self.log_info(f"Filtered {len(filtered_questions)} questions from {len(questions)} "
                     f"based on Bloom taxonomy levels")
        return filtered_questions
