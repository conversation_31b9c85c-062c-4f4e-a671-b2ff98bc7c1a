"""
Tests for GPU Acceleration Utilities

Tests the GPU acceleration functionality including device detection,
model optimization, performance monitoring, and acceleration configurations.
"""

import unittest
import time
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from quizaigen.utils.gpu_accelerator import (
    DeviceType, GPUInfo, DeviceCapabilities, AccelerationConfig,
    GPUAccelerator, get_gpu_accelerator, gpu_accelerated
)


class TestDeviceType(unittest.TestCase):
    """Test device type enumeration."""
    
    def test_device_types(self):
        """Test device type values."""
        self.assertEqual(DeviceType.CPU.value, "cpu")
        self.assertEqual(DeviceType.CUDA.value, "cuda")
        self.assertEqual(DeviceType.MPS.value, "mps")
        self.assertEqual(DeviceType.AUTO.value, "auto")


class TestGPUInfo(unittest.TestCase):
    """Test GPU information structure."""
    
    def test_gpu_info_creation(self):
        """Test creating GPU info."""
        gpu_info = GPUInfo(
            device_id=0,
            name="Test GPU",
            total_memory=8589934592,  # 8GB
            free_memory=4294967296,   # 4GB
            used_memory=4294967296,   # 4GB
            compute_capability=(7, 5),
            temperature=65.0,
            utilization=80.0,
            power_usage=150.0
        )
        
        self.assertEqual(gpu_info.device_id, 0)
        self.assertEqual(gpu_info.name, "Test GPU")
        self.assertEqual(gpu_info.total_memory, 8589934592)
        self.assertEqual(gpu_info.compute_capability, (7, 5))
        self.assertEqual(gpu_info.temperature, 65.0)


class TestDeviceCapabilities(unittest.TestCase):
    """Test device capabilities structure."""
    
    def test_device_capabilities_creation(self):
        """Test creating device capabilities."""
        capabilities = DeviceCapabilities(
            device_type=DeviceType.CUDA,
            device_count=2,
            supports_fp16=True,
            supports_bf16=True,
            supports_int8=True,
            max_memory_gb=16.0,
            compute_capability=(8, 6)
        )
        
        self.assertEqual(capabilities.device_type, DeviceType.CUDA)
        self.assertEqual(capabilities.device_count, 2)
        self.assertTrue(capabilities.supports_fp16)
        self.assertTrue(capabilities.supports_bf16)
        self.assertEqual(capabilities.max_memory_gb, 16.0)


class TestAccelerationConfig(unittest.TestCase):
    """Test acceleration configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = AccelerationConfig()
        
        self.assertEqual(config.device, DeviceType.AUTO)
        self.assertIsNone(config.device_id)
        self.assertFalse(config.use_fp16)
        self.assertFalse(config.use_bf16)
        self.assertFalse(config.use_int8)
        self.assertEqual(config.batch_size, 8)
        self.assertEqual(config.max_memory_fraction, 0.8)
        self.assertTrue(config.enable_memory_growth)
        self.assertFalse(config.enable_mixed_precision)
        self.assertEqual(config.optimization_level, "O1")
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = AccelerationConfig(
            device=DeviceType.CUDA,
            device_id=1,
            use_fp16=True,
            batch_size=16,
            enable_mixed_precision=True,
            optimization_level="O2"
        )
        
        self.assertEqual(config.device, DeviceType.CUDA)
        self.assertEqual(config.device_id, 1)
        self.assertTrue(config.use_fp16)
        self.assertEqual(config.batch_size, 16)
        self.assertTrue(config.enable_mixed_precision)
        self.assertEqual(config.optimization_level, "O2")


class TestGPUAccelerator(unittest.TestCase):
    """Test GPU accelerator functionality."""
    
    def setUp(self):
        """Set up test accelerator."""
        self.config = AccelerationConfig(device=DeviceType.CPU)
        self.accelerator = GPUAccelerator(self.config)
    
    def test_accelerator_creation(self):
        """Test accelerator creation."""
        self.assertIsInstance(self.accelerator.config, AccelerationConfig)
        self.assertIsNotNone(self.accelerator.device_capabilities)
        self.assertIsNotNone(self.accelerator.current_device)
        self.assertEqual(self.accelerator.current_device, "cpu")
    
    @patch('quizaigen.utils.gpu_accelerator.TORCH_AVAILABLE', False)
    def test_cpu_fallback_no_torch(self):
        """Test CPU fallback when PyTorch is not available."""
        accelerator = GPUAccelerator()
        
        self.assertEqual(accelerator.current_device, "cpu")
        self.assertEqual(accelerator.device_capabilities.device_type, DeviceType.CPU)
    
    def test_get_device_info(self):
        """Test getting device information."""
        info = self.accelerator.get_device_info()
        
        self.assertIn('current_device', info)
        self.assertIn('device_type', info)
        self.assertIn('device_count', info)
        self.assertIn('capabilities', info)
        
        capabilities = info['capabilities']
        self.assertIn('fp16', capabilities)
        self.assertIn('bf16', capabilities)
        self.assertIn('int8', capabilities)
        self.assertIn('max_memory_gb', capabilities)
    
    @patch('quizaigen.utils.gpu_accelerator.TORCH_AVAILABLE', True)
    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.device_count', return_value=2)
    @patch('torch.cuda.get_device_properties')
    @patch('torch.cuda.is_bf16_supported', return_value=True)
    def test_cuda_detection(self, mock_bf16, mock_props, mock_count, mock_available):
        """Test CUDA device detection."""
        # Mock GPU properties
        mock_prop = Mock()
        mock_prop.name = "Test GPU"
        mock_prop.total_memory = 8589934592
        mock_props.return_value = mock_prop

        with patch('torch.cuda.get_device_capability', return_value=(7, 5)):
            accelerator = GPUAccelerator(AccelerationConfig(device=DeviceType.AUTO))

            self.assertEqual(accelerator.device_capabilities.device_type, DeviceType.CUDA)
            self.assertEqual(accelerator.device_capabilities.device_count, 2)
            self.assertTrue(accelerator.current_device.startswith('cuda'))
    
    @patch('quizaigen.utils.gpu_accelerator.TORCH_AVAILABLE', True)
    @patch('torch.cuda.is_available', return_value=False)
    @patch('torch.backends.mps.is_available', return_value=True)
    def test_mps_detection(self, mock_mps, mock_cuda):
        """Test MPS device detection."""
        with patch('torch.backends', create=True) as mock_backends:
            mock_backends.mps.is_available.return_value = True
            
            accelerator = GPUAccelerator(AccelerationConfig(device=DeviceType.AUTO))
            
            # Should detect MPS when CUDA is not available
            self.assertEqual(accelerator.device_capabilities.device_type, DeviceType.MPS)
            self.assertEqual(accelerator.current_device, "mps")
    
    def test_optimize_model_no_torch(self):
        """Test model optimization without PyTorch."""
        mock_model = Mock()
        
        with patch('quizaigen.utils.gpu_accelerator.TORCH_AVAILABLE', False):
            result = self.accelerator.optimize_model_for_device(mock_model)
            
            # Should return original model unchanged
            self.assertIs(result, mock_model)
    
    @patch('quizaigen.utils.gpu_accelerator.TORCH_AVAILABLE', True)
    def test_optimize_model_with_torch(self):
        """Test model optimization with PyTorch."""
        mock_model = Mock()
        mock_model.to.return_value = mock_model
        mock_model.half.return_value = mock_model
        
        config = AccelerationConfig(use_fp16=True)
        accelerator = GPUAccelerator(config)
        accelerator.device_capabilities.supports_fp16 = True
        
        result = accelerator.optimize_model_for_device(mock_model)
        
        # Should call to() and half() methods
        mock_model.to.assert_called_once()
        mock_model.half.assert_called_once()
        self.assertIs(result, mock_model)
    
    def test_create_optimized_pipeline_no_transformers(self):
        """Test pipeline creation without transformers."""
        with patch('quizaigen.utils.gpu_accelerator.TRANSFORMERS_AVAILABLE', False):
            with self.assertRaises(RuntimeError):
                self.accelerator.create_optimized_pipeline("text-generation", "test-model")
    
    @patch('quizaigen.utils.gpu_accelerator.TRANSFORMERS_AVAILABLE', True)
    @patch('quizaigen.utils.gpu_accelerator.pipeline')
    def test_create_optimized_pipeline_with_transformers(self, mock_pipeline):
        """Test pipeline creation with transformers."""
        mock_pipe = Mock()
        mock_pipeline.return_value = mock_pipe
        
        result = self.accelerator.create_optimized_pipeline("text-generation", "test-model")
        
        mock_pipeline.assert_called()
        self.assertIs(result, mock_pipe)
    
    @patch('quizaigen.utils.gpu_accelerator.TORCH_AVAILABLE', False)
    def test_benchmark_device_no_torch(self):
        """Test device benchmark without PyTorch."""
        result = self.accelerator.benchmark_device()
        
        self.assertIn('error', result)
        self.assertEqual(result['error'], 'PyTorch not available')
    
    @patch('quizaigen.utils.gpu_accelerator.TORCH_AVAILABLE', True)
    @patch('torch.randn')
    @patch('torch.matmul')
    @patch('torch.device')
    def test_benchmark_device_with_torch(self, mock_device, mock_matmul, mock_randn):
        """Test device benchmark with PyTorch."""
        # Mock torch operations
        mock_tensor = Mock()
        mock_randn.return_value = mock_tensor
        mock_matmul.return_value = mock_tensor
        mock_device.return_value = Mock(type='cpu')
        
        result = self.accelerator.benchmark_device(num_iterations=2)
        
        self.assertIn('device', result)
        self.assertIn('avg_time_ms', result)
        self.assertIn('gflops', result)
        self.assertIn('iterations', result)
        self.assertEqual(result['iterations'], 2)
    
    def test_monitor_gpu_usage_non_cuda(self):
        """Test GPU monitoring on non-CUDA device."""
        # Should not start monitoring for non-CUDA devices
        thread = self.accelerator.monitor_gpu_usage(duration=1.0)
        
        # Should return None or not start monitoring
        self.assertIsNone(thread)
    
    def test_get_performance_stats(self):
        """Test getting performance statistics."""
        # Add some mock data
        self.accelerator.performance_stats['inference_times'] = [0.1, 0.2, 0.15]
        self.accelerator.performance_stats['memory_usage'] = [50.0, 60.0, 55.0]
        self.accelerator.performance_stats['gpu_utilization'] = [70.0, 80.0, 75.0]
        
        stats = self.accelerator.get_performance_stats()
        
        self.assertIn('device', stats)
        self.assertIn('inference_count', stats)
        self.assertIn('avg_inference_time_ms', stats)
        self.assertIn('avg_memory_usage_percent', stats)
        self.assertIn('avg_gpu_utilization_percent', stats)
        
        self.assertEqual(stats['inference_count'], 3)
        self.assertAlmostEqual(stats['avg_inference_time_ms'], 150.0, places=1)
        self.assertAlmostEqual(stats['avg_memory_usage_percent'], 55.0, places=1)
        self.assertAlmostEqual(stats['avg_gpu_utilization_percent'], 75.0, places=1)
    
    @patch('quizaigen.utils.gpu_accelerator.TORCH_AVAILABLE', True)
    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.empty_cache')
    def test_clear_cache_cuda(self, mock_empty_cache, mock_available):
        """Test clearing CUDA cache."""
        self.accelerator.clear_cache()
        
        mock_empty_cache.assert_called_once()
    
    @patch('quizaigen.utils.gpu_accelerator.TF_AVAILABLE', True)
    @patch('tensorflow.keras.backend.clear_session')
    def test_clear_cache_tensorflow(self, mock_clear_session):
        """Test clearing TensorFlow session."""
        self.accelerator.clear_cache()
        
        mock_clear_session.assert_called_once()


class TestGlobalAccelerator(unittest.TestCase):
    """Test global accelerator functionality."""
    
    def test_get_global_accelerator(self):
        """Test getting global accelerator instance."""
        accelerator1 = get_gpu_accelerator()
        accelerator2 = get_gpu_accelerator()
        
        # Should return the same instance
        self.assertIs(accelerator1, accelerator2)
        self.assertIsInstance(accelerator1, GPUAccelerator)
    
    def test_get_global_accelerator_with_config(self):
        """Test getting global accelerator with custom config."""
        config = AccelerationConfig(device=DeviceType.CPU)
        accelerator = get_gpu_accelerator(config)
        
        self.assertIsInstance(accelerator, GPUAccelerator)
        # Note: Global instance is created once, so config might not be applied
        # if instance already exists


class TestGPUAcceleratedDecorator(unittest.TestCase):
    """Test GPU acceleration decorator."""
    
    def test_decorator_basic_functionality(self):
        """Test that decorator doesn't break function execution."""
        
        @gpu_accelerated()
        def test_function(x, y):
            return x + y
        
        result = test_function(2, 3)
        self.assertEqual(result, 5)
    
    def test_decorator_with_exception(self):
        """Test decorator behavior when function raises exception."""
        
        @gpu_accelerated()
        def failing_function():
            raise ValueError("Test error")
        
        with self.assertRaises(ValueError):
            failing_function()
    
    def test_decorator_records_performance(self):
        """Test that decorator records performance statistics."""
        accelerator = get_gpu_accelerator()
        initial_count = len(accelerator.performance_stats['inference_times'])
        
        @gpu_accelerated()
        def test_function():
            time.sleep(0.01)  # Small delay
            return "result"
        
        result = test_function()
        
        self.assertEqual(result, "result")
        final_count = len(accelerator.performance_stats['inference_times'])
        self.assertEqual(final_count, initial_count + 1)
        
        # Check that time was recorded
        last_time = accelerator.performance_stats['inference_times'][-1]
        self.assertGreater(last_time, 0.005)  # Should be at least 5ms


class TestIntegration(unittest.TestCase):
    """Integration tests for GPU acceleration."""
    
    def test_end_to_end_cpu_workflow(self):
        """Test end-to-end workflow on CPU."""
        config = AccelerationConfig(device=DeviceType.CPU, batch_size=2)
        accelerator = GPUAccelerator(config)
        
        # Get device info
        info = accelerator.get_device_info()
        self.assertEqual(info['current_device'], 'cpu')
        
        # Run benchmark
        benchmark = accelerator.benchmark_device(num_iterations=2)
        if 'error' not in benchmark:
            self.assertIn('gflops', benchmark)
        
        # Get performance stats
        stats = accelerator.get_performance_stats()
        self.assertIn('device', stats)
        
        # Clear cache (should not fail)
        accelerator.clear_cache()
    
    def test_configuration_validation(self):
        """Test configuration validation and fallbacks."""
        # Test invalid CUDA device fallback
        config = AccelerationConfig(device=DeviceType.CUDA, device_id=999)
        accelerator = GPUAccelerator(config)
        
        # Should fallback to CPU if CUDA not available or invalid device
        self.assertTrue(accelerator.current_device in ['cpu', 'cuda:0', 'cuda:999'])
    
    def test_multiple_accelerators(self):
        """Test creating multiple accelerator instances."""
        config1 = AccelerationConfig(device=DeviceType.CPU)
        config2 = AccelerationConfig(device=DeviceType.AUTO)
        
        accelerator1 = GPUAccelerator(config1)
        accelerator2 = GPUAccelerator(config2)
        
        # Should be separate instances
        self.assertIsNot(accelerator1, accelerator2)
        
        # Both should work independently
        info1 = accelerator1.get_device_info()
        info2 = accelerator2.get_device_info()
        
        self.assertIn('current_device', info1)
        self.assertIn('current_device', info2)


if __name__ == '__main__':
    unittest.main()
