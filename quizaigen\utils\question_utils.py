"""QuizAIGen Question Generation Utilities

This module provides common utilities for question generation across different generators.
"""

import re
import random
from typing import List, Dict, Any, Optional, Tuple

from .text_utils import extract_keywords, extract_sentences


def extract_common_entities(text: str) -> Dict[str, List[str]]:
    """
    Extract named entities from text using pattern matching.
    
    Args:
        text: Input text
        
    Returns:
        Dictionary of entity types and their values
    """
    entities = {
        'persons': [],
        'places': [],
        'organizations': [],
        'dates': [],
        'numbers': []
    }
    
    # Person names (comprehensive pattern for various name formats)
    person_patterns = [
        r'\b[A-Z][a-z]+(?:[-\s][A-Z][a-z]+)*\s+[A-Z][a-z]+\b',  # Hyphenated and compound names
        r'\b[A-Z]\.?[A-Z]\.?[A-Z]\.\s+[A-Z][a-z]+\b',  # Initials like J.R.R. Tolkien
        r'\b[A-Z][a-z]+\s+[a-z]{2,3}\s+[A-Z][a-z]+\b',  # Names with lowercase middle (van, de, da, etc.)
    ]
    all_persons = []
    for pattern in person_patterns:
        all_persons.extend(re.findall(pattern, text))
    entities['persons'] = list(set(all_persons))

    # Places (words ending with common place suffixes or known places)
    place_pattern = r'\b[A-Z][a-z]*(?:town|city|state|country|land|burg|ville)\b'
    entities['places'] = list(set(re.findall(place_pattern, text)))

    # Organizations (words with Corp, Inc, Ltd, etc.)
    org_pattern = r'\b[A-Z][a-zA-Z\s]*(?:Corp|Inc|Ltd|Company|Organization|Institute)\b'
    entities['organizations'] = list(set(re.findall(org_pattern, text)))

    # Dates (various date formats including just years)
    date_pattern = r'\b(?:\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{4}|\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4})\b'
    entities['dates'] = list(set(re.findall(date_pattern, text)))

    # Numbers
    number_pattern = r'\b\d+(?:\.\d+)?\b'
    entities['numbers'] = list(set(re.findall(number_pattern, text)))
    
    return entities


def calculate_question_confidence(sentence: str, question_type: str, 
                                target_element: Optional[str] = None) -> float:
    """
    Calculate confidence score for a generated question.
    
    Args:
        sentence: Source sentence
        question_type: Type of question (mcq, boolean, short_answer, etc.)
        target_element: Target element (keyword, entity, etc.)
        
    Returns:
        Confidence score between 0.0 and 1.0
    """
    base_confidence = 0.5
    
    # Sentence length factor
    word_count = len(sentence.split())
    if 10 <= word_count <= 30:
        base_confidence += 0.2
    elif word_count < 5:
        base_confidence -= 0.3
    elif word_count > 50:
        base_confidence -= 0.1
    
    # Sentence structure factor
    if sentence.count(',') <= 2:  # Not overly complex
        base_confidence += 0.1
    
    # Target element factor
    if target_element and len(target_element) > 2:
        base_confidence += 0.1
    
    # Question type specific adjustments
    if question_type == 'boolean':
        # Boolean questions are generally easier to validate
        base_confidence += 0.1
    elif question_type == 'mcq':
        # MCQ depends on quality of distractors
        base_confidence += 0.05
    
    return min(max(base_confidence, 0.0), 1.0)


def select_unused_sentences(sentences: List[str], used_sentences: set, 
                          min_length: int = 10, max_length: int = 200) -> List[str]:
    """
    Select sentences that haven't been used and meet length criteria.
    
    Args:
        sentences: List of all sentences
        used_sentences: Set of already used sentences
        min_length: Minimum sentence length
        max_length: Maximum sentence length
        
    Returns:
        List of available sentences
    """
    available = []
    for sentence in sentences:
        if (sentence not in used_sentences and 
            min_length <= len(sentence) <= max_length and
            len(sentence.split()) >= 5):
            available.append(sentence)
    return available


def generate_simple_distractors(target_word: str, word_type: str = 'general') -> List[str]:
    """
    Generate simple distractors for multiple choice questions.
    
    Args:
        target_word: The correct answer
        word_type: Type of word (noun, verb, adjective, etc.)
        
    Returns:
        List of distractor words
    """
    # Common distractor pools by type
    distractor_pools = {
        'noun': ['concept', 'element', 'factor', 'aspect', 'component', 'feature', 'property'],
        'verb': ['process', 'action', 'function', 'operation', 'method', 'procedure', 'technique'],
        'adjective': ['important', 'significant', 'major', 'primary', 'essential', 'critical', 'fundamental'],
        'general': ['item', 'thing', 'object', 'entity', 'unit', 'part', 'piece']
    }
    
    pool = distractor_pools.get(word_type, distractor_pools['general'])
    
    # Filter out words too similar to target
    distractors = [word for word in pool if word.lower() != target_word.lower()]
    
    # Add some variations of the target word
    if len(target_word) > 4:
        variations = [
            target_word + 's',
            target_word + 'ing',
            target_word + 'ed',
            'non-' + target_word
        ]
        distractors.extend([v for v in variations if v != target_word])
    
    return random.sample(distractors, min(len(distractors), 5))


def clean_question_text(text: str) -> str:
    """
    Clean and format question text.
    
    Args:
        text: Raw question text
        
    Returns:
        Cleaned question text
    """
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Ensure proper capitalization
    if text and not text[0].isupper():
        text = text[0].upper() + text[1:]
    
    # Ensure proper punctuation
    if text and text[-1] not in '.?!':
        if any(word in text.lower() for word in ['what', 'who', 'when', 'where', 'why', 'how']):
            text += '?'
        else:
            text += '.'
    
    return text


def validate_question_quality(question_text: str, answer: str, 
                            question_type: str) -> bool:
    """
    Validate the quality of a generated question.
    
    Args:
        question_text: The question text
        answer: The answer
        question_type: Type of question
        
    Returns:
        True if question meets quality standards
    """
    # Basic validation
    if not question_text or not answer:
        return False
    
    # Length validation
    if len(question_text) < 10 or len(question_text) > 500:
        return False
    
    if len(answer) < 1 or len(answer) > 200:
        return False
    
    # Question format validation
    if question_type in ['short_answer', 'faq']:
        if not any(word in question_text.lower() for word in ['what', 'who', 'when', 'where', 'why', 'how']):
            return False
    
    # Avoid questions that are too similar to answers
    question_words = set(question_text.lower().split())
    answer_words = set(answer.lower().split())
    overlap = len(question_words & answer_words)
    
    if overlap > len(answer_words) * 0.7:  # Too much overlap
        return False
    
    return True