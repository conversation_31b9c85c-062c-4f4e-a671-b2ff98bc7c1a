"""
Performance Monitor

This module provides comprehensive performance monitoring and optimization
tools for the QuizAIGen library.
"""

import time
import threading
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
from multiprocessing import cpu_count
import json
import os

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    cpu_usage_start: Optional[float] = None
    cpu_usage_end: Optional[float] = None
    memory_usage_start: Optional[int] = None
    memory_usage_end: Optional[int] = None
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def finalize(self):
        """Finalize metrics calculation."""
        if self.end_time is None:
            self.end_time = time.time()
        
        if self.duration is None:
            self.duration = self.end_time - self.start_time
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'operation_name': self.operation_name,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'cpu_usage_start': self.cpu_usage_start,
            'cpu_usage_end': self.cpu_usage_end,
            'memory_usage_start': self.memory_usage_start,
            'memory_usage_end': self.memory_usage_end,
            'success': self.success,
            'error_message': self.error_message,
            'metadata': self.metadata
        }


@dataclass
class SystemResources:
    """System resource information."""
    cpu_count: int
    cpu_usage: float
    memory_total: int
    memory_available: int
    memory_usage_percent: float
    disk_usage_percent: float
    load_average: Optional[float] = None
    
    @classmethod
    def get_current(cls) -> 'SystemResources':
        """Get current system resources."""
        if not PSUTIL_AVAILABLE:
            return cls(
                cpu_count=cpu_count(),
                cpu_usage=0.0,
                memory_total=0,
                memory_available=0,
                memory_usage_percent=0.0,
                disk_usage_percent=0.0
            )
        
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return cls(
            cpu_count=psutil.cpu_count(),
            cpu_usage=psutil.cpu_percent(interval=0.1),
            memory_total=memory.total,
            memory_available=memory.available,
            memory_usage_percent=memory.percent,
            disk_usage_percent=disk.percent,
            load_average=os.getloadavg()[0] if hasattr(os, 'getloadavg') else None
        )


class PerformanceMonitor:
    """Comprehensive performance monitoring system."""
    
    def __init__(self, max_history: int = 1000):
        """
        Initialize performance monitor.
        
        Args:
            max_history: Maximum number of metrics to keep in history
        """
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        self.active_operations: Dict[str, PerformanceMetrics] = {}
        self.operation_stats: Dict[str, List[float]] = defaultdict(list)
        self.lock = threading.Lock()
        
        # Performance thresholds
        self.cpu_threshold = 80.0  # CPU usage percentage
        self.memory_threshold = 85.0  # Memory usage percentage
        self.duration_threshold = 30.0  # Operation duration in seconds
        
        # Adaptive settings
        self.adaptive_enabled = True
        self.min_workers = 1
        self.max_workers = cpu_count() * 2
        self.current_optimal_workers = cpu_count()
    
    def start_operation(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Start monitoring an operation.
        
        Args:
            operation_name: Name of the operation
            metadata: Additional metadata
        
        Returns:
            Operation ID for tracking
        """
        operation_id = f"{operation_name}_{int(time.time() * 1000000)}"
        
        # Get system resources
        resources = SystemResources.get_current()
        
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            start_time=time.time(),
            cpu_usage_start=resources.cpu_usage,
            memory_usage_start=resources.memory_total - resources.memory_available,
            metadata=metadata or {}
        )
        
        with self.lock:
            self.active_operations[operation_id] = metrics
        
        return operation_id
    
    def end_operation(self, operation_id: str, success: bool = True, 
                     error_message: Optional[str] = None) -> PerformanceMetrics:
        """
        End monitoring an operation.
        
        Args:
            operation_id: Operation ID from start_operation
            success: Whether operation was successful
            error_message: Error message if failed
        
        Returns:
            Completed performance metrics
        """
        with self.lock:
            if operation_id not in self.active_operations:
                raise ValueError(f"Operation ID not found: {operation_id}")
            
            metrics = self.active_operations.pop(operation_id)
        
        # Get final system resources
        resources = SystemResources.get_current()
        
        metrics.end_time = time.time()
        metrics.duration = metrics.end_time - metrics.start_time
        metrics.cpu_usage_end = resources.cpu_usage
        metrics.memory_usage_end = resources.memory_total - resources.memory_available
        metrics.success = success
        metrics.error_message = error_message
        
        # Add to history and stats
        with self.lock:
            self.metrics_history.append(metrics)
            self.operation_stats[metrics.operation_name].append(metrics.duration)
        
        return metrics
    
    def get_operation_stats(self, operation_name: str) -> Dict[str, Any]:
        """Get statistics for a specific operation."""
        with self.lock:
            durations = self.operation_stats.get(operation_name, [])
        
        if not durations:
            return {'operation_name': operation_name, 'count': 0}
        
        return {
            'operation_name': operation_name,
            'count': len(durations),
            'avg_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations),
            'total_duration': sum(durations)
        }
    
    def get_system_recommendations(self) -> Dict[str, Any]:
        """Get system optimization recommendations."""
        resources = SystemResources.get_current()
        
        recommendations = {
            'current_resources': {
                'cpu_usage': resources.cpu_usage,
                'memory_usage': resources.memory_usage_percent,
                'cpu_count': resources.cpu_count
            },
            'recommendations': []
        }
        
        # CPU recommendations
        if resources.cpu_usage > self.cpu_threshold:
            recommendations['recommendations'].append({
                'type': 'cpu_high',
                'message': f'High CPU usage ({resources.cpu_usage:.1f}%). Consider reducing parallel workers.',
                'suggested_workers': max(self.min_workers, self.current_optimal_workers // 2)
            })
        elif resources.cpu_usage < 30.0:
            recommendations['recommendations'].append({
                'type': 'cpu_low',
                'message': f'Low CPU usage ({resources.cpu_usage:.1f}%). Consider increasing parallel workers.',
                'suggested_workers': min(self.max_workers, self.current_optimal_workers * 2)
            })
        
        # Memory recommendations
        if resources.memory_usage_percent > self.memory_threshold:
            recommendations['recommendations'].append({
                'type': 'memory_high',
                'message': f'High memory usage ({resources.memory_usage_percent:.1f}%). Consider reducing batch sizes.',
                'suggested_action': 'reduce_batch_size'
            })
        
        return recommendations
    
    def optimize_worker_count(self, workload_size: int, operation_type: str = 'mixed') -> int:
        """
        Determine optimal worker count based on system resources and workload.
        
        Args:
            workload_size: Size of the workload
            operation_type: Type of operation ('cpu_bound', 'io_bound', 'mixed')
        
        Returns:
            Optimal number of workers
        """
        resources = SystemResources.get_current()
        
        # Base worker count on operation type
        if operation_type == 'cpu_bound':
            base_workers = resources.cpu_count
        elif operation_type == 'io_bound':
            base_workers = resources.cpu_count * 2
        else:  # mixed
            base_workers = int(resources.cpu_count * 1.5)
        
        # Adjust based on system load
        if resources.cpu_usage > self.cpu_threshold:
            base_workers = max(1, base_workers // 2)
        elif resources.memory_usage_percent > self.memory_threshold:
            base_workers = max(1, base_workers // 2)
        
        # Limit by workload size
        optimal_workers = min(base_workers, workload_size, self.max_workers)
        optimal_workers = max(optimal_workers, self.min_workers)
        
        # Update current optimal for future recommendations
        self.current_optimal_workers = optimal_workers
        
        return optimal_workers
    
    def export_metrics(self, filepath: str, format: str = 'json'):
        """
        Export performance metrics to file.
        
        Args:
            filepath: Output file path
            format: Export format ('json', 'csv')
        """
        with self.lock:
            metrics_data = [m.to_dict() for m in self.metrics_history]
        
        if format.lower() == 'json':
            with open(filepath, 'w') as f:
                json.dump(metrics_data, f, indent=2, default=str)
        elif format.lower() == 'csv':
            import csv
            if metrics_data:
                with open(filepath, 'w', newline='') as f:
                    writer = csv.DictWriter(f, fieldnames=metrics_data[0].keys())
                    writer.writeheader()
                    writer.writerows(metrics_data)
    
    def clear_history(self):
        """Clear performance metrics history."""
        with self.lock:
            self.metrics_history.clear()
            self.operation_stats.clear()


# Global performance monitor instance
_global_monitor = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get global performance monitor instance."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor


def monitor_performance(operation_name: str, metadata: Optional[Dict[str, Any]] = None):
    """
    Decorator for monitoring function performance.
    
    Args:
        operation_name: Name of the operation
        metadata: Additional metadata
    """
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            monitor = get_performance_monitor()
            operation_id = monitor.start_operation(operation_name, metadata)
            
            try:
                result = func(*args, **kwargs)
                monitor.end_operation(operation_id, success=True)
                return result
            except Exception as e:
                monitor.end_operation(operation_id, success=False, error_message=str(e))
                raise
        
        return wrapper
    return decorator
