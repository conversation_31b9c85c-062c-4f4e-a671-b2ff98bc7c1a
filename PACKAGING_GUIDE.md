# QuizAIGen Library Packaging Guide

This comprehensive guide covers packaging the QuizAIGen AI-powered question generation library into a distributable Python package for PyPI.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Prerequisites](#prerequisites)
3. [Project Structure](#project-structure)
4. [Configuration Files](#configuration-files)
5. [Dependency Management](#dependency-management)
6. [Building the Package](#building-the-package)
7. [Testing the Package](#testing-the-package)
8. [Publishing to PyPI](#publishing-to-pypi)
9. [Version Management](#version-management)
10. [Quality Assurance](#quality-assurance)
11. [Automation & CI/CD](#automation--cicd)
12. [Troubleshooting](#troubleshooting)
13. [Best Practices](#best-practices)

## Project Overview

QuizAIGen is an AI-powered question generation library that supports:
- Multiple question types (MCQ, Boolean, FAQ, Fill-in-the-blank, Short Answer)
- Multi-language support with 50+ languages
- Various input formats (text, PDF, DOCX, URLs)
- Multiple export formats (JSON, CSV, XML, QTI, Moodle, etc.)
- GPU acceleration and memory optimization
- Batch processing capabilities

## Prerequisites

### Required Tools

```bash
# Install build tools
pip install build twine wheel setuptools

# Install validation tools
pip install check-manifest readme-renderer[md] twine

# Install development dependencies
pip install -e ".[dev]"
```

### System Requirements

- Python 3.8+
- Git
- PyPI account (for publishing)
- Test PyPI account (for testing)

## Project Structure

The QuizAIGen project follows Python packaging best practices:

```
QuizAIGen/
├── quizaigen/                 # Main package directory
│   ├── __init__.py           # Package initialization
│   ├── core/                 # Core functionality
│   ├── generators/           # Question generators
│   ├── processors/           # Text processors
│   ├── exporters/           # Export managers
│   ├── models/              # AI models
│   └── utils/               # Utilities
├── tests/                    # Test suite
├── examples/                 # Usage examples
├── scripts/                  # Build/publish scripts
├── docs/                     # Documentation
├── pyproject.toml           # Modern Python packaging config
├── requirements.txt         # Dependencies
├── README.md               # Project description
├── LICENSE                 # License file
├── CHANGELOG.md           # Version history
└── .github/               # GitHub Actions workflows
```

## Configuration Files

### pyproject.toml (Primary Configuration)

The project uses `pyproject.toml` as the primary configuration file:

```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "quizaigen"
version = "1.0.0"
description = "AI-powered question generation library with multi-language support"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
keywords = [
    "ai", "nlp", "question-generation", "quiz", "education", 
    "machine-learning", "transformers", "multilingual"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Education",
    "Topic :: Text Processing :: Linguistic"
]

dependencies = [
    "torch>=1.9.0",
    "transformers>=4.20.0",
    "tokenizers>=0.12.0",
    "spacy>=3.4.0",
    "nltk>=3.7",
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "scikit-learn>=1.0.0",
    "requests>=2.25.0",
    "beautifulsoup4>=4.9.0",
    "PyPDF2>=2.0.0",
    "python-docx>=0.8.11",
    "pydantic>=1.8.0",
    "tqdm>=4.62.0",
    "sense2vec>=2.0.0",
    "gensim>=4.1.0",
    "langdetect>=1.0.9",
    "polyglot>=16.7.4",
    "fasttext>=0.9.2"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
    "pre-commit>=2.17.0"
]
docs = [
    "sphinx>=4.5.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.17.0"
]
examples = [
    "jupyter>=1.0.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0"
]

[project.urls]
Homepage = "https://github.com/yourusername/quizaigen"
Documentation = "https://quizaigen.readthedocs.io"
Repository = "https://github.com/yourusername/quizaigen"
"Bug Tracker" = "https://github.com/yourusername/quizaigen/issues"
Changelog = "https://github.com/yourusername/quizaigen/blob/main/CHANGELOG.md"

[tool.setuptools.packages.find]
where = ["."]  # list of folders that contain the packages (["src"] by default)
include = ["quizaigen*"]  # package names should match these glob patterns (["*"] by default)
exclude = ["tests*"]  # exclude packages matching these glob patterns

[tool.setuptools.package-data]
quizaigen = ["*.json", "*.yaml", "*.yml", "models/*.bin", "data/*.txt"]

# Tool configurations
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m "not slow"')",
    "integration: marks tests as integration tests",
    "gpu: marks tests that require GPU"
]

[tool.coverage.run]
source = ["quizaigen"]
omit = ["*/tests/*", "*/examples/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError"
]
```

### Alternative: setup.py (Legacy Support)

For compatibility with older tools, you can also include a minimal `setup.py`:

```python
#!/usr/bin/env python3
"""Setup script for QuizAIGen."""

from setuptools import setup

# Use pyproject.toml for configuration
setup()
```

## Dependency Management

### Core Dependencies

The library has several categories of dependencies:

1. **AI/ML Libraries**: torch, transformers, spacy, nltk
2. **Data Processing**: pandas, numpy, scikit-learn
3. **Text Processing**: beautifulsoup4, PyPDF2, python-docx
4. **Multi-language**: langdetect, polyglot, fasttext
5. **Utilities**: requests, tqdm, pydantic

### Managing Dependencies

```bash
# Install core dependencies
pip install -r requirements.txt

# Install with optional dependencies
pip install "quizaigen[dev,docs,examples]"

# Install in development mode
pip install -e ".[dev]"
```

### Dependency Pinning Strategy

- **Lower bounds**: Specify minimum versions for compatibility
- **Upper bounds**: Avoid for core dependencies unless known issues
- **Exact pinning**: Only for development dependencies in separate files

## Building the Package

### Using the Build Tool

```bash
# Clean previous builds
rm -rf dist/ build/ *.egg-info/

# Build source distribution and wheel
python -m build

# Check build outputs
ls -la dist/
# quizaigen-1.0.0-py3-none-any.whl
# quizaigen-1.0.0.tar.gz
```

### Build Script

Create `scripts/build_package.py`:

```python
#!/usr/bin/env python3
"""Build script for QuizAIGen package."""

import subprocess
import shutil
from pathlib import Path

def clean_build():
    """Clean previous build artifacts."""
    dirs_to_clean = ['dist', 'build', 'quizaigen.egg-info']
    for dir_name in dirs_to_clean:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"Cleaned {dir_name}/")

def build_package():
    """Build the package."""
    print("Building package...")
    result = subprocess.run(["python", "-m", "build"], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Package built successfully")
        # List generated files
        dist_dir = Path("dist")
        if dist_dir.exists():
            for file in dist_dir.glob("*"):
                size_mb = file.stat().st_size / (1024 * 1024)
                print(f"  📦 {file.name} ({size_mb:.1f} MB)")
    else:
        print("❌ Build failed:")
        print(result.stderr)
        return False
    
    return True

if __name__ == "__main__":
    clean_build()
    build_package()
```

### Validating the Build

```bash
# Check package metadata
twine check dist/*

# Test installation in virtual environment
python -m venv test_env
source test_env/bin/activate  # On Windows: test_env\Scripts\activate
pip install dist/quizaigen-1.0.0-py3-none-any.whl
python -c "import quizaigen; print(quizaigen.__version__)"
deactivate
rm -rf test_env
```

## Testing the Package

### Pre-publication Testing

```bash
# Run full test suite
pytest tests/ -v

# Test with coverage
pytest tests/ --cov=quizaigen --cov-report=html

# Test installation from wheel
pip install dist/quizaigen-*.whl
python -c "from quizaigen import QuestionGenerator; print('Import successful')"
```

### Package Validation Script

The project includes `scripts/package_validator.py` for comprehensive validation:

```bash
# Run package validation
python scripts/package_validator.py

# Example output:
# ✅ Required file exists: pyproject.toml
# ✅ Required field present: project.name
# ✅ README has good length (2547 characters)
# ⚠️  Recommended field missing: project.keywords
```

## Publishing to PyPI

### Setting Up Credentials

#### Option 1: API Tokens (Recommended)

```bash
# Set environment variables
export PYPI_API_TOKEN="pypi-your-token-here"
export TEST_PYPI_API_TOKEN="pypi-your-test-token-here"
```

#### Option 2: .pypirc File

Create `~/.pypirc`:

```ini
[distutils]
index-servers =
    pypi
    testpypi

[pypi]
username = __token__
password = pypi-your-token-here

[testpypi]
repository = https://test.pypi.org/legacy/
username = __token__
password = pypi-your-test-token-here
```

### Publishing Process

#### 1. Test on Test PyPI

```bash
# Upload to Test PyPI
twine upload --repository testpypi dist/*

# Test installation from Test PyPI
pip install --index-url https://test.pypi.org/simple/ quizaigen
```

#### 2. Publish to PyPI

```bash
# Upload to PyPI
twine upload dist/*

# Verify installation
pip install quizaigen
```

### Automated Publishing Script

The project includes `scripts/pypi_publisher.py`:

```bash
# Publish to Test PyPI
python scripts/pypi_publisher.py --test-pypi

# Publish to PyPI (production)
python scripts/pypi_publisher.py --publish

# Dry run (validation only)
python scripts/pypi_publisher.py --dry-run
```

## Version Management

### Semantic Versioning

QuizAIGen follows [Semantic Versioning](https://semver.org/):

- **MAJOR**: Incompatible API changes
- **MINOR**: Backward-compatible functionality additions
- **PATCH**: Backward-compatible bug fixes

### Version Update Process

1. **Update version in pyproject.toml**:
   ```toml
   [project]
   version = "1.1.0"
   ```

2. **Update __init__.py**:
   ```python
   __version__ = "1.1.0"
   ```

3. **Update CHANGELOG.md**:
   ```markdown
   ## [1.1.0] - 2024-01-15
   ### Added
   - New short answer question generation
   - GPU acceleration support
   
   ### Fixed
   - Memory optimization issues
   ```

4. **Create Git tag**:
   ```bash
   git tag -a v1.1.0 -m "Release version 1.1.0"
   git push origin v1.1.0
   ```

### Automated Version Management

Create `scripts/bump_version.py`:

```python
#!/usr/bin/env python3
"""Version bumping script for QuizAIGen."""

import re
import argparse
from pathlib import Path

def bump_version(version_type: str):
    """Bump version in pyproject.toml and __init__.py."""
    # Read current version from pyproject.toml
    pyproject_path = Path("pyproject.toml")
    content = pyproject_path.read_text()
    
    version_match = re.search(r'version = "([0-9]+)\.([0-9]+)\.([0-9]+)"', content)
    if not version_match:
        raise ValueError("Could not find version in pyproject.toml")
    
    major, minor, patch = map(int, version_match.groups())
    
    # Bump version
    if version_type == "major":
        major += 1
        minor = 0
        patch = 0
    elif version_type == "minor":
        minor += 1
        patch = 0
    elif version_type == "patch":
        patch += 1
    else:
        raise ValueError("version_type must be 'major', 'minor', or 'patch'")
    
    new_version = f"{major}.{minor}.{patch}"
    
    # Update pyproject.toml
    new_content = re.sub(
        r'version = "[0-9]+\.[0-9]+\.[0-9]+"',
        f'version = "{new_version}"',
        content
    )
    pyproject_path.write_text(new_content)
    
    # Update __init__.py
    init_path = Path("quizaigen/__init__.py")
    init_content = init_path.read_text()
    new_init_content = re.sub(
        r'__version__ = "[0-9]+\.[0-9]+\.[0-9]+"',
        f'__version__ = "{new_version}"',
        init_content
    )
    init_path.write_text(new_init_content)
    
    print(f"Version bumped to {new_version}")
    return new_version

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Bump QuizAIGen version")
    parser.add_argument("type", choices=["major", "minor", "patch"], 
                       help="Type of version bump")
    args = parser.parse_args()
    
    bump_version(args.type)
```

## Quality Assurance

### Code Quality Tools

```bash
# Format code
black quizaigen/ tests/ examples/
isort quizaigen/ tests/ examples/

# Lint code
flake8 quizaigen/ tests/
mypy quizaigen/

# Security checks
bandit -r quizaigen/
safety check
```

### Pre-commit Hooks

Create `.pre-commit-config.yaml`:

```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict

  - repo: https://github.com/psf/black
    rev: 22.12.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/pycqa/isort
    rev: 5.11.4
    hooks:
      - id: isort
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.991
    hooks:
      - id: mypy
        additional_dependencies: [types-requests]
```

Install and activate:

```bash
pre-commit install
pre-commit run --all-files
```

### Testing Strategy

```bash
# Unit tests
pytest tests/unit/ -v

# Integration tests
pytest tests/integration/ -v

# Performance tests
pytest tests/performance/ -v

# Test coverage
pytest --cov=quizaigen --cov-report=html --cov-report=term

# Test with different Python versions using tox
tox
```

## Automation & CI/CD

### GitHub Actions Workflow

The project includes `.github/workflows/ci.yml` for comprehensive CI/CD:

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  release:
    types: [published]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11"]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"
    
    - name: Run tests
      run: |
        pytest tests/ --cov=quizaigen --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"
    
    - name: Code quality checks
      run: |
        black --check quizaigen/ tests/
        isort --check-only quizaigen/ tests/
        flake8 quizaigen/ tests/
        mypy quizaigen/
        bandit -r quizaigen/
        safety check

  build:
    runs-on: ubuntu-latest
    needs: [test, quality]
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
    
    - name: Build package
      run: python -m build
    
    - name: Check package
      run: twine check dist/*
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  publish:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'release' && github.event.action == 'published'
    steps:
    - uses: actions/checkout@v3
    
    - name: Download artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/
    
    - name: Publish to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.PYPI_API_TOKEN }}
```

### Release Automation

Create `scripts/release.py`:

```python
#!/usr/bin/env python3
"""Automated release script for QuizAIGen."""

import subprocess
import sys
from pathlib import Path

def run_command(cmd, check=True):
    """Run shell command."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"Error: {result.stderr}")
        sys.exit(1)
    return result

def create_release(version):
    """Create a new release."""
    print(f"Creating release {version}...")
    
    # Run tests
    print("\n1. Running tests...")
    run_command(["pytest", "tests/", "-v"])
    
    # Quality checks
    print("\n2. Running quality checks...")
    run_command(["black", "--check", "quizaigen/", "tests/"])
    run_command(["flake8", "quizaigen/", "tests/"])
    
    # Build package
    print("\n3. Building package...")
    run_command(["python", "-m", "build"])
    
    # Validate package
    print("\n4. Validating package...")
    run_command(["twine", "check", "dist/*"])
    
    # Create git tag
    print("\n5. Creating git tag...")
    run_command(["git", "tag", "-a", f"v{version}", "-m", f"Release {version}"])
    run_command(["git", "push", "origin", f"v{version}"])
    
    print(f"\n✅ Release {version} created successfully!")
    print("   GitHub Actions will handle PyPI publishing.")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python scripts/release.py <version>")
        sys.exit(1)
    
    version = sys.argv[1]
    create_release(version)
```

## Troubleshooting

### Common Issues

#### 1. Build Failures

**Problem**: `ModuleNotFoundError` during build
```
ModuleNotFoundError: No module named 'quizaigen'
```

**Solution**: Install package in development mode
```bash
pip install -e .
```

#### 2. Import Errors

**Problem**: Package imports fail after installation
```
ImportError: cannot import name 'QuestionGenerator'
```

**Solution**: Check `__init__.py` exports
```python
# quizaigen/__init__.py
from .question_generator import QuestionGenerator
from .export_manager import ExportManager
# ... other exports

__all__ = ['QuestionGenerator', 'ExportManager', ...]
```

#### 3. Dependency Conflicts

**Problem**: Version conflicts during installation
```
ERROR: pip's dependency resolver does not currently consider all the ways
```

**Solution**: Review and update dependency versions
```bash
# Check for conflicts
pip-compile requirements.in

# Update dependencies
pip install --upgrade-strategy eager -e .
```

#### 4. Large Package Size

**Problem**: Package is too large for PyPI
```
HTTP Error 413: Request Entity Too Large
```

**Solution**: Exclude unnecessary files
```toml
# pyproject.toml
[tool.setuptools.packages.find]
exclude = ["tests*", "docs*", "examples*", "*.egg-info"]
```

#### 5. Metadata Validation Errors

**Problem**: PyPI rejects package metadata
```
ERROR: Invalid classifier
```

**Solution**: Use valid PyPI classifiers
```bash
# Check valid classifiers
python -c "from trove_classifiers import classifiers; print('\n'.join(classifiers))"
```

### Debugging Tools

```bash
# Check package contents
tar -tzf dist/quizaigen-1.0.0.tar.gz

# Inspect wheel contents
unzip -l dist/quizaigen-1.0.0-py3-none-any.whl

# Validate metadata
twine check dist/* --strict

# Test import in clean environment
python -c "import sys; print(sys.path); import quizaigen; print('Success')"
```

## Best Practices

### 1. Package Structure
- Use clear, descriptive module names
- Keep `__init__.py` files minimal but complete
- Organize code logically by functionality
- Include type hints for better IDE support

### 2. Documentation
- Write comprehensive docstrings
- Include usage examples in docstrings
- Maintain up-to-date README.md
- Document breaking changes in CHANGELOG.md

### 3. Testing
- Aim for >90% test coverage
- Include integration tests
- Test on multiple Python versions
- Test installation from built packages

### 4. Dependencies
- Pin lower bounds, avoid upper bounds
- Use optional dependencies for non-core features
- Regularly update dependencies
- Monitor security vulnerabilities

### 5. Versioning
- Follow semantic versioning strictly
- Tag releases in git
- Maintain detailed changelog
- Use pre-release versions for testing

### 6. Security
- Never commit API keys or secrets
- Use environment variables for sensitive data
- Regularly scan for vulnerabilities
- Keep dependencies updated

### 7. Performance
- Profile package import time
- Minimize startup overhead
- Use lazy imports where appropriate
- Optimize critical paths

### 8. Compatibility
- Support multiple Python versions
- Test on different operating systems
- Handle platform-specific dependencies
- Provide clear system requirements

## Useful Links

- [Python Packaging User Guide](https://packaging.python.org/)
- [PyPI Help](https://pypi.org/help/)
- [Semantic Versioning](https://semver.org/)
- [Python Package Index Classifiers](https://pypi.org/classifiers/)
- [Twine Documentation](https://twine.readthedocs.io/)
- [setuptools Documentation](https://setuptools.pypa.io/)
- [PEP 517 - Build System Interface](https://peps.python.org/pep-0517/)
- [PEP 621 - Project Metadata](https://peps.python.org/pep-0621/)

---

**Note**: This guide is specific to the QuizAIGen project structure and requirements. Adapt the configurations and scripts according to your specific needs and project structure.