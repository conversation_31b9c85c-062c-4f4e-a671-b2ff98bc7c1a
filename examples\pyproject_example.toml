# Example pyproject.toml for QuizAIGen
# This file demonstrates best practices for Python package configuration
# Copy and modify this file as needed for your project

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "quizaigen"
version = "1.0.0"
description = "AI-powered question generation library for educational content"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "QuizAIGen Team", email = "<EMAIL>"},
]
maintainers = [
    {name = "QuizAIGen Team", email = "<EMAIL>"},
]
keywords = [
    "ai", "education", "question-generation", "nlp", "machine-learning",
    "quiz", "assessment", "transformers", "pytorch", "educational-technology"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Education",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Education",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Text Processing :: Linguistic",
]

# Core dependencies
dependencies = [
    "torch>=1.9.0",
    "transformers>=4.20.0",
    "tokenizers>=0.13.0",
    "spacy>=3.4.0",
    "nltk>=3.7",
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "scikit-learn>=1.0.0",
    "requests>=2.25.0",
    "beautifulsoup4>=4.9.0",
    "PyPDF2>=3.0.0",
    "python-docx>=0.8.11",
    "pydantic>=1.8.0",
    "tqdm>=4.62.0",
    "sense2vec>=2.0.0",
    "gensim>=4.1.0",
    "langdetect>=1.0.9",
    "polyglot>=16.7.4",
    "fasttext>=0.9.2",
]

# Optional dependencies for different use cases
[project.optional-dependencies]
# Development dependencies
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",
    "pytest-mock>=3.10.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
    "pre-commit>=2.20.0",
    "twine>=4.0.0",
    "build>=0.8.0",
    "wheel>=0.37.0",
    "setuptools>=61.0.0",
]

# Documentation dependencies
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "sphinx-autodoc-typehints>=1.19.0",
    "myst-parser>=0.18.0",
    "sphinx-copybutton>=0.5.0",
    "sphinxcontrib-mermaid>=0.7.0",
]

# Example and tutorial dependencies
examples = [
    "jupyter>=1.0.0",
    "notebook>=6.4.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "plotly>=5.0.0",
    "ipywidgets>=7.6.0",
]

# Performance and optimization dependencies
performance = [
    "accelerate>=0.20.0",
    "optimum>=1.8.0",
    "onnx>=1.12.0",
    "onnxruntime>=1.12.0",
]

# Multi-language support dependencies
multilingual = [
    "polyglot>=16.7.4",
    "fasttext>=0.9.2",
    "langdetect>=1.0.9",
    "googletrans>=4.0.0",
]

# Testing dependencies for different environments
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",
    "pytest-mock>=3.10.0",
    "pytest-benchmark>=4.0.0",
    "pytest-timeout>=2.1.0",
    "coverage>=6.0.0",
]

# Security and quality assurance
security = [
    "bandit>=1.7.0",
    "safety>=2.0.0",
    "semgrep>=1.0.0",
]

# All optional dependencies combined
all = [
    "quizaigen[dev,docs,examples,performance,multilingual,test,security]"
]

[project.urls]
Homepage = "https://github.com/yourusername/quizaigen"
Documentation = "https://quizaigen.readthedocs.io"
Repository = "https://github.com/yourusername/quizaigen.git"
"Bug Tracker" = "https://github.com/yourusername/quizaigen/issues"
Changelog = "https://github.com/yourusername/quizaigen/blob/main/CHANGELOG.md"
"Funding" = "https://github.com/sponsors/yourusername"
"Say Thanks!" = "https://saythanks.io/to/yourusername"

[project.scripts]
quizaigen = "quizaigen.cli:main"
quizaigen-train = "quizaigen.training.cli:main"
quizaigen-export = "quizaigen.export.cli:main"

[project.entry-points."quizaigen.generators"]
mcq = "quizaigen.generators.mcq:MCQGenerator"
boolean = "quizaigen.generators.boolean:BooleanGenerator"
faq = "quizaigen.generators.faq:FAQGenerator"
fill_blank = "quizaigen.generators.fill_blank:FillBlankGenerator"

# Tool configurations
[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
where = ["."]  # list of folders that contain the packages (["src"] by default)
include = ["quizaigen*"]  # package names should match these glob patterns (["*"] by default)
exclude = ["tests*", "docs*", "examples*", "scripts*"]  # exclude packages matching these glob patterns

[tool.setuptools.package-data]
quizaigen = [
    "data/*.json",
    "data/*.txt",
    "models/*.json",
    "templates/*.html",
    "templates/*.xml",
    "py.typed",
]

# Black code formatter configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
# A regex preceded by ^/ will apply only to files and directories
# in the root of the project.
^/(
  (
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
  | foo.py           # also separately exclude a file named foo.py in
                     # the root of the project
)
'''

# isort import sorter configuration
[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
src_paths = ["quizaigen", "tests", "examples"]
skip_glob = ["*/__init__.py"]
known_first_party = ["quizaigen"]
known_third_party = [
    "torch", "transformers", "spacy", "nltk", "numpy", "pandas",
    "sklearn", "requests", "bs4", "PyPDF2", "docx", "pydantic",
    "tqdm", "sense2vec", "gensim", "langdetect", "polyglot", "fasttext"
]

# Flake8 linter configuration
[tool.flake8]
max-line-length = 88
extend-ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
    "W504",  # line break after binary operator
]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".tox",
    ".mypy_cache",
    "docs",
]
per-file-ignores = [
    "__init__.py:F401",  # imported but unused
    "tests/*:S101",      # use of assert
]

# MyPy type checker configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
pretty = true

# Per-module options
[[tool.mypy.overrides]]
module = [
    "spacy.*",
    "nltk.*",
    "sense2vec.*",
    "gensim.*",
    "polyglot.*",
    "fasttext.*",
    "PyPDF2.*",
    "docx.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m "not slow"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gpu: marks tests that require GPU",
    "network: marks tests that require network access",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

# Coverage configuration
[tool.coverage.run]
source = ["quizaigen"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "*/venv/*",
    "*/.venv/*",
    "*/virtualenv/*",
    "*/.tox/*",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\bProtocol\):",
    "@(abc\.)?abstractmethod",
]
ignore_errors = true
show_missing = true
skip_covered = false
skip_empty = false
sort = "Cover"

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"

# Bandit security linter configuration
[tool.bandit]
exclude_dirs = ["tests", "docs", "examples"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process

# Pre-commit configuration (if using pre-commit)
[tool.pre-commit]
repos = [
    {
        repo = "https://github.com/pre-commit/pre-commit-hooks",
        rev = "v4.4.0",
        hooks = [
            {id = "trailing-whitespace"},
            {id = "end-of-file-fixer"},
            {id = "check-yaml"},
            {id = "check-added-large-files"},
            {id = "check-merge-conflict"},
            {id = "debug-statements"},
        ]
    },
    {
        repo = "https://github.com/psf/black",
        rev = "23.3.0",
        hooks = [{id = "black"}]
    },
    {
        repo = "https://github.com/pycqa/isort",
        rev = "5.12.0",
        hooks = [{id = "isort"}]
    },
    {
        repo = "https://github.com/pycqa/flake8",
        rev = "6.0.0",
        hooks = [{id = "flake8"}]
    },
    {
        repo = "https://github.com/pre-commit/mirrors-mypy",
        rev = "v1.3.0",
        hooks = [{id = "mypy", additional_dependencies = ["types-requests"]}]
    },
]

# Semantic versioning configuration (if using semantic-release)
[tool.semantic_release]
version_toml = "pyproject.toml:project.version"
version_variable = "quizaigen/__init__.py:__version__"
branch = "main"
upload_to_pypi = true
upload_to_release = true
build_command = "python -m build"

# Additional metadata for package discovery
[tool.setuptools_scm]
write_to = "quizaigen/_version.py"
write_to_template = '__version__ = "{version}"\n'

# Wheel configuration
[tool.bdist_wheel]
universal = false  # Set to true if package is pure Python and compatible with both Python 2 and 3

# Documentation building configuration
[tool.sphinx]
source-dir = "docs"
build-dir = "docs/_build"
all_files = true

# Performance profiling configuration
[tool.profile]
output_dir = "profiling_results"
include_patterns = ["quizaigen/**/*.py"]
exclude_patterns = ["tests/**/*.py"]