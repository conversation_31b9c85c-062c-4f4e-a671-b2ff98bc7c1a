#!/usr/bin/env python3
"""
Simple test for batch processor logging
"""

from quizaigen.utils.logger import configure_logging, get_logger
from quizaigen.api.batch_processor import EnhancedBatchProcessor


def test_simple_initialization():
    """Test simple batch processor initialization."""
    print("=== Testing Simple Batch Processor Initialization ===")
    
    # Configure logging
    configure_logging(
        log_dir="simple_test_logs",
        level="INFO",
        console_logging=True,
        file_logging=True
    )
    
    print("✓ Logging configured")
    
    try:
        # Simple configuration
        config = {
            'max_workers': 2
        }
        
        print("✓ Creating batch processor...")
        processor = EnhancedBatchProcessor(config)
        print("✓ Batch processor created successfully")
        
        # Test logging methods
        processor.log_info("Test info message", extra_data={'test': True})
        processor.log_debug("Test debug message")
        
        print("✓ Logging methods work correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run simple test."""
    print("🚀 Starting Simple Batch Processor Logging Test")
    
    success = test_simple_initialization()
    
    if success:
        print("\n✅ Simple test completed successfully!")
    else:
        print("\n❌ Simple test failed")
    
    return success


if __name__ == "__main__":
    main()
