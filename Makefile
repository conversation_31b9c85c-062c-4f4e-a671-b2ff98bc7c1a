# QuizAIGen Makefile
# Provides convenient commands for development and publishing

.PHONY: help install install-dev test lint format build clean validate publish publish-test release docs

# Default target
help:
	@echo "QuizAIGen Development Commands"
	@echo "=============================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  install      Install package in development mode"
	@echo "  install-dev  Install with development dependencies"
	@echo ""
	@echo "Development Commands:"
	@echo "  test         Run all tests"
	@echo "  test-fast    Run tests without slow tests"
	@echo "  lint         Run linting checks"
	@echo "  format       Format code with black and isort"
	@echo "  type-check   Run type checking with mypy"
	@echo ""
	@echo "Build Commands:"
	@echo "  build        Build package distributions"
	@echo "  clean        Clean build artifacts"
	@echo "  validate     Validate package for publishing"
	@echo ""
	@echo "Publishing Commands:"
	@echo "  publish-test Publish to Test PyPI"
	@echo "  publish      Publish to PyPI"
	@echo "  release      Create release (interactive)"
	@echo ""
	@echo "Documentation Commands:"
	@echo "  docs         Build documentation"
	@echo "  docs-serve   Serve documentation locally"
	@echo ""
	@echo "Utility Commands:"
	@echo "  check-deps   Check for dependency updates"
	@echo "  security     Run security checks"

# Setup Commands
install:
	pip install -e .

install-dev:
	pip install -e .
	pip install -r requirements-dev.txt

# Development Commands
test:
	pytest tests/ -v --cov=quizaigen --cov-report=term-missing --cov-report=html

test-fast:
	pytest tests/ -v -m "not slow" --cov=quizaigen --cov-report=term-missing

test-integration:
	pytest tests/ -v -m "integration" --cov=quizaigen

lint:
	flake8 quizaigen tests --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 quizaigen tests --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

format:
	black quizaigen tests examples scripts
	isort quizaigen tests examples scripts

format-check:
	black --check --diff quizaigen tests examples scripts
	isort --check-only --diff quizaigen tests examples scripts

type-check:
	mypy quizaigen --ignore-missing-imports

# Build Commands
build: clean
	python -m build

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

validate:
	python scripts/package_validator.py

# Publishing Commands
publish-test: validate build
	python scripts/pypi_publisher.py --test-pypi

publish: validate build
	python scripts/pypi_publisher.py

publish-dry-run: validate build
	python scripts/pypi_publisher.py --dry-run

# Release Commands
release-patch:
	python scripts/release_manager.py patch

release-minor:
	python scripts/release_manager.py minor

release-major:
	python scripts/release_manager.py major

release-prerelease:
	python scripts/release_manager.py prerelease --prerelease alpha1

release: release-patch

# Documentation Commands
docs:
	@echo "Building documentation..."
	@echo "Note: Sphinx documentation setup needed"

docs-serve:
	@echo "Serving documentation..."
	@echo "Note: Sphinx documentation setup needed"

# Utility Commands
check-deps:
	pip list --outdated

security:
	safety check --json || true
	bandit -r quizaigen -f json || true

# Quality Assurance
qa: format-check lint type-check test

# Full CI simulation
ci: install-dev qa build validate

# Development setup
setup: install-dev
	python -m spacy download en_core_web_sm
	@echo "Development environment setup complete!"

# Example commands
examples:
	@echo "Running examples..."
	python examples/basic_usage.py
	python examples/batch_processing.py
	python examples/document_processing.py

# Performance testing
benchmark:
	pytest tests/ -k "benchmark" --benchmark-only --benchmark-json=benchmark.json

# Coverage report
coverage:
	pytest tests/ --cov=quizaigen --cov-report=html --cov-report=term
	@echo "Coverage report generated in htmlcov/"

# Install pre-commit hooks
pre-commit:
	pre-commit install
	pre-commit run --all-files

# Database/cache cleanup
clean-cache:
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

# Version info
version:
	python -c "import quizaigen; print(f'QuizAIGen v{quizaigen.__version__}')"

# Package info
info:
	python -c "
	import quizaigen
	print(f'Package: {quizaigen.__name__}')
	print(f'Version: {quizaigen.__version__}')
	print(f'Author: {quizaigen.__author__}')
	print(f'License: {quizaigen.__license__}')
	"

# Quick development cycle
dev: format lint test

# Full release preparation
prepare-release: clean qa build validate
	@echo "Release preparation complete!"
	@echo "Run 'make release' to create a new release"

# Help for specific commands
help-release:
	@echo "Release Commands Help"
	@echo "===================="
	@echo ""
	@echo "release-patch    Bump patch version (0.1.0 -> 0.1.1)"
	@echo "release-minor    Bump minor version (0.1.0 -> 0.2.0)"
	@echo "release-major    Bump major version (0.1.0 -> 1.0.0)"
	@echo "release-prerelease  Create prerelease (0.1.0 -> 0.1.1-alpha1)"
	@echo ""
	@echo "After running a release command:"
	@echo "1. Review the changes"
	@echo "2. Push the tag: git push origin v<version>"
	@echo "3. GitHub Actions will handle the rest"

help-publish:
	@echo "Publishing Commands Help"
	@echo "======================="
	@echo ""
	@echo "publish-test     Publish to Test PyPI (for testing)"
	@echo "publish          Publish to production PyPI"
	@echo "publish-dry-run  Simulate publishing (no actual upload)"
	@echo ""
	@echo "Prerequisites:"
	@echo "- Set PYPI_API_TOKEN environment variable"
	@echo "- Set TEST_PYPI_API_TOKEN for test publishing"
	@echo "- Or configure ~/.pypirc file"

# Windows compatibility
ifeq ($(OS),Windows_NT)
    RM = del /Q
    RMDIR = rmdir /S /Q
else
    RM = rm -f
    RMDIR = rm -rf
endif
