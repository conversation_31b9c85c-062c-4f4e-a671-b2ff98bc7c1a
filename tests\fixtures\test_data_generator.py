"""
Test Data Generator for QuizAIGen

Provides utilities for generating test data, mock questions,
and sample content for comprehensive testing.
"""

import random
import string
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from quizaigen.generators.base import Question


@dataclass
class TestQuestion:
    """Test question data structure."""
    question: str
    type: str
    answer: str
    options: Optional[List[str]] = None
    explanation: Optional[str] = None
    difficulty: str = "medium"
    language: str = "en"


class TestDataGenerator:
    """Generate test data for QuizAIGen testing."""
    
    def __init__(self, seed: Optional[int] = None):
        """
        Initialize test data generator.
        
        Args:
            seed: Random seed for reproducible test data
        """
        if seed is not None:
            random.seed(seed)
        
        self.question_templates = {
            'mcq': [
                "What is {concept}?",
                "Which of the following best describes {concept}?",
                "What are the main characteristics of {concept}?",
                "How does {concept} work?",
                "What is the purpose of {concept}?"
            ],
            'boolean': [
                "{concept} is a type of {category}.",
                "{concept} was developed in {year}.",
                "{concept} is used for {purpose}.",
                "{concept} supports {feature}.",
                "{concept} requires {requirement}."
            ],
            'faq': [
                "What is {concept}?",
                "How do you use {concept}?",
                "Why is {concept} important?",
                "When should you use {concept}?",
                "Where is {concept} commonly used?"
            ],
            'fill_blank': [
                "{concept} is a _____ that helps with _____.",
                "The main purpose of {concept} is to _____.",
                "_____ is an important feature of {concept}.",
                "{concept} was created to solve the problem of _____."
            ]
        }
        
        self.concepts = [
            "Python", "Machine Learning", "Artificial Intelligence", "Data Science",
            "Neural Networks", "Deep Learning", "Natural Language Processing",
            "Computer Vision", "Algorithms", "Programming", "Software Development",
            "Web Development", "Database", "Cloud Computing", "Cybersecurity"
        ]
        
        self.categories = [
            "programming language", "technology", "methodology", "framework",
            "library", "tool", "concept", "approach", "system", "platform"
        ]
        
        self.purposes = [
            "data analysis", "automation", "problem solving", "development",
            "optimization", "processing", "management", "security", "efficiency"
        ]
    
    def generate_sample_questions(self, 
                                question_type: str, 
                                count: int = 5,
                                language: str = "en") -> List[TestQuestion]:
        """
        Generate sample questions of specified type.
        
        Args:
            question_type: Type of questions to generate
            count: Number of questions to generate
            language: Language for questions
            
        Returns:
            List of test questions
        """
        questions = []
        templates = self.question_templates.get(question_type, [])
        
        if not templates:
            return questions
        
        for i in range(count):
            template = random.choice(templates)
            concept = random.choice(self.concepts)
            category = random.choice(self.categories)
            purpose = random.choice(self.purposes)
            year = random.randint(1950, 2023)
            
            question_text = template.format(
                concept=concept,
                category=category,
                purpose=purpose,
                year=year,
                feature="advanced features",
                requirement="proper setup"
            )
            
            # Generate appropriate answer and options
            if question_type == "mcq":
                options = self._generate_mcq_options(concept)
                answer = options[0]  # First option is correct
                random.shuffle(options)  # Shuffle for realistic test
            elif question_type == "boolean":
                answer = random.choice(["True", "False"])
                options = None
            else:
                answer = f"Sample answer for {concept}"
                options = None
            
            questions.append(TestQuestion(
                question=question_text,
                type=question_type,
                answer=answer,
                options=options,
                explanation=f"This question tests knowledge about {concept}.",
                difficulty=random.choice(["easy", "medium", "hard"]),
                language=language
            ))
        
        return questions
    
    def _generate_mcq_options(self, concept: str) -> List[str]:
        """Generate MCQ options for a concept."""
        correct_option = f"{concept} is the correct answer"
        
        distractors = [
            f"Alternative explanation for {concept}",
            f"Different approach to {concept}",
            f"Unrelated concept to {concept}",
            f"Opposite of {concept}"
        ]
        
        options = [correct_option] + random.sample(distractors, 3)
        return options
    
    def generate_sample_text(self, 
                           topic: str = None, 
                           length: str = "medium",
                           language: str = "en") -> str:
        """
        Generate sample text for testing.
        
        Args:
            topic: Topic for the text
            length: Length of text (short, medium, long)
            language: Language for the text
            
        Returns:
            Generated sample text
        """
        if topic is None:
            topic = random.choice(self.concepts)
        
        length_configs = {
            "short": {"sentences": 2, "words_per_sentence": 10},
            "medium": {"sentences": 5, "words_per_sentence": 15},
            "long": {"sentences": 10, "words_per_sentence": 20}
        }
        
        config = length_configs.get(length, length_configs["medium"])
        
        if language == "en":
            return self._generate_english_text(topic, config)
        elif language == "es":
            return self._generate_spanish_text(topic, config)
        elif language == "fr":
            return self._generate_french_text(topic, config)
        else:
            return self._generate_english_text(topic, config)
    
    def _generate_english_text(self, topic: str, config: Dict) -> str:
        """Generate English text."""
        sentences = []
        
        # Opening sentence
        sentences.append(f"{topic} is an important concept in modern technology.")
        
        # Additional sentences
        sentence_templates = [
            f"It helps developers and researchers solve complex problems.",
            f"The main advantages include efficiency and scalability.",
            f"Many companies are adopting {topic} for their projects.",
            f"Understanding {topic} is crucial for success in the field.",
            f"Recent developments in {topic} have been remarkable.",
            f"The future of {topic} looks very promising.",
            f"Experts recommend learning {topic} fundamentals first.",
            f"Common applications include data processing and automation."
        ]
        
        for i in range(config["sentences"] - 1):
            sentences.append(random.choice(sentence_templates))
        
        return " ".join(sentences)
    
    def _generate_spanish_text(self, topic: str, config: Dict) -> str:
        """Generate Spanish text."""
        sentences = [
            f"{topic} es un concepto importante en la tecnología moderna.",
            f"Ayuda a desarrolladores e investigadores a resolver problemas complejos.",
            f"Las principales ventajas incluyen eficiencia y escalabilidad.",
            f"Muchas empresas están adoptando {topic} para sus proyectos."
        ]
        
        return " ".join(sentences[:config["sentences"]])
    
    def _generate_french_text(self, topic: str, config: Dict) -> str:
        """Generate French text."""
        sentences = [
            f"{topic} est un concept important dans la technologie moderne.",
            f"Il aide les développeurs et chercheurs à résoudre des problèmes complexes.",
            f"Les principaux avantages incluent l'efficacité et la scalabilité.",
            f"Beaucoup d'entreprises adoptent {topic} pour leurs projets."
        ]
        
        return " ".join(sentences[:config["sentences"]])
    
    def generate_mock_questions(self, count: int = 10) -> List[Question]:
        """
        Generate mock Question objects for testing.
        
        Args:
            count: Number of questions to generate
            
        Returns:
            List of Question objects
        """
        questions = []
        question_types = ["mcq", "boolean", "faq", "fill_blank"]
        
        for i in range(count):
            q_type = random.choice(question_types)
            test_questions = self.generate_sample_questions(q_type, 1)
            
            if test_questions:
                test_q = test_questions[0]
                
                question = Question(
                    question=test_q.question,
                    type=test_q.type,
                    answer=test_q.answer,
                    options=test_q.options,
                    explanation=test_q.explanation,
                    metadata={
                        'difficulty': test_q.difficulty,
                        'language': test_q.language,
                        'generated_at': datetime.now().isoformat(),
                        'test_data': True
                    }
                )
                
                questions.append(question)
        
        return questions
    
    def generate_test_files(self, temp_dir: str) -> Dict[str, str]:
        """
        Generate test files in temporary directory.
        
        Args:
            temp_dir: Temporary directory path
            
        Returns:
            Dictionary mapping file types to file paths
        """
        import os
        
        files = {}
        
        # Generate text file
        text_content = self.generate_sample_text("Artificial Intelligence", "long")
        text_file = os.path.join(temp_dir, "sample.txt")
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(text_content)
        files['text'] = text_file
        
        # Generate JSON file with questions
        questions = self.generate_mock_questions(5)
        json_content = {
            'questions': [q.to_dict() for q in questions],
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'count': len(questions)
            }
        }
        
        import json
        json_file = os.path.join(temp_dir, "sample_questions.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_content, f, indent=2)
        files['json'] = json_file
        
        return files
