# QuizAIGen Publishing Guide

This guide covers the complete process for publishing QuizAIGen to PyPI, including automated workflows and manual publishing procedures.

## 📋 Table of Contents

- [Overview](#overview)
- [Automated Publishing (Recommended)](#automated-publishing-recommended)
- [Manual Publishing](#manual-publishing)
- [Publishing Scripts](#publishing-scripts)
- [Version Management](#version-management)
- [Testing Releases](#testing-releases)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

QuizAIGen uses a comprehensive publishing workflow that includes:

- **Automated CI/CD**: GitHub Actions for automated testing and publishing
- **Version Management**: Automated version bumping and changelog generation
- **Package Validation**: Comprehensive checks before publishing
- **Multi-environment Support**: Test PyPI and production PyPI publishing
- **Safety Checks**: Validation, duplicate detection, and confirmation prompts

## 🤖 Automated Publishing (Recommended)

### GitHub Actions Workflow

The project includes automated publishing via GitHub Actions:

1. **Trigger Release**: Push a version tag to trigger the release workflow
2. **Automated Testing**: Full test suite runs on multiple Python versions
3. **Package Building**: Wheel and source distributions are built
4. **PyPI Publishing**: Automatic publishing using trusted publishing

### Creating a Release

#### Method 1: Using Release Manager Script

```bash
# Install dependencies
pip install -r requirements-dev.txt

# Prepare release (patch version bump)
python scripts/release_manager.py patch

# Review changes and create tag
git add .
git commit -m "Release v0.1.1"
git tag -a v0.1.1 -m "Release v0.1.1"
git push origin main
git push origin v0.1.1
```

#### Method 2: Manual Tag Creation

```bash
# Update version in files manually
# Update CHANGELOG.md manually

# Create and push tag
git add .
git commit -m "Release v0.1.1"
git tag -a v0.1.1 -m "Release v0.1.1"
git push origin main
git push origin v0.1.1
```

#### Method 3: GitHub Web Interface

1. Go to the repository's "Releases" page
2. Click "Create a new release"
3. Choose or create a new tag (e.g., `v0.1.1`)
4. Fill in release title and description
5. Publish the release

### Workflow Triggers

The release workflow is triggered by:

- **Tag Push**: Any tag matching `v*` pattern
- **Manual Dispatch**: Via GitHub Actions web interface
- **Workflow Dispatch**: With version input parameter

## 🔧 Manual Publishing

### Prerequisites

1. **Install Build Tools**:
   ```bash
   pip install build twine
   ```

2. **Set Up PyPI Credentials**:
   
   **Option A: API Token (Recommended)**
   ```bash
   export PYPI_API_TOKEN="pypi-your-token-here"
   export TEST_PYPI_API_TOKEN="pypi-your-test-token-here"
   ```
   
   **Option B: .pypirc File**
   ```ini
   [distutils]
   index-servers = 
       pypi
       testpypi

   [pypi]
   username = __token__
   password = pypi-your-token-here

   [testpypi]
   repository = https://test.pypi.org/legacy/
   username = __token__
   password = pypi-your-test-token-here
   ```

### Manual Publishing Steps

#### 1. Validate Package

```bash
python scripts/package_validator.py
```

#### 2. Build Package

```bash
python -m build
```

#### 3. Check Package

```bash
twine check dist/*
```

#### 4. Test Publish (Optional)

```bash
twine upload --repository testpypi dist/*
```

#### 5. Publish to PyPI

```bash
twine upload dist/*
```

## 🛠️ Publishing Scripts

### Release Manager (`scripts/release_manager.py`)

Automates version bumping and release preparation:

```bash
# Patch version bump (0.1.0 -> 0.1.1)
python scripts/release_manager.py patch

# Minor version bump (0.1.0 -> 0.2.0)
python scripts/release_manager.py minor

# Major version bump (0.1.0 -> 1.0.0)
python scripts/release_manager.py major

# Prerelease version (0.1.0 -> 0.1.1-alpha1)
python scripts/release_manager.py prerelease --prerelease alpha1

# Skip tests and build
python scripts/release_manager.py patch --skip-tests --skip-build

# Create git tag automatically
python scripts/release_manager.py patch --create-tag
```

### Package Validator (`scripts/package_validator.py`)

Validates package before publishing:

```bash
# Run all validation checks
python scripts/package_validator.py

# Save validation report
python scripts/package_validator.py --output validation_report.json
```

**Validation Checks**:
- Project structure
- pyproject.toml configuration
- README content and quality
- Version consistency
- Package build success
- Package contents
- Twine validation
- Import testing

### PyPI Publisher (`scripts/pypi_publisher.py`)

Handles publishing with safety checks:

```bash
# Publish to Test PyPI
python scripts/pypi_publisher.py --test-pypi

# Dry run (no actual publishing)
python scripts/pypi_publisher.py --dry-run

# Force publish (skip confirmations)
python scripts/pypi_publisher.py --force

# Skip validation
python scripts/pypi_publisher.py --skip-validation

# Publish to production PyPI
python scripts/pypi_publisher.py
```

## 📊 Version Management

### Version Scheme

QuizAIGen follows [Semantic Versioning](https://semver.org/):

- **MAJOR**: Incompatible API changes
- **MINOR**: New functionality (backward compatible)
- **PATCH**: Bug fixes (backward compatible)
- **PRERELEASE**: Alpha, beta, or release candidate versions

### Version Locations

Versions must be consistent across:

- `pyproject.toml`: `project.version`
- `quizaigen/__init__.py`: `__version__`
- Git tags: `v{version}`

### Prerelease Versions

Supported prerelease formats:

- `1.0.0-alpha1`, `1.0.0-alpha2`, etc.
- `1.0.0-beta1`, `1.0.0-beta2`, etc.
- `1.0.0-rc1`, `1.0.0-rc2`, etc.

## 🧪 Testing Releases

### Test PyPI

Always test releases on Test PyPI first:

1. **Publish to Test PyPI**:
   ```bash
   python scripts/pypi_publisher.py --test-pypi
   ```

2. **Install from Test PyPI**:
   ```bash
   pip install -i https://test.pypi.org/simple/ quizaigen
   ```

3. **Test Installation**:
   ```python
   import quizaigen
   print(quizaigen.__version__)
   
   # Test basic functionality
   from quizaigen import QuestionGenerator
   generator = QuestionGenerator()
   ```

### Local Testing

Test package installation locally:

```bash
# Build package
python -m build

# Install locally
pip install dist/quizaigen-*.whl

# Test import
python -c "import quizaigen; print(quizaigen.__version__)"
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Version Already Exists

**Error**: `File already exists`

**Solution**: 
- Bump version number
- Check if version was already published
- Use `--force` flag (not recommended)

#### 2. Authentication Failed

**Error**: `Invalid or non-existent authentication information`

**Solutions**:
- Check API token is correct
- Verify token has upload permissions
- Update .pypirc file
- Set environment variables correctly

#### 3. Package Validation Failed

**Error**: Various validation errors

**Solutions**:
- Run `python scripts/package_validator.py` for details
- Fix reported issues
- Use `--skip-validation` for testing (not recommended)

#### 4. Build Failed

**Error**: Package build errors

**Solutions**:
- Check pyproject.toml syntax
- Verify all required files exist
- Install build dependencies: `pip install build`

#### 5. Import Test Failed

**Error**: Package cannot be imported after installation

**Solutions**:
- Check package structure
- Verify __init__.py files exist
- Check for missing dependencies
- Test in clean virtual environment

### Debug Commands

```bash
# Check package contents
python -m zipfile -l dist/quizaigen-*.whl

# Verbose twine upload
twine upload --verbose dist/*

# Check PyPI package info
pip show quizaigen

# Test in isolated environment
python -m venv test_env
source test_env/bin/activate  # or test_env\Scripts\activate on Windows
pip install quizaigen
python -c "import quizaigen; print('Success!')"
```

### Getting Help

1. **Check Logs**: Review GitHub Actions logs for automated publishing
2. **Validate Package**: Run validation script for detailed error reports
3. **Test Locally**: Use local installation testing
4. **PyPI Status**: Check [PyPI Status](https://status.python.org/) for service issues
5. **Documentation**: Review [PyPI Publishing Documentation](https://packaging.python.org/tutorials/packaging-projects/)

## 📚 Additional Resources

- [Python Packaging User Guide](https://packaging.python.org/)
- [Twine Documentation](https://twine.readthedocs.io/)
- [GitHub Actions for Python](https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python)
- [Semantic Versioning](https://semver.org/)
- [PyPI Help](https://pypi.org/help/)

---

**Note**: Always test releases thoroughly before publishing to production PyPI. Once published, packages cannot be deleted or modified.
