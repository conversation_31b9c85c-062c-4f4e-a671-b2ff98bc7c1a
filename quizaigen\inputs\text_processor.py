"""
Text Input Processor

This module handles processing of various text inputs.
"""

from typing import Optional, Union
from pathlib import Path
import requests
from bs4 import BeautifulSoup

from ..core.config import Config
from ..utils.logger import LoggerMixin
from ..core.exceptions import InputError, ProcessingError
from ..utils.validation_utils import validate_text_input, validate_file_path, validate_url
from ..utils.file_utils import read_file, get_file_extension
from ..utils.text_utils import clean_text


class TextProcessor(LoggerMixin):
    """Processor for handling various text input formats."""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the text processor.
        
        Args:
            config: Configuration object
        """
        self.config = config or Config()
        self.processing_config = self.config.get_processing_config()
        
        self.log_info("TextProcessor initialized")
    
    def process_text(self, text: str) -> str:
        """
        Process plain text input.
        
        Args:
            text: Input text to process
        
        Returns:
            Processed text
        
        Raises:
            ValidationError: If text validation fails
            ProcessingError: If text processing fails
        """
        # Validate input
        text = validate_text_input(text, field_name="text")
        
        try:
            # Clean and normalize the text
            processed_text = clean_text(
                text,
                remove_extra_whitespace=True,
                remove_special_chars=False,
                lowercase=False
            )
            
            self.log_info(f"Processed text: {len(processed_text)} characters")
            return processed_text
            
        except Exception as e:
            raise ProcessingError(
                stage="text_processing",
                message=f"Failed to process text: {str(e)}"
            )
    
    def process_file(self, file_path: Union[str, Path]) -> str:
        """
        Process text from a file.
        
        Args:
            file_path: Path to the file
        
        Returns:
            Extracted and processed text
        
        Raises:
            ValidationError: If file validation fails
            InputError: If file cannot be read
            ProcessingError: If text processing fails
        """
        # Validate file path
        path = validate_file_path(file_path, must_exist=True, field_name="file_path")
        
        # Get file extension to determine processing method
        extension = get_file_extension(path)
        
        try:
            if extension in ['txt', 'md', 'rst']:
                text = self._process_text_file(path)
            elif extension == 'pdf':
                text = self._process_pdf_file(path)
            elif extension in ['doc', 'docx']:
                text = self._process_word_file(path)
            elif extension in ['html', 'htm']:
                text = self._process_html_file(path)
            else:
                # Try to read as plain text
                self.log_warning(f"Unknown file extension '{extension}', treating as plain text")
                text = self._process_text_file(path)
            
            # Process the extracted text
            return self.process_text(text)
            
        except Exception as e:
            raise ProcessingError(
                stage="file_processing",
                message=f"Failed to process file {file_path}: {str(e)}"
            )
    
    def process_url(self, url: str) -> str:
        """
        Process text from a URL.
        
        Args:
            url: URL to extract content from
        
        Returns:
            Extracted and processed text
        
        Raises:
            ValidationError: If URL validation fails
            InputError: If URL cannot be accessed
            ProcessingError: If text processing fails
        """
        # Validate URL
        url = validate_url(url, field_name="url")
        
        try:
            # Fetch content from URL
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Extract text from HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Process the extracted text
            return self.process_text(text)
            
        except requests.RequestException as e:
            raise InputError(
                input_type="url",
                message=f"Failed to fetch content from URL {url}: {str(e)}"
            )
        except Exception as e:
            raise ProcessingError(
                stage="url_processing",
                message=f"Failed to process URL {url}: {str(e)}"
            )
    
    def _process_text_file(self, file_path: Path) -> str:
        """
        Process a plain text file.
        
        Args:
            file_path: Path to the text file
        
        Returns:
            File content as string
        """
        return read_file(file_path)
    
    def _process_pdf_file(self, file_path: Path) -> str:
        """
        Process a PDF file.
        
        Args:
            file_path: Path to the PDF file
        
        Returns:
            Extracted text from PDF
        """
        try:
            import PyPDF2
            
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            
            return text
            
        except ImportError:
            raise InputError(
                input_type="pdf",
                message="PyPDF2 library is required for PDF processing. Install with: pip install PyPDF2"
            )
        except Exception as e:
            raise InputError(
                input_type="pdf",
                message=f"Failed to extract text from PDF: {str(e)}"
            )
    
    def _process_word_file(self, file_path: Path) -> str:
        """
        Process a Word document file.
        
        Args:
            file_path: Path to the Word file
        
        Returns:
            Extracted text from Word document
        """
        try:
            from docx import Document
            
            doc = Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return text
            
        except ImportError:
            raise InputError(
                input_type="word",
                message="python-docx library is required for Word processing. Install with: pip install python-docx"
            )
        except Exception as e:
            raise InputError(
                input_type="word",
                message=f"Failed to extract text from Word document: {str(e)}"
            )
    
    def _process_html_file(self, file_path: Path) -> str:
        """
        Process an HTML file.
        
        Args:
            file_path: Path to the HTML file
        
        Returns:
            Extracted text from HTML
        """
        try:
            html_content = read_file(file_path)
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            return soup.get_text()
            
        except Exception as e:
            raise InputError(
                input_type="html",
                message=f"Failed to extract text from HTML file: {str(e)}"
            )
    
    def get_supported_formats(self) -> list[str]:
        """
        Get list of supported file formats.
        
        Returns:
            List of supported file extensions
        """
        return ['txt', 'md', 'rst', 'pdf', 'doc', 'docx', 'html', 'htm']
    
    def is_supported_format(self, file_path: Union[str, Path]) -> bool:
        """
        Check if a file format is supported.
        
        Args:
            file_path: Path to the file
        
        Returns:
            True if format is supported
        """
        extension = get_file_extension(file_path)
        return extension in self.get_supported_formats()
    
    def get_text_statistics(self, text: str) -> dict[str, any]:
        """
        Get statistics about processed text.
        
        Args:
            text: Text to analyze
        
        Returns:
            Dictionary with text statistics
        """
        from ..utils.text_utils import extract_sentences, tokenize_text, extract_keywords
        
        sentences = extract_sentences(text)
        tokens = tokenize_text(text, remove_punctuation=True)
        keywords = extract_keywords(text, top_k=10)
        
        return {
            'character_count': len(text),
            'word_count': len(tokens),
            'sentence_count': len(sentences),
            'average_sentence_length': len(tokens) / len(sentences) if sentences else 0,
            'top_keywords': keywords,
            'estimated_reading_time_minutes': len(tokens) / 200  # Assuming 200 words per minute
        }
