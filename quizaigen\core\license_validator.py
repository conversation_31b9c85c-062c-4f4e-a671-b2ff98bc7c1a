"""
Enhanced License Validation System for QuizAIGen
===============================================

Comprehensive license validation with JWT tokens, hardware binding,
and online/offline validation capabilities.
"""

import os
import json
import hashlib
import hmac
import base64
import time
import platform
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging

try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False
    
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

from .exceptions import LicenseError
from ..models.base_model import ModelTier
from ..utils.logger import LoggerMixin


class LicenseValidator(LoggerMixin):
    """
    Enhanced license validation system for QuizAIGen commercial features.
    """
    
    def __init__(self, validation_server: Optional[str] = None):
        """
        Initialize license validator.
        
        Args:
            validation_server: URL of license validation server
        """
        super().__init__()
        
        self.validation_server = validation_server or os.getenv(
            'QUIZAIGEN_LICENSE_SERVER',
            'https://api.quizaigen.com/license'
        )
        
        self.license_key: Optional[str] = None
        self.license_data: Optional[Dict[str, Any]] = None
        self.last_validation: Optional[datetime] = None
        self.validation_cache_duration = timedelta(hours=24)
        self.current_tier = ModelTier.FREE
        
        # Embedded public key for JWT validation (replace with your actual key)
        self.public_key = os.getenv('QUIZAIGEN_PUBLIC_KEY', """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4f5wg5l2hKsTeNem/V41
fGnJm6gOdrj8ym3rFkEjWT2btf02VoBtzS9ze4gRZDRHFpjQqDFt3ShgsHm8xzO
...your actual public key here...
-----END PUBLIC KEY-----""")
        
        # Hardware fingerprinting salt
        self._fingerprint_salt = os.getenv(
            'QUIZAIGEN_FINGERPRINT_SALT', 
            'quizaigen-default-salt'
        ).encode()
        
        # Try to auto-initialize
        self._auto_initialize()
    
    def _auto_initialize(self) -> None:
        """Automatically initialize license from various sources."""
        try:
            # Try environment variable first
            env_license = os.getenv('QUIZAIGEN_LICENSE_KEY')
            if env_license:
                self.set_license_key(env_license)
                return
            
            # Try default license file locations
            default_locations = [
                Path.home() / '.quizaigen' / 'license.key',
                Path('/etc/quizaigen/license.key'),
                Path.cwd() / 'quizaigen_license.key',
                Path.cwd() / 'license.key'
            ]
            
            for location in default_locations:
                if location.exists():
                    try:
                        self.load_license_file(str(location))
                        self.log_info(f"License loaded from {location}")
                        return
                    except Exception as e:
                        self.log_warning(f"Failed to load license from {location}: {e}")
                        continue
            
            # No license found - using free tier
            self.log_info("No license found, using free tier features only")
            
        except Exception as e:
            self.log_warning(f"License auto-initialization failed: {e}")
    
    def set_license_key(self, license_key: str) -> None:
        """Set the license key for validation."""
        self.license_key = license_key.strip()
        self.license_data = None  # Reset cached data
        self.last_validation = None
        
        # Try immediate validation
        try:
            if self.validate():
                self.log_info("License key set and validated successfully")
            else:
                self.log_warning("License key set but validation failed")
        except Exception as e:
            self.log_warning(f"License validation error: {e}")
    
    def load_license_file(self, file_path: str) -> None:
        """Load license key from file."""
        try:
            with open(file_path, 'r') as f:
                content = f.read().strip()
            
            # Support both plain key and JSON format
            try:
                data = json.loads(content)
                self.license_key = data.get('license_key')
                if 'license_data' in data:
                    self.license_data = data['license_data']
            except json.JSONDecodeError:
                self.license_key = content
                
        except FileNotFoundError:
            raise LicenseError("unknown", "license_file", f"License file not found: {file_path}")
        except Exception as e:
            raise LicenseError("unknown", "license_file", f"Failed to load license file: {str(e)}")
    
    def _generate_hardware_fingerprint(self) -> str:
        """Generate hardware fingerprint for license binding."""
        # Collect system information
        system_info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'machine': platform.machine(),
            'node': platform.node(),
            'mac_address': self._get_mac_address()
        }
        
        # Create deterministic fingerprint
        fingerprint_data = json.dumps(system_info, sort_keys=True).encode()
        fingerprint = hmac.new(
            self._fingerprint_salt,
            fingerprint_data,
            hashlib.sha256
        ).hexdigest()
        
        return fingerprint[:16]  # Use first 16 characters
    
    def _get_mac_address(self) -> str:
        """Get MAC address in a consistent format."""
        mac = uuid.getnode()
        return ':'.join(['{:02x}'.format((mac >> elements) & 0xff)
                        for elements in range(0, 2*6, 2)][::-1])

    def _validate_test_license(self, license_key: str) -> Dict[str, Any]:
        """Validate test license format (base64 encoded JSON with signature)."""
        import base64
        import hashlib

        try:
            # Decode base64
            license_bytes = base64.b64decode(license_key.encode())
            signed_license = json.loads(license_bytes.decode())

            license_data = signed_license["data"]
            signature = signed_license["signature"]

            # Verify signature (simple HMAC for testing)
            license_json = json.dumps(license_data, sort_keys=True)
            expected_signature = hashlib.sha256(
                (license_json + "test-secret-key-for-quizaigen-development").encode()
            ).hexdigest()

            if signature != expected_signature:
                raise LicenseError("unknown", "signature_validation", "Invalid license signature")

            # Check expiration
            exp_timestamp = license_data.get("exp", 0)
            if exp_timestamp and datetime.now().timestamp() > exp_timestamp:
                raise LicenseError("unknown", "license_expiry", "License has expired")

            # Check product match
            if license_data.get('product') != 'quizaigen':
                raise LicenseError("unknown", "product_validation", "License not valid for QuizAIGen")

            return license_data

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            raise LicenseError("unknown", "license_format", f"Invalid license format: {str(e)}")

    def _validate_jwt_license(self, license_key: str) -> Dict[str, Any]:
        """Validate JWT-based license key."""
        if not JWT_AVAILABLE:
            raise LicenseError("unknown", "jwt_validation", "JWT library not available for license validation")
        
        try:
            # Decode JWT token
            payload = jwt.decode(
                license_key,
                self.public_key,
                algorithms=['RS256'],
                options={"verify_exp": True}
            )
            
            # Validate required fields
            required_fields = ['customer_id', 'product', 'tier', 'features', 'exp']
            for field in required_fields:
                if field not in payload:
                    raise LicenseError("unknown", "license_validation", f"Missing required field: {field}")

            # Check product match
            if payload.get('product') != 'quizaigen':
                raise LicenseError("unknown", "product_validation", "License not valid for QuizAIGen")

            # Check hardware binding if present
            if 'hardware_fingerprint' in payload:
                current_fingerprint = self._generate_hardware_fingerprint()
                if payload['hardware_fingerprint'] != current_fingerprint:
                    raise LicenseError("unknown", "hardware_binding", "License not valid for this hardware")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise LicenseError("unknown", "license_expiry", "License has expired")
        except jwt.InvalidTokenError as e:
            raise LicenseError("unknown", "token_validation", f"Invalid license token: {str(e)}")
    
    def _validate_online(self, license_key: str) -> Dict[str, Any]:
        """Validate license with online server."""
        if not REQUESTS_AVAILABLE:
            # Fallback to offline validation
            return self._validate_jwt_license(license_key)
        
        try:
            payload = {
                'license_key': license_key,
                'hardware_fingerprint': self._generate_hardware_fingerprint(),
                'product': 'quizaigen',
                'version': '0.1.0'
            }
            
            response = requests.post(
                f"{self.validation_server}/validate",
                json=payload,
                timeout=10,
                headers={'User-Agent': 'QuizAIGen/0.1.0'}
            )
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                raise LicenseError("unknown", "online_validation", "Invalid license key")
            elif response.status_code == 403:
                raise LicenseError("unknown", "online_validation", "License expired or suspended")
            else:
                raise LicenseError("unknown", "online_validation", f"License validation failed: {response.status_code}")
                
        except requests.RequestException:
            # Fallback to offline validation if online fails
            self.log_warning("Online validation failed, falling back to offline")
            try:
                return self._validate_test_license(license_key)
            except Exception:
                return self._validate_jwt_license(license_key)
    
    def validate(self, force_online: bool = False) -> bool:
        """
        Validate the current license.
        
        Args:
            force_online: Force online validation even if cache is valid
            
        Returns:
            bool: True if license is valid
        """
        if not self.license_key:
            self.current_tier = ModelTier.FREE
            return False
        
        # Check cache validity
        if (not force_online and 
            self.last_validation and 
            self.license_data and
            datetime.now() - self.last_validation < self.validation_cache_duration):
            return True
        
        try:
            # Try test license format first (for development)
            try:
                self.license_data = self._validate_test_license(self.license_key)
            except Exception:
                # Try online validation, fallback to offline
                try:
                    if force_online or not self.license_data:
                        self.license_data = self._validate_online(self.license_key)
                    else:
                        # Use cached data but verify JWT signature
                        self.license_data = self._validate_jwt_license(self.license_key)
                except Exception:
                    # Final fallback to JWT validation
                    self.license_data = self._validate_jwt_license(self.license_key)
            
            self.last_validation = datetime.now()
            
            # Update current tier
            tier_str = self.license_data.get('tier', 'free').lower()
            if tier_str == 'enterprise':
                self.current_tier = ModelTier.ENTERPRISE
            elif tier_str == 'premium':
                self.current_tier = ModelTier.PREMIUM
            else:
                self.current_tier = ModelTier.FREE
            
            # Track usage
            self._track_usage()
            
            return True
            
        except LicenseError as e:
            self.log_warning(f"License validation failed: {e}")
            self.license_data = None
            self.last_validation = None
            self.current_tier = ModelTier.FREE
            return False
        except Exception as e:
            self.log_error(f"Unexpected license validation error: {e}")
            self.current_tier = ModelTier.FREE
            return False
    
    def _track_usage(self) -> None:
        """Track package usage for analytics."""
        if not self.license_data or not REQUESTS_AVAILABLE:
            return
        
        try:
            usage_data = {
                'customer_id': self.license_data.get('customer_id'),
                'timestamp': datetime.now().isoformat(),
                'action': 'package_usage',
                'version': '0.1.0',
                'hardware_fingerprint': self._generate_hardware_fingerprint()
            }
            
            # Send usage data asynchronously (non-blocking)
            requests.post(
                f"{self.validation_server}/usage",
                json=usage_data,
                timeout=5
            )
        except Exception:
            # Silently fail usage tracking to not impact functionality
            pass
    
    def get_current_tier(self) -> ModelTier:
        """Get current license tier."""
        return self.current_tier
    
    def has_feature(self, feature: str) -> bool:
        """Check if license includes specific feature."""
        if not self.license_data:
            return False
        
        features = self.license_data.get('features', [])
        return feature in features or 'all' in features
    
    def get_license_info(self) -> Dict[str, Any]:
        """Get current license information."""
        if not self.license_data:
            return {
                "status": "unlicensed",
                "tier": self.current_tier.value,
                "features": []
            }
        
        return {
            "status": "valid",
            "tier": self.current_tier.value,
            "customer_id": self.license_data.get('customer_id'),
            "features": self.license_data.get('features', []),
            "expires": datetime.fromtimestamp(
                self.license_data.get('exp', 0)
            ).isoformat() if self.license_data.get('exp') else None,
            "last_validated": self.last_validation.isoformat() if self.last_validation else None
        }
    
    def get_remaining_days(self) -> Optional[int]:
        """Get number of days remaining on license."""
        if not self.license_data or 'exp' not in self.license_data:
            return None
        
        exp_date = datetime.fromtimestamp(self.license_data['exp'])
        remaining = exp_date - datetime.now()
        
        return max(0, remaining.days)


# Global license validator instance
_global_validator: Optional[LicenseValidator] = None


def get_license_validator() -> LicenseValidator:
    """Get global license validator instance."""
    global _global_validator
    if _global_validator is None:
        _global_validator = LicenseValidator()
    return _global_validator


def require_license(tier: ModelTier = ModelTier.PREMIUM, feature: Optional[str] = None):
    """Decorator to require valid license for function execution."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            validator = get_license_validator()
            
            # Check tier requirement
            current_tier = validator.get_current_tier()
            tier_hierarchy = {
                ModelTier.FREE: 0,
                ModelTier.PREMIUM: 1,
                ModelTier.ENTERPRISE: 2
            }
            
            if tier_hierarchy[current_tier] < tier_hierarchy[tier]:
                raise LicenseError(current_tier.value, "tier_upgrade", f"Feature requires {tier.value} license or higher")

            # Check specific feature if specified
            if feature and not validator.has_feature(feature):
                raise LicenseError(current_tier.value, feature, f"License does not include feature: {feature}")
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator
