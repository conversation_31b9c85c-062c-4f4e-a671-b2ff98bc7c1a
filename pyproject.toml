[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "quizaigen"
version = "0.1.0"
description = "AI-Powered Question Generation Library"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
keywords = [
    "ai", "nlp", "question-generation", "quiz", "education", 
    "machine-learning", "transformers", "bert", "t5", "gpt"
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Education",
    "Topic :: Text Processing :: Linguistic"
]
requires-python = ">=3.8"
dependencies = [
    "torch>=1.9.0",
    "transformers>=4.20.0",
    "tokenizers>=0.12.0",
    "spacy>=3.4.0",
    "nltk>=3.7",
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "scikit-learn>=1.0.0",
    "requests>=2.25.0",
    "beautifulsoup4>=4.9.0",
    "PyPDF2>=2.0.0",
    "python-docx>=0.8.11",
    "pydantic>=1.8.0",
    "tqdm>=4.62.0",
    "sense2vec>=2.0.0",
    "gensim>=4.1.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
    "pre-commit>=2.15.0",
    "isort>=5.9.0"
]
docs = [
    "sphinx>=4.0.0",
    "sphinx-rtd-theme>=0.5.0",
    "myst-parser>=0.15.0",
    "sphinx-autodoc-typehints>=1.12.0"
]
examples = [
    "jupyter>=1.0.0",
    "matplotlib>=3.4.0",
    "seaborn>=0.11.0"
]

[project.urls]
Homepage = "https://github.com/quizaigen/quizaigen"
Documentation = "https://quizaigen.readthedocs.io"
Repository = "https://github.com/quizaigen/quizaigen.git"
"Bug Tracker" = "https://github.com/quizaigen/quizaigen/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["quizaigen*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
quizaigen = ["*.json", "*.yaml", "*.yml", "data/*"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["quizaigen"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests"
]

[tool.coverage.run]
source = ["quizaigen"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]
