"""
Model Cache System for QuizAIGen

Provides intelligent caching for AI models and their outputs to improve performance.
Supports different caching strategies based on tier and usage patterns.
"""

import os
import json
import hashlib
import pickle
import time
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from threading import Lock
import logging

from .base_model import ModelOutput, ModelTier
from ..utils.logger import LoggerMixin


@dataclass
class CacheEntry:
    """Cache entry for model outputs."""
    key: str
    output: ModelOutput
    timestamp: float
    access_count: int
    tier: ModelTier
    model_name: str
    
    def is_expired(self, ttl_seconds: int) -> bool:
        """Check if cache entry is expired."""
        return time.time() - self.timestamp > ttl_seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "key": self.key,
            "output": asdict(self.output),
            "timestamp": self.timestamp,
            "access_count": self.access_count,
            "tier": self.tier.value,
            "model_name": self.model_name
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        """Create from dictionary."""
        output_data = data["output"]
        output = ModelOutput(**output_data)
        
        return cls(
            key=data["key"],
            output=output,
            timestamp=data["timestamp"],
            access_count=data["access_count"],
            tier=ModelTier(data["tier"]),
            model_name=data["model_name"]
        )


class ModelCache(LoggerMixin):
    """
    Intelligent cache system for AI model outputs.
    
    Features:
    - Memory and disk caching
    - TTL-based expiration
    - LRU eviction policy
    - Tier-based cache limits
    - Thread-safe operations
    """
    
    def __init__(self,
                 cache_dir: Optional[str] = None,
                 max_memory_entries: int = 1000,
                 max_disk_entries: int = 10000,
                 default_ttl: int = 3600,  # 1 hour
                 enable_disk_cache: bool = True):
        """
        Initialize model cache.

        Args:
            cache_dir: Directory for disk cache
            max_memory_entries: Maximum entries in memory cache
            max_disk_entries: Maximum entries in disk cache
            default_ttl: Default TTL in seconds
            enable_disk_cache: Whether to enable disk caching
        """
        super().__init__()  # Initialize LoggerMixin
        self.cache_dir = Path(cache_dir) if cache_dir else Path.home() / ".quizaigen" / "cache"
        self.max_memory_entries = max_memory_entries
        self.max_disk_entries = max_disk_entries
        self.default_ttl = default_ttl
        self.enable_disk_cache = enable_disk_cache

        # Memory cache
        self._memory_cache: Dict[str, CacheEntry] = {}
        self._access_order: List[str] = []  # For LRU
        self._lock = Lock()

        # Initialize cache directory
        if self.enable_disk_cache:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            self._load_disk_cache_index()

        self.log_info(f"Initialized ModelCache with {max_memory_entries} memory entries")
    
    def _generate_cache_key(self, input_text: str, model_name: str, **kwargs) -> str:
        """
        Generate cache key for input.
        
        Args:
            input_text: Input text
            model_name: Model name
            **kwargs: Additional parameters
            
        Returns:
            Cache key
        """
        # Create deterministic key from input and parameters
        key_data = {
            "text": input_text,
            "model": model_name,
            **kwargs
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get(self, input_text: str, model_name: str, **kwargs) -> Optional[ModelOutput]:
        """
        Get cached model output.
        
        Args:
            input_text: Input text
            model_name: Model name
            **kwargs: Additional parameters
            
        Returns:
            Cached model output or None if not found
        """
        cache_key = self._generate_cache_key(input_text, model_name, **kwargs)
        
        with self._lock:
            # Check memory cache first
            if cache_key in self._memory_cache:
                entry = self._memory_cache[cache_key]
                
                # Check if expired
                if entry.is_expired(self.default_ttl):
                    self._remove_from_memory(cache_key)
                    return None
                
                # Update access
                entry.access_count += 1
                self._update_access_order(cache_key)
                
                self.log_debug(f"Cache hit (memory): {cache_key[:8]}")
                return entry.output
            
            # Check disk cache if enabled
            if self.enable_disk_cache:
                disk_entry = self._load_from_disk(cache_key)
                if disk_entry and not disk_entry.is_expired(self.default_ttl):
                    # Move to memory cache
                    self._add_to_memory(disk_entry)
                    self.log_debug(f"Cache hit (disk): {cache_key[:8]}")
                    return disk_entry.output
        
        self.log_debug(f"Cache miss: {cache_key[:8]}")
        return None
    
    def put(self, input_text: str, model_name: str, output: ModelOutput, 
            tier: ModelTier = ModelTier.FREE, **kwargs) -> None:
        """
        Store model output in cache.
        
        Args:
            input_text: Input text
            model_name: Model name
            output: Model output to cache
            tier: Model tier
            **kwargs: Additional parameters
        """
        cache_key = self._generate_cache_key(input_text, model_name, **kwargs)
        
        entry = CacheEntry(
            key=cache_key,
            output=output,
            timestamp=time.time(),
            access_count=1,
            tier=tier,
            model_name=model_name
        )
        
        with self._lock:
            self._add_to_memory(entry)
            
            # Save to disk if enabled
            if self.enable_disk_cache:
                self._save_to_disk(entry)
        
        self.log_debug(f"Cached output: {cache_key[:8]}")
    
    def _add_to_memory(self, entry: CacheEntry) -> None:
        """Add entry to memory cache with LRU eviction."""
        # Remove if already exists
        if entry.key in self._memory_cache:
            self._remove_from_memory(entry.key)
        
        # Check if we need to evict
        if len(self._memory_cache) >= self.max_memory_entries:
            self._evict_lru()
        
        # Add new entry
        self._memory_cache[entry.key] = entry
        self._access_order.append(entry.key)
    
    def _remove_from_memory(self, cache_key: str) -> None:
        """Remove entry from memory cache."""
        if cache_key in self._memory_cache:
            del self._memory_cache[cache_key]
        
        if cache_key in self._access_order:
            self._access_order.remove(cache_key)
    
    def _update_access_order(self, cache_key: str) -> None:
        """Update access order for LRU."""
        if cache_key in self._access_order:
            self._access_order.remove(cache_key)
        self._access_order.append(cache_key)
    
    def _evict_lru(self) -> None:
        """Evict least recently used entry."""
        if self._access_order:
            lru_key = self._access_order[0]
            self._remove_from_memory(lru_key)
            self.log_debug(f"Evicted LRU entry: {lru_key[:8]}")
    
    def _load_from_disk(self, cache_key: str) -> Optional[CacheEntry]:
        """Load cache entry from disk."""
        if not self.enable_disk_cache:
            return None
        
        cache_file = self.cache_dir / f"{cache_key}.cache"
        if not cache_file.exists():
            return None
        
        try:
            with open(cache_file, 'rb') as f:
                data = pickle.load(f)
                return CacheEntry.from_dict(data)
        except Exception as e:
            self.log_warning(f"Failed to load cache entry {cache_key[:8]}: {e}")
            return None
    
    def _save_to_disk(self, entry: CacheEntry) -> None:
        """Save cache entry to disk."""
        if not self.enable_disk_cache:
            return
        
        cache_file = self.cache_dir / f"{entry.key}.cache"
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(entry.to_dict(), f)
        except Exception as e:
            self.log_warning(f"Failed to save cache entry {entry.key[:8]}: {e}")
    
    def _load_disk_cache_index(self) -> None:
        """Load disk cache index for management."""
        # This would implement disk cache indexing for better management
        pass
    
    def clear_cache(self, tier: Optional[ModelTier] = None) -> None:
        """
        Clear cache entries.
        
        Args:
            tier: If specified, only clear entries for this tier
        """
        with self._lock:
            if tier is None:
                # Clear all
                self._memory_cache.clear()
                self._access_order.clear()
                
                if self.enable_disk_cache:
                    for cache_file in self.cache_dir.glob("*.cache"):
                        cache_file.unlink()
                
                self.log_info("Cleared all cache entries")
            else:
                # Clear specific tier
                keys_to_remove = [
                    key for key, entry in self._memory_cache.items()
                    if entry.tier == tier
                ]
                
                for key in keys_to_remove:
                    self._remove_from_memory(key)
                
                self.log_info(f"Cleared {len(keys_to_remove)} cache entries for tier {tier.value}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Cache statistics
        """
        with self._lock:
            memory_size = len(self._memory_cache)
            
            # Count disk entries if enabled
            disk_size = 0
            if self.enable_disk_cache:
                disk_size = len(list(self.cache_dir.glob("*.cache")))
            
            # Calculate tier distribution
            tier_counts = {}
            for entry in self._memory_cache.values():
                tier_name = entry.tier.value
                tier_counts[tier_name] = tier_counts.get(tier_name, 0) + 1
            
            return {
                "memory_entries": memory_size,
                "disk_entries": disk_size,
                "max_memory_entries": self.max_memory_entries,
                "max_disk_entries": self.max_disk_entries,
                "tier_distribution": tier_counts,
                "cache_dir": str(self.cache_dir),
                "disk_cache_enabled": self.enable_disk_cache
            }
