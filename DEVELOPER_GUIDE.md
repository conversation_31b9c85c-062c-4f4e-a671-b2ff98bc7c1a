# QuizAIGen Developer Guide

A comprehensive guide for developers on how to use the QuizAIGen AI-powered question generation library.

## Table of Contents

1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Quick Start](#quick-start)
4. [Core Components](#core-components)
5. [Question Generation](#question-generation)
6. [Input Processing](#input-processing)
7. [Export and Output](#export-and-output)
8. [Advanced Features](#advanced-features)
9. [Configuration](#configuration)
10. [Performance Optimization](#performance-optimization)
11. [Multi-language Support](#multi-language-support)
12. [Error Handling](#error-handling)
13. [Best Practices](#best-practices)
14. [Examples](#examples)
15. [API Reference](#api-reference)
16. [Troubleshooting](#troubleshooting)

## Introduction

QuizAIGen is a powerful AI-driven library for generating educational questions from various text sources. It supports multiple question types, languages, and export formats, making it ideal for educators, content creators, and e-learning platforms.

### Key Features

- **Multiple Question Types**: <PERSON>Q, <PERSON><PERSON>an, <PERSON>Q, Fill-in-the-blank, Short Answer
- **Multi-language Support**: 50+ languages with cultural adaptation
- **Flexible Input**: Text, PDF, DOCX, URLs
- **Various Export Formats**: JSON, CSV, XML, QTI, Moodle, Aiken, GIFT
- **AI-Powered Quality**: Advanced NLP models for high-quality questions
- **Performance Optimized**: GPU acceleration and memory optimization
- **Batch Processing**: Handle large volumes efficiently

## Installation

### Basic Installation

```bash
pip install quizaigen
```

### Development Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/quizaigen.git
cd quizaigen

# Install in development mode
pip install -e ".[dev]"
```

### Optional Dependencies

```bash
# For documentation generation
pip install "quizaigen[docs]"

# For examples and notebooks
pip install "quizaigen[examples]"

# Install all optional dependencies
pip install "quizaigen[dev,docs,examples]"
```

### System Requirements

- Python 3.8+
- 4GB+ RAM (8GB+ recommended for large documents)
- GPU support (optional, for acceleration)

## Quick Start

### Basic Usage

```python
from quizaigen import QuestionGenerator

# Initialize the generator
generator = QuestionGenerator()

# Sample text
text = """
Artificial Intelligence (AI) is a branch of computer science that aims to create 
intelligent machines that work and react like humans. Machine learning is a subset 
of AI that provides systems the ability to automatically learn and improve from 
experience without being explicitly programmed.
"""

# Generate questions
questions = generator.generate_mixed(text, num_questions=5)

# Display results
for i, question in enumerate(questions, 1):
    print(f"Q{i}: {question['question']}")
    print(f"Type: {question['type']}")
    print(f"Answer: {question['answer']}")
    print()
```

### Export Questions

```python
from quizaigen import ExportManager

# Initialize export manager
exporter = ExportManager()

# Export to different formats
exporter.export_questions(questions, "quiz.json", format="json")
exporter.export_questions(questions, "quiz.csv", format="csv")
exporter.export_questions(questions, "quiz.xml", format="xml")
```

## Core Components

### QuestionGenerator

The main class for generating questions from text.

```python
from quizaigen import QuestionGenerator

# Initialize with default settings
generator = QuestionGenerator()

# Initialize with custom configuration
from quizaigen.core import Config

config = Config(
    model_name="distilbert-base-uncased",
    max_length=512,
    temperature=0.7
)
generator = QuestionGenerator(config=config)
```

### BatchProcessor

For processing multiple documents efficiently.

```python
from quizaigen import BatchProcessor

# Initialize batch processor
batch_processor = BatchProcessor(
    max_workers=4,
    chunk_size=1000
)

# Process multiple texts
texts = ["Text 1...", "Text 2...", "Text 3..."]
results = batch_processor.process_texts(texts, questions_per_text=3)
```

### ExportManager

Handles exporting questions to various formats.

```python
from quizaigen import ExportManager

exporter = ExportManager()

# Available formats
print(exporter.get_supported_formats())
# ['json', 'csv', 'xml', 'qti', 'moodle', 'aiken', 'respondus', 'gift']
```

## Question Generation

### Multiple Choice Questions (MCQ)

```python
# Generate MCQ questions
mcq_questions = generator.generate_mcq(
    text=sample_text,
    num_questions=5,
    num_options=4,
    difficulty="medium"
)

# Example output structure
{
    "question": "What is the main purpose of artificial intelligence?",
    "type": "mcq",
    "options": [
        "To replace human workers",
        "To create intelligent machines that work like humans",
        "To process large amounts of data",
        "To automate simple tasks"
    ],
    "answer": "To create intelligent machines that work like humans",
    "confidence": 0.89,
    "difficulty": "medium",
    "bloom_level": "understanding"
}
```

### Boolean Questions

```python
# Generate True/False questions
boolean_questions = generator.generate_boolean(
    text=sample_text,
    num_questions=3,
    difficulty="easy"
)

# Example output
{
    "question": "Machine learning is a subset of artificial intelligence.",
    "type": "boolean",
    "answer": True,
    "confidence": 0.95,
    "explanation": "The text explicitly states that machine learning is a subset of AI."
}
```

### Fill-in-the-Blank Questions

```python
# Generate fill-in-the-blank questions
fill_blank_questions = generator.generate_fill_blank(
    text=sample_text,
    num_questions=4,
    blank_type="key_terms"  # or "phrases", "numbers"
)

# Example output
{
    "question": "______ is a branch of computer science that aims to create intelligent machines.",
    "type": "fill_blank",
    "answer": "Artificial Intelligence",
    "alternatives": ["AI", "Artificial intelligence"],
    "context": "The sentence discusses the definition of AI."
}
```

### Short Answer Questions

```python
# Generate short answer questions
short_answer_questions = generator.generate_short_answer(
    text=sample_text,
    num_questions=3,
    answer_length="brief"  # or "detailed"
)

# Example output
{
    "question": "Explain the relationship between AI and machine learning.",
    "type": "short_answer",
    "sample_answer": "Machine learning is a subset of AI that enables systems to learn automatically from experience.",
    "key_points": ["subset relationship", "automatic learning", "experience-based"],
    "bloom_level": "analysis"
}
```

### Mixed Question Generation

```python
# Generate a mix of question types
mixed_questions = generator.generate_mixed(
    text=sample_text,
    num_questions=10,
    question_types=["mcq", "boolean", "fill_blank"],
    distribution={"mcq": 0.5, "boolean": 0.3, "fill_blank": 0.2}
)
```

## Input Processing

### Text Input

```python
# Direct text input
text = "Your educational content here..."
questions = generator.generate_mcq(text, num_questions=5)
```

### Document Processing

```python
from quizaigen.inputs import DocumentProcessor

# Initialize document processor
doc_processor = DocumentProcessor()

# Process PDF files
pdf_text = doc_processor.process_pdf("document.pdf")
questions = generator.generate_mixed(pdf_text, num_questions=8)

# Process DOCX files
docx_text = doc_processor.process_docx("document.docx")
questions = generator.generate_boolean(docx_text, num_questions=5)

# Process multiple documents
documents = ["doc1.pdf", "doc2.docx", "doc3.txt"]
all_questions = []

for doc_path in documents:
    text = doc_processor.process_document(doc_path)
    doc_questions = generator.generate_mixed(text, num_questions=3)
    all_questions.extend(doc_questions)
```

### URL Processing

```python
from quizaigen.inputs import URLProcessor

# Initialize URL processor
url_processor = URLProcessor()

# Process web content
url = "https://example.com/article"
web_text = url_processor.process_url(url)
questions = generator.generate_mcq(web_text, num_questions=6)

# Process multiple URLs
urls = [
    "https://example.com/article1",
    "https://example.com/article2"
]

for url in urls:
    try:
        text = url_processor.process_url(url)
        url_questions = generator.generate_mixed(text, num_questions=4)
        print(f"Generated {len(url_questions)} questions from {url}")
    except Exception as e:
        print(f"Error processing {url}: {e}")
```

## Export and Output

### JSON Export

```python
# Export to JSON with custom formatting
exporter.export_questions(
    questions,
    "quiz.json",
    format="json",
    indent=2,
    include_metadata=True
)

# JSON structure
{
    "metadata": {
        "generated_at": "2024-01-15T10:30:00Z",
        "total_questions": 10,
        "question_types": {"mcq": 5, "boolean": 3, "fill_blank": 2},
        "average_confidence": 0.87
    },
    "questions": [...]
}
```

### CSV Export

```python
# Export to CSV
exporter.export_questions(
    questions,
    "quiz.csv",
    format="csv",
    include_headers=True
)

# CSV columns: question, type, answer, options, confidence, difficulty
```

### Learning Management System Formats

```python
# Export for Moodle
exporter.export_questions(
    questions,
    "moodle_quiz.xml",
    format="moodle",
    category="AI Fundamentals"
)

# Export QTI format
exporter.export_questions(
    questions,
    "qti_quiz.xml",
    format="qti",
    version="2.1"
)

# Export GIFT format
exporter.export_questions(
    questions,
    "gift_quiz.txt",
    format="gift"
)
```

## Advanced Features

### Custom Model Training

```python
from quizaigen.models import CustomModelTrainer

# Prepare training data
training_data = [
    {
        "text": "Sample educational text...",
        "questions": [
            {"question": "What is...", "answer": "...", "type": "mcq"}
        ]
    }
]

# Train custom model
trainer = CustomModelTrainer(
    base_model="distilbert-base-uncased",
    output_dir="./custom_model"
)

trainer.train(
    training_data=training_data,
    epochs=3,
    batch_size=16,
    learning_rate=2e-5
)

# Use custom model
generator = QuestionGenerator(model_path="./custom_model")
```

### GPU Acceleration

```python
from quizaigen.utils import GPUAccelerator

# Enable GPU acceleration
accelerator = GPUAccelerator()
if accelerator.is_available():
    generator = QuestionGenerator(
        device="cuda",
        batch_size=32  # Larger batch size for GPU
    )
    print(f"Using GPU: {accelerator.get_device_name()}")
else:
    generator = QuestionGenerator(device="cpu")
    print("Using CPU")
```

### Memory Optimization

```python
from quizaigen.utils import MemoryOptimizer

# Optimize memory usage for large documents
optimizer = MemoryOptimizer()

with optimizer.optimize_context():
    # Process large document
    large_text = doc_processor.process_pdf("large_document.pdf")
    
    # Generate questions in chunks
    all_questions = []
    text_chunks = optimizer.chunk_text(large_text, chunk_size=2000)
    
    for chunk in text_chunks:
        chunk_questions = generator.generate_mixed(chunk, num_questions=3)
        all_questions.extend(chunk_questions)
        
        # Clear cache periodically
        optimizer.clear_cache()

print(f"Generated {len(all_questions)} questions from large document")
```

## Configuration

### Basic Configuration

```python
from quizaigen.core import Config

# Create custom configuration
config = Config(
    # Model settings
    model_name="bert-base-uncased",
    max_length=512,
    temperature=0.7,
    
    # Generation settings
    min_confidence=0.7,
    max_retries=3,
    
    # Quality settings
    enable_quality_filter=True,
    min_difficulty_score=0.3,
    
    # Performance settings
    batch_size=16,
    num_workers=4
)

generator = QuestionGenerator(config=config)
```

### Configuration from File

```python
# Load configuration from YAML file
config = Config.from_file("config.yaml")

# config.yaml example:
# model:
#   name: "distilbert-base-uncased"
#   max_length: 512
#   temperature: 0.8
# generation:
#   min_confidence: 0.75
#   enable_quality_filter: true
# performance:
#   batch_size: 32
#   device: "auto"
```

### Environment Variables

```bash
# Set environment variables
export QUIZAIGEN_MODEL_NAME="bert-base-uncased"
export QUIZAIGEN_DEVICE="cuda"
export QUIZAIGEN_BATCH_SIZE="32"
export QUIZAIGEN_CACHE_DIR="./cache"
```

```python
# Configuration will automatically use environment variables
generator = QuestionGenerator()  # Uses env vars
```

## Performance Optimization

### Batch Processing

```python
from quizaigen import BatchProcessor

# Configure batch processor
batch_processor = BatchProcessor(
    max_workers=8,
    chunk_size=1500,
    enable_gpu=True
)

# Process multiple documents
document_paths = ["doc1.pdf", "doc2.docx", "doc3.txt"]
results = batch_processor.process_documents(
    document_paths,
    questions_per_document=5,
    question_types=["mcq", "boolean"]
)

# Results structure
{
    "doc1.pdf": {
        "questions": [...],
        "processing_time": 2.3,
        "status": "success"
    },
    "doc2.docx": {
        "questions": [...],
        "processing_time": 1.8,
        "status": "success"
    }
}
```

### Caching

```python
from quizaigen.models import ModelCache

# Enable model caching
cache = ModelCache(cache_dir="./model_cache")
generator = QuestionGenerator(cache=cache)

# Models will be cached automatically
# Subsequent runs will be faster
```

### Performance Monitoring

```python
from quizaigen.utils import PerformanceMonitor

# Monitor performance
monitor = PerformanceMonitor()

with monitor.track("question_generation"):
    questions = generator.generate_mixed(text, num_questions=10)

# Get performance metrics
metrics = monitor.get_metrics()
print(f"Generation time: {metrics['question_generation']['duration']:.2f}s")
print(f"Memory usage: {metrics['question_generation']['memory_mb']:.1f}MB")
```

## Multi-language Support

### Language Detection

```python
from quizaigen.core import LanguageDetector

# Detect language automatically
detector = LanguageDetector()
language = detector.detect("Bonjour, comment allez-vous?")
print(f"Detected language: {language}")  # Output: fr
```

### Multi-language Generation

```python
from quizaigen.generators import MultilingualGenerator

# Initialize multilingual generator
multilingual_gen = MultilingualGenerator()

# Generate questions in specific language
french_text = "L'intelligence artificielle est une branche de l'informatique..."
french_questions = multilingual_gen.generate_mcq(
    text=french_text,
    language="fr",
    num_questions=3
)

# Generate questions with translation
english_questions = multilingual_gen.generate_with_translation(
    text=french_text,
    source_language="fr",
    target_language="en",
    num_questions=3
)
```

### Supported Languages

```python
# Get list of supported languages
supported_langs = multilingual_gen.get_supported_languages()
print(f"Supported languages: {len(supported_langs)}")

# Popular languages include:
# en (English), es (Spanish), fr (French), de (German), 
# zh (Chinese), ja (Japanese), ar (Arabic), hi (Hindi), etc.
```

### Cultural Adaptation

```python
# Generate culturally adapted questions
cultural_questions = multilingual_gen.generate_with_cultural_adaptation(
    text=sample_text,
    language="ja",
    culture="japanese",
    num_questions=5
)

# Questions will be adapted for Japanese cultural context
```

## Error Handling

### Exception Types

```python
from quizaigen.core.exceptions import (
    QuizAIGenError,
    ModelLoadError,
    ProcessingError,
    ValidationError,
    ExportError
)

try:
    questions = generator.generate_mcq(text, num_questions=5)
except ModelLoadError as e:
    print(f"Model loading failed: {e}")
except ProcessingError as e:
    print(f"Text processing failed: {e}")
except ValidationError as e:
    print(f"Input validation failed: {e}")
except QuizAIGenError as e:
    print(f"General error: {e}")
```

### Robust Error Handling

```python
def generate_questions_safely(text, num_questions=5, max_retries=3):
    """Generate questions with robust error handling."""
    
    for attempt in range(max_retries):
        try:
            # Validate input
            if not text or len(text.strip()) < 50:
                raise ValidationError("Text too short for question generation")
            
            # Generate questions
            questions = generator.generate_mixed(
                text=text,
                num_questions=num_questions
            )
            
            # Validate output
            if not questions:
                raise ProcessingError("No questions generated")
            
            return questions
            
        except (ProcessingError, ModelLoadError) as e:
            print(f"Attempt {attempt + 1} failed: {e}")
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)  # Exponential backoff
        
        except ValidationError:
            # Don't retry validation errors
            raise
    
    return []

# Usage
try:
    questions = generate_questions_safely(sample_text)
    print(f"Successfully generated {len(questions)} questions")
except Exception as e:
    print(f"Failed to generate questions: {e}")
```

## Best Practices

### Input Preparation

```python
# Clean and prepare text
def prepare_text(raw_text):
    """Prepare text for optimal question generation."""
    
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', raw_text.strip())
    
    # Remove special characters that might interfere
    text = re.sub(r'[^\w\s.,!?;:()-]', '', text)
    
    # Ensure minimum length
    if len(text) < 100:
        raise ValueError("Text too short for meaningful questions")
    
    # Split into sentences for better processing
    sentences = text.split('. ')
    if len(sentences) < 3:
        raise ValueError("Text should contain at least 3 sentences")
    
    return text

# Usage
clean_text = prepare_text(raw_input)
questions = generator.generate_mcq(clean_text, num_questions=5)
```

### Question Quality Optimization

```python
# Filter and improve question quality
def filter_high_quality_questions(questions, min_confidence=0.8):
    """Filter questions based on quality metrics."""
    
    high_quality = []
    
    for question in questions:
        # Check confidence score
        if question.get('confidence', 0) < min_confidence:
            continue
        
        # Check question length (not too short or too long)
        q_text = question['question']
        if len(q_text.split()) < 5 or len(q_text.split()) > 25:
            continue
        
        # Check for question marks
        if not q_text.strip().endswith('?'):
            continue
        
        # For MCQ, check option quality
        if question['type'] == 'mcq':
            options = question.get('options', [])
            if len(options) < 3 or len(set(options)) != len(options):
                continue
        
        high_quality.append(question)
    
    return high_quality

# Usage
all_questions = generator.generate_mixed(text, num_questions=15)
quality_questions = filter_high_quality_questions(all_questions)
print(f"Filtered {len(quality_questions)} high-quality questions from {len(all_questions)}")
```

### Memory Management

```python
# Efficient processing of large documents
def process_large_document(file_path, questions_per_chunk=3):
    """Process large documents efficiently."""
    
    doc_processor = DocumentProcessor()
    optimizer = MemoryOptimizer()
    
    # Process document in chunks
    text = doc_processor.process_document(file_path)
    chunks = optimizer.chunk_text(text, chunk_size=2000, overlap=200)
    
    all_questions = []
    
    for i, chunk in enumerate(chunks):
        print(f"Processing chunk {i+1}/{len(chunks)}...")
        
        try:
            chunk_questions = generator.generate_mixed(
                chunk, 
                num_questions=questions_per_chunk
            )
            all_questions.extend(chunk_questions)
            
        except Exception as e:
            print(f"Error processing chunk {i+1}: {e}")
            continue
        
        # Clear memory periodically
        if i % 5 == 0:
            optimizer.clear_cache()
    
    # Remove duplicates
    unique_questions = remove_duplicate_questions(all_questions)
    
    return unique_questions

def remove_duplicate_questions(questions):
    """Remove duplicate questions based on similarity."""
    from quizaigen.quality import DuplicateDetector
    
    detector = DuplicateDetector(similarity_threshold=0.8)
    return detector.remove_duplicates(questions)
```

## Examples

### Example 1: Educational Content Processing

```python
#!/usr/bin/env python3
"""Process educational PDF and generate quiz questions."""

from quizaigen import QuestionGenerator, ExportManager
from quizaigen.inputs import DocumentProcessor
from quizaigen.quality import ContentFilter

def create_quiz_from_pdf(pdf_path, output_path, num_questions=10):
    """Create a quiz from a PDF document."""
    
    # Initialize components
    doc_processor = DocumentProcessor()
    generator = QuestionGenerator()
    exporter = ExportManager()
    content_filter = ContentFilter()
    
    try:
        # Process PDF
        print(f"Processing PDF: {pdf_path}")
        text = doc_processor.process_pdf(pdf_path)
        
        # Filter content for educational value
        filtered_text = content_filter.filter_educational_content(text)
        
        # Generate questions
        print(f"Generating {num_questions} questions...")
        questions = generator.generate_mixed(
            text=filtered_text,
            num_questions=num_questions,
            question_types=["mcq", "boolean", "short_answer"],
            distribution={"mcq": 0.6, "boolean": 0.2, "short_answer": 0.2}
        )
        
        # Export to multiple formats
        base_name = output_path.rsplit('.', 1)[0]
        
        exporter.export_questions(questions, f"{base_name}.json", "json")
        exporter.export_questions(questions, f"{base_name}.csv", "csv")
        exporter.export_questions(questions, f"{base_name}_moodle.xml", "moodle")
        
        print(f"Successfully generated {len(questions)} questions")
        print(f"Exported to: {base_name}.[json|csv|xml]")
        
        return questions
        
    except Exception as e:
        print(f"Error creating quiz: {e}")
        return []

if __name__ == "__main__":
    questions = create_quiz_from_pdf(
        "textbook_chapter.pdf",
        "chapter_quiz",
        num_questions=15
    )
```

### Example 2: Multi-language Quiz Generation

```python
#!/usr/bin/env python3
"""Generate multilingual quiz questions."""

from quizaigen.generators import MultilingualGenerator
from quizaigen import ExportManager

def create_multilingual_quiz(text, languages, questions_per_language=5):
    """Create quiz questions in multiple languages."""
    
    multilingual_gen = MultilingualGenerator()
    exporter = ExportManager()
    
    all_questions = {}
    
    for lang in languages:
        print(f"Generating questions in {lang}...")
        
        try:
            questions = multilingual_gen.generate_mcq(
                text=text,
                language=lang,
                num_questions=questions_per_language,
                cultural_adaptation=True
            )
            
            all_questions[lang] = questions
            
            # Export each language separately
            exporter.export_questions(
                questions,
                f"quiz_{lang}.json",
                "json"
            )
            
        except Exception as e:
            print(f"Error generating questions for {lang}: {e}")
            continue
    
    # Create combined export
    combined_questions = []
    for lang, questions in all_questions.items():
        for q in questions:
            q['language'] = lang
        combined_questions.extend(questions)
    
    exporter.export_questions(
        combined_questions,
        "multilingual_quiz.json",
        "json"
    )
    
    return all_questions

# Usage
sample_text = """
Climate change refers to long-term shifts in global temperatures and weather patterns.
While climate variations are natural, scientific evidence shows that human activities
have been the main driver of climate change since the 1800s.
"""

languages = ["en", "es", "fr", "de", "zh"]
results = create_multilingual_quiz(sample_text, languages, 3)

for lang, questions in results.items():
    print(f"{lang}: {len(questions)} questions generated")
```

### Example 3: Batch Processing with Quality Control

```python
#!/usr/bin/env python3
"""Batch process multiple documents with quality control."""

from quizaigen import BatchProcessor, QuestionGenerator
from quizaigen.quality import QualityController
from quizaigen.utils import PerformanceMonitor
import os
from pathlib import Path

def batch_process_with_quality_control(input_dir, output_dir, questions_per_doc=5):
    """Process multiple documents with quality control."""
    
    # Initialize components
    batch_processor = BatchProcessor(max_workers=4)
    quality_controller = QualityController(
        min_confidence=0.8,
        enable_bloom_taxonomy=True,
        enable_difficulty_assessment=True
    )
    monitor = PerformanceMonitor()
    
    # Find all documents
    input_path = Path(input_dir)
    documents = list(input_path.glob("*.pdf")) + list(input_path.glob("*.docx"))
    
    print(f"Found {len(documents)} documents to process")
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    results = {}
    
    with monitor.track("batch_processing"):
        # Process documents in batches
        batch_results = batch_processor.process_documents(
            [str(doc) for doc in documents],
            questions_per_document=questions_per_doc * 2,  # Generate extra for filtering
            question_types=["mcq", "boolean", "fill_blank"]
        )
        
        # Apply quality control
        for doc_path, result in batch_results.items():
            if result["status"] == "success":
                questions = result["questions"]
                
                # Apply quality filters
                quality_questions = quality_controller.filter_questions(
                    questions,
                    target_count=questions_per_doc
                )
                
                # Save results
                doc_name = Path(doc_path).stem
                output_file = output_path / f"{doc_name}_quiz.json"
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    import json
                    json.dump({
                        "source_document": doc_path,
                        "generated_at": monitor.get_timestamp(),
                        "questions": quality_questions,
                        "quality_metrics": quality_controller.get_metrics(quality_questions)
                    }, f, indent=2, ensure_ascii=False)
                
                results[doc_path] = {
                    "questions_generated": len(quality_questions),
                    "output_file": str(output_file),
                    "processing_time": result["processing_time"]
                }
                
                print(f"✅ {doc_name}: {len(quality_questions)} questions")
            else:
                print(f"❌ {Path(doc_path).name}: {result.get('error', 'Unknown error')}")
    
    # Print summary
    metrics = monitor.get_metrics()
    total_time = metrics["batch_processing"]["duration"]
    total_questions = sum(r["questions_generated"] for r in results.values() if "questions_generated" in r)
    
    print(f"\n📊 Processing Summary:")
    print(f"   Documents processed: {len(results)}")
    print(f"   Total questions: {total_questions}")
    print(f"   Total time: {total_time:.2f}s")
    print(f"   Average time per document: {total_time/len(documents):.2f}s")
    
    return results

if __name__ == "__main__":
    results = batch_process_with_quality_control(
        input_dir="./documents",
        output_dir="./generated_quizzes",
        questions_per_doc=8
    )
```

## API Reference

### QuestionGenerator

```python
class QuestionGenerator:
    """Main class for generating questions from text."""
    
    def __init__(self, config=None, model_path=None, device="auto"):
        """Initialize the question generator."""
        pass
    
    def generate_mcq(self, text, num_questions=5, num_options=4, difficulty="medium"):
        """Generate multiple choice questions."""
        pass
    
    def generate_boolean(self, text, num_questions=5, difficulty="medium"):
        """Generate true/false questions."""
        pass
    
    def generate_fill_blank(self, text, num_questions=5, blank_type="key_terms"):
        """Generate fill-in-the-blank questions."""
        pass
    
    def generate_short_answer(self, text, num_questions=5, answer_length="brief"):
        """Generate short answer questions."""
        pass
    
    def generate_mixed(self, text, num_questions=10, question_types=None, distribution=None):
        """Generate a mix of question types."""
        pass
    
    def get_available_question_types(self):
        """Get list of available question types."""
        return ["mcq", "boolean", "fill_blank", "short_answer"]
    
    def get_statistics(self, questions):
        """Get statistics about generated questions."""
        pass
```

### ExportManager

```python
class ExportManager:
    """Handles exporting questions to various formats."""
    
    def export_questions(self, questions, output_path, format="json", **kwargs):
        """Export questions to specified format."""
        pass
    
    def get_supported_formats(self):
        """Get list of supported export formats."""
        return ["json", "csv", "xml", "qti", "moodle", "aiken", "respondus", "gift"]
    
    def validate_format(self, format_name):
        """Validate if format is supported."""
        pass
```

### BatchProcessor

```python
class BatchProcessor:
    """Handles batch processing of multiple texts/documents."""
    
    def __init__(self, max_workers=4, chunk_size=1000, enable_gpu=False):
        """Initialize batch processor."""
        pass
    
    def process_texts(self, texts, questions_per_text=5, question_types=None):
        """Process multiple text strings."""
        pass
    
    def process_documents(self, document_paths, questions_per_document=5, question_types=None):
        """Process multiple document files."""
        pass
```

## Troubleshooting

### Common Issues

#### 1. Model Loading Issues

**Problem**: `ModelLoadError: Unable to load model`

**Solutions**:
```bash
# Clear model cache
rm -rf ~/.cache/huggingface/

# Reinstall transformers
pip uninstall transformers
pip install transformers

# Check internet connection for model download
```

#### 2. Memory Issues

**Problem**: `OutOfMemoryError` during processing

**Solutions**:
```python
# Reduce batch size
generator = QuestionGenerator(batch_size=8)  # Default is 16

# Use memory optimizer
from quizaigen.utils import MemoryOptimizer
optimizer = MemoryOptimizer()
with optimizer.optimize_context():
    questions = generator.generate_mcq(text, num_questions=5)

# Process in smaller chunks
chunks = optimizer.chunk_text(large_text, chunk_size=1000)
for chunk in chunks:
    chunk_questions = generator.generate_mcq(chunk, num_questions=2)
```

#### 3. Poor Question Quality

**Problem**: Generated questions are low quality or irrelevant

**Solutions**:
```python
# Increase confidence threshold
questions = generator.generate_mcq(
    text, 
    num_questions=10,  # Generate more, filter later
    min_confidence=0.8
)

# Use quality filtering
from quizaigen.quality import QualityController
qc = QualityController(min_confidence=0.8)
filtered_questions = qc.filter_questions(questions, target_count=5)

# Improve input text quality
clean_text = prepare_text(raw_text)  # Remove noise, ensure proper formatting
```

#### 4. Slow Processing

**Problem**: Question generation is too slow

**Solutions**:
```python
# Enable GPU acceleration
generator = QuestionGenerator(device="cuda", batch_size=32)

# Use smaller model
generator = QuestionGenerator(model_name="distilbert-base-uncased")

# Enable caching
from quizaigen.models import ModelCache
cache = ModelCache()
generator = QuestionGenerator(cache=cache)

# Use batch processing
batch_processor = BatchProcessor(max_workers=4)
results = batch_processor.process_texts(texts)
```

### Debug Mode

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Use debug configuration
from quizaigen.core import Config
config = Config(debug=True, verbose=True)
generator = QuestionGenerator(config=config)

# Monitor performance
from quizaigen.utils import PerformanceMonitor
monitor = PerformanceMonitor()
with monitor.track("debug_generation"):
    questions = generator.generate_mcq(text, num_questions=5)

print(monitor.get_detailed_metrics())
```

### Getting Help

- **Documentation**: [https://quizaigen.readthedocs.io](https://quizaigen.readthedocs.io)
- **GitHub Issues**: [https://github.com/yourusername/quizaigen/issues](https://github.com/yourusername/quizaigen/issues)
- **Discussions**: [https://github.com/yourusername/quizaigen/discussions](https://github.com/yourusername/quizaigen/discussions)
- **Email Support**: <EMAIL>

---

**Note**: This guide covers the main features and usage patterns of QuizAIGen. For the most up-to-date information, please refer to the official documentation and API reference.