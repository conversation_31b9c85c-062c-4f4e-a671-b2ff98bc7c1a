#!/usr/bin/env python3
"""
Commercial Package Build System for QuizAIGen
=============================================

Automated build system that creates protected, licensed packages
for commercial distribution.
"""

import os
import sys
import shutil
import subprocess
import tempfile
import json
from pathlib import Path
from typing import Dict, Any, List
import argparse
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CommercialPackageBuilder:
    """
    Comprehensive build system for QuizAIGen commercial packages.
    """
    
    def __init__(self, config_file: str):
        """
        Initialize the build system.
        
        Args:
            config_file: Path to build configuration file
        """
        self.config_file = Path(config_file)
        self.config = self._load_config()
        self.build_dir = Path(self.config['build']['output_dir'])
        self.temp_dir = None
        
        # Validate configuration
        self._validate_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load build configuration from file."""
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            sys.exit(1)
    
    def _validate_config(self) -> None:
        """Validate build configuration."""
        required_sections = ['source', 'build', 'protection', 'distribution']
        for section in required_sections:
            if section not in self.config:
                logger.error(f"Missing required config section: {section}")
                sys.exit(1)
    
    def build_package(self, tier: str = "all") -> bool:
        """
        Build commercial package for specified tier.
        
        Args:
            tier: License tier to build for (free, premium, enterprise, all)
            
        Returns:
            bool: True if build successful
        """
        try:
            logger.info(f"Starting commercial build for tier: {tier}")
            
            # Create build directory
            self.build_dir.mkdir(parents=True, exist_ok=True)
            
            # Create temporary working directory
            self.temp_dir = Path(tempfile.mkdtemp(prefix="quizaigen_build_"))
            logger.info(f"Working directory: {self.temp_dir}")
            
            if tier == "all":
                tiers = ["free", "premium", "enterprise"]
            else:
                tiers = [tier]
            
            for build_tier in tiers:
                logger.info(f"Building {build_tier} tier package...")
                self._build_tier_package(build_tier)
            
            logger.info("Commercial build completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Build failed: {e}")
            return False
        finally:
            # Cleanup
            if self.temp_dir and self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
    
    def _build_tier_package(self, tier: str) -> None:
        """Build package for specific tier."""
        tier_config = self.config['tiers'][tier]
        
        # Step 1: Prepare source code
        tier_source_dir = self.temp_dir / f"quizaigen_{tier}"
        self._prepare_source_code(tier_source_dir, tier_config)
        
        # Step 2: Apply tier-specific modifications
        self._apply_tier_modifications(tier_source_dir, tier_config)
        
        # Step 3: Apply protection
        if tier != "free":  # Free tier gets minimal protection
            protected_dir = self.temp_dir / f"quizaigen_{tier}_protected"
            self._apply_protection(tier_source_dir, protected_dir, tier)
            source_for_packaging = protected_dir
        else:
            source_for_packaging = tier_source_dir
        
        # Step 4: Create package
        self._create_package(source_for_packaging, tier)
        
        # Step 5: Generate distribution files
        self._generate_distribution_files(tier)
    
    def _prepare_source_code(self, dest_dir: Path, tier_config: Dict[str, Any]) -> None:
        """Prepare source code for specific tier."""
        logger.info("Preparing source code...")
        
        source_dir = Path(self.config['source']['directory'])
        
        # Copy base source
        shutil.copytree(source_dir, dest_dir)
        
        # Remove excluded features for this tier
        excluded_features = tier_config.get('excluded_features', [])
        for feature in excluded_features:
            feature_path = dest_dir / 'quizaigen' / feature
            if feature_path.exists():
                if feature_path.is_file():
                    feature_path.unlink()
                else:
                    shutil.rmtree(feature_path)
                logger.info(f"Removed feature: {feature}")
    
    def _apply_tier_modifications(self, source_dir: Path, tier_config: Dict[str, Any]) -> None:
        """Apply tier-specific code modifications."""
        logger.info("Applying tier modifications...")
        
        # Update version and metadata
        self._update_package_metadata(source_dir, tier_config)
        
        # Add tier-specific license validation
        self._add_tier_license_validation(source_dir, tier_config)
        
        # Modify feature availability
        self._modify_feature_availability(source_dir, tier_config)
    
    def _update_package_metadata(self, source_dir: Path, tier_config: Dict[str, Any]) -> None:
        """Update package metadata for tier."""
        init_file = source_dir / 'quizaigen' / '__init__.py'
        
        if init_file.exists():
            content = init_file.read_text()
            
            # Update version with tier suffix
            version_line = f'__version__ = "{self.config["version"]}-{tier_config["name"]}"'
            content = self._replace_line(content, '__version__', version_line)
            
            # Update license info
            license_line = f'__license__ = "{tier_config["license"]}"'
            content = self._replace_line(content, '__license__', license_line)
            
            init_file.write_text(content)
    
    def _add_tier_license_validation(self, source_dir: Path, tier_config: Dict[str, Any]) -> None:
        """Add tier-specific license validation."""
        # Create tier-specific license validator
        validator_template = f'''
# Tier-specific license validation for {tier_config["name"]}
REQUIRED_TIER = ModelTier.{tier_config["name"].upper()}
REQUIRED_FEATURES = {tier_config.get("required_features", [])}

def validate_tier_access():
    """Validate access for {tier_config["name"]} tier features."""
    validator = get_license_validator()
    current_tier = validator.get_current_tier()
    
    tier_hierarchy = {{
        ModelTier.FREE: 0,
        ModelTier.PREMIUM: 1,
        ModelTier.ENTERPRISE: 2
    }}
    
    if tier_hierarchy[current_tier] < tier_hierarchy[REQUIRED_TIER]:
        raise LicenseError(f"Feature requires {{REQUIRED_TIER.value}} license or higher")
    
    for feature in REQUIRED_FEATURES:
        if not validator.has_feature(feature):
            raise LicenseError(f"License does not include required feature: {{feature}}")
'''
        
        # Add to license validator module
        license_file = source_dir / 'quizaigen' / 'core' / 'license_validator.py'
        if license_file.exists():
            content = license_file.read_text()
            content += validator_template
            license_file.write_text(content)
    
    def _modify_feature_availability(self, source_dir: Path, tier_config: Dict[str, Any]) -> None:
        """Modify feature availability based on tier."""
        available_features = tier_config.get('available_features', [])
        
        # Add license decorators to premium/enterprise features
        feature_modules = {
            'generators/fill_blank_generator.py': 'premium',
            'export/advanced_exporters.py': 'premium',
            'models/custom_training.py': 'enterprise',
            'processing/multilingual_processor.py': 'enterprise'
        }
        
        for module_path, required_tier in feature_modules.items():
            module_file = source_dir / 'quizaigen' / module_path
            if module_file.exists():
                content = module_file.read_text()
                
                # Add license requirement decorator to main classes
                if required_tier not in tier_config['name'].lower():
                    # This tier doesn't have access - add restrictive decorator
                    decorator = f"@require_license(ModelTier.{required_tier.upper()})"
                    content = self._add_decorator_to_classes(content, decorator)
                
                module_file.write_text(content)
    
    def _apply_protection(self, source_dir: Path, output_dir: Path, tier: str) -> None:
        """Apply code protection based on tier."""
        logger.info(f"Applying protection for {tier} tier...")
        
        protection_config = self.config['protection'][tier]
        
        # Use the obfuscation script
        obfuscator_script = Path(__file__).parent / 'obfuscate_package.py'
        
        cmd = [
            sys.executable,
            str(obfuscator_script),
            str(source_dir),
            str(output_dir),
            '--protection-level', protection_config['level']
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"Protection failed: {result.stderr}")
            raise RuntimeError("Code protection failed")
    
    def _create_package(self, source_dir: Path, tier: str) -> None:
        """Create distributable package."""
        logger.info(f"Creating {tier} package...")
        
        # Build wheel
        build_cmd = [
            sys.executable, '-m', 'build',
            '--wheel',
            '--outdir', str(self.build_dir / tier),
            str(source_dir)
        ]
        
        result = subprocess.run(build_cmd, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"Package build failed: {result.stderr}")
            raise RuntimeError("Package creation failed")
        
        logger.info(f"Package created: {self.build_dir / tier}")
    
    def _generate_distribution_files(self, tier: str) -> None:
        """Generate distribution files for tier."""
        tier_dir = self.build_dir / tier
        
        # Generate installation instructions
        install_instructions = self._generate_install_instructions(tier)
        (tier_dir / 'INSTALL.md').write_text(install_instructions)
        
        # Generate license file
        license_content = self._generate_license_file(tier)
        (tier_dir / 'LICENSE.txt').write_text(license_content)
        
        # Generate customer README
        readme_content = self._generate_customer_readme(tier)
        (tier_dir / 'README.md').write_text(readme_content)
    
    def _generate_install_instructions(self, tier: str) -> str:
        """Generate installation instructions for tier."""
        tier_config = self.config['tiers'][tier]
        
        if tier == 'free':
            return f"""
# QuizAIGen {tier.title()} Tier Installation

## Installation
```bash
pip install quizaigen-{tier}
```

## Usage
```python
import quizaigen
# Free tier features available
```

## Features
{chr(10).join(f"- {feature}" for feature in tier_config.get('available_features', []))}
"""
        else:
            return f"""
# QuizAIGen {tier.title()} Tier Installation

## Prerequisites
- Valid QuizAIGen {tier} license key
- Python 3.8 or higher

## Installation
```bash
pip install quizaigen-{tier} --extra-index-url https://pypi.quizaigen.com/simple/
```

## License Setup
```bash
# Set license key as environment variable
export QUIZAIGEN_LICENSE_KEY="your-license-key-here"

# Or save to file
echo "your-license-key-here" > ~/.quizaigen/license.key
```

## Usage
```python
import quizaigen

# Initialize with license
quizaigen.initialize_license("your-license-key-here")

# Check license status
print(quizaigen.get_license_info())
```

## Features
{chr(10).join(f"- {feature}" for feature in tier_config.get('available_features', []))}

## Support
For technical support, contact: <EMAIL>
For licensing questions, contact: <EMAIL>
"""
    
    def _generate_license_file(self, tier: str) -> str:
        """Generate license file for tier."""
        tier_config = self.config['tiers'][tier]
        
        if tier == 'free':
            return """
MIT License

Copyright (c) 2024 QuizAIGen Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
"""
        else:
            return f"""
QuizAIGen {tier.title()} License

Copyright (c) 2024 QuizAIGen Team

This software is licensed under the QuizAIGen Commercial License.

COMMERCIAL USE TERMS:
1. This software requires a valid commercial license for use
2. License terms are governed by your QuizAIGen License Agreement
3. Unauthorized use, copying, or distribution is prohibited
4. Contact <EMAIL> for licensing terms

For full license terms, see your QuizAIGen License Agreement.
"""
    
    def _generate_customer_readme(self, tier: str) -> str:
        """Generate customer-facing README."""
        return f"""
# QuizAIGen {tier.title()} Tier

Welcome to QuizAIGen {tier.title()} - AI-Powered Question Generation Library.

## Quick Start

1. Install the package (see INSTALL.md)
2. Set up your license (if applicable)
3. Start generating questions!

```python
import quizaigen

# Create question generator
generator = quizaigen.QuestionGenerator()

# Generate questions from text
text = "Python is a programming language..."
questions = generator.generate_mcq(text, num_questions=5)

print(questions)
```

## Documentation

- Full documentation: https://docs.quizaigen.com
- API reference: https://docs.quizaigen.com/api
- Examples: https://docs.quizaigen.com/examples

## Support

- Technical support: <EMAIL>
- Community forum: https://community.quizaigen.com
- GitHub issues: https://github.com/quizaigen/quizaigen/issues

## License

See LICENSE.txt for license terms.
"""
    
    def _replace_line(self, content: str, pattern: str, replacement: str) -> str:
        """Replace line containing pattern with replacement."""
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if pattern in line:
                lines[i] = replacement
                break
        return '\n'.join(lines)
    
    def _add_decorator_to_classes(self, content: str, decorator: str) -> str:
        """Add decorator to class definitions."""
        lines = content.split('\n')
        new_lines = []
        
        for line in lines:
            if line.strip().startswith('class ') and ':' in line:
                new_lines.append(decorator)
            new_lines.append(line)
        
        return '\n'.join(new_lines)


def main():
    """Main entry point for the build system."""
    parser = argparse.ArgumentParser(description="QuizAIGen Commercial Package Builder")
    parser.add_argument("config", help="Build configuration file")
    parser.add_argument(
        "--tier",
        choices=["free", "premium", "enterprise", "all"],
        default="all",
        help="Tier to build"
    )
    
    args = parser.parse_args()
    
    builder = CommercialPackageBuilder(args.config)
    success = builder.build_package(args.tier)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
