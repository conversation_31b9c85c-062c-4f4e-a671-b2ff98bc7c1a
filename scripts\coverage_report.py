#!/usr/bin/env python3
"""
Coverage Analysis Script for QuizAIGen

Generates comprehensive coverage reports and analysis.
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Any
import argparse


class CoverageAnalyzer:
    """Analyze code coverage for QuizAIGen."""
    
    def __init__(self, project_root: str = None):
        """
        Initialize coverage analyzer.
        
        Args:
            project_root: Root directory of the project
        """
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.coverage_dir = self.project_root / "htmlcov"
        self.reports_dir = self.project_root / "coverage_reports"
        self.reports_dir.mkdir(exist_ok=True)
    
    def run_coverage(self, test_path: str = "tests/") -> bool:
        """
        Run tests with coverage collection.
        
        Args:
            test_path: Path to tests directory
            
        Returns:
            True if successful, False otherwise
        """
        try:
            print("🧪 Running tests with coverage...")
            
            cmd = [
                sys.executable, "-m", "pytest",
                test_path,
                "--cov=quizaigen",
                "--cov-report=html",
                "--cov-report=xml",
                "--cov-report=json",
                "--cov-report=term-missing",
                "-v"
            ]
            
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Tests completed successfully")
                return True
            else:
                print(f"❌ Tests failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error running coverage: {e}")
            return False
    
    def generate_detailed_report(self) -> Dict[str, Any]:
        """
        Generate detailed coverage report.
        
        Returns:
            Coverage analysis data
        """
        try:
            # Read JSON coverage report
            coverage_json_path = self.project_root / "coverage.json"
            
            if not coverage_json_path.exists():
                print("❌ Coverage JSON file not found. Run tests with coverage first.")
                return {}
            
            with open(coverage_json_path, 'r') as f:
                coverage_data = json.load(f)
            
            # Analyze coverage data
            analysis = self._analyze_coverage_data(coverage_data)
            
            # Save detailed report
            report_path = self.reports_dir / "detailed_coverage_report.json"
            with open(report_path, 'w') as f:
                json.dump(analysis, f, indent=2)
            
            print(f"📊 Detailed coverage report saved to: {report_path}")
            return analysis
            
        except Exception as e:
            print(f"❌ Error generating detailed report: {e}")
            return {}
    
    def _analyze_coverage_data(self, coverage_data: Dict) -> Dict[str, Any]:
        """
        Analyze coverage data and generate insights.
        
        Args:
            coverage_data: Raw coverage data
            
        Returns:
            Analysis results
        """
        files = coverage_data.get('files', {})
        totals = coverage_data.get('totals', {})
        
        analysis = {
            'summary': {
                'total_statements': totals.get('num_statements', 0),
                'covered_statements': totals.get('covered_lines', 0),
                'missing_statements': totals.get('missing_lines', 0),
                'coverage_percentage': totals.get('percent_covered', 0.0),
                'branch_coverage': totals.get('percent_covered_display', 'N/A')
            },
            'files': {},
            'categories': {},
            'recommendations': []
        }
        
        # Analyze by file
        for file_path, file_data in files.items():
            file_analysis = {
                'statements': file_data.get('summary', {}).get('num_statements', 0),
                'covered': file_data.get('summary', {}).get('covered_lines', 0),
                'missing': file_data.get('summary', {}).get('missing_lines', 0),
                'coverage': file_data.get('summary', {}).get('percent_covered', 0.0),
                'missing_lines': file_data.get('missing_lines', [])
            }
            analysis['files'][file_path] = file_analysis
        
        # Categorize by module
        categories = {}
        for file_path, file_data in analysis['files'].items():
            if 'quizaigen/' in file_path:
                parts = file_path.replace('quizaigen/', '').split('/')
                category = parts[0] if len(parts) > 1 else 'core'
                
                if category not in categories:
                    categories[category] = {
                        'files': 0,
                        'total_statements': 0,
                        'covered_statements': 0,
                        'coverage': 0.0
                    }
                
                categories[category]['files'] += 1
                categories[category]['total_statements'] += file_data['statements']
                categories[category]['covered_statements'] += file_data['covered']
        
        # Calculate category coverage
        for category, data in categories.items():
            if data['total_statements'] > 0:
                data['coverage'] = (data['covered_statements'] / data['total_statements']) * 100
        
        analysis['categories'] = categories
        
        # Generate recommendations
        analysis['recommendations'] = self._generate_recommendations(analysis)
        
        return analysis
    
    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """
        Generate coverage improvement recommendations.
        
        Args:
            analysis: Coverage analysis data
            
        Returns:
            List of recommendations
        """
        recommendations = []
        overall_coverage = analysis['summary']['coverage_percentage']
        
        if overall_coverage < 80:
            recommendations.append(
                f"Overall coverage is {overall_coverage:.1f}%. Aim for at least 80% coverage."
            )
        
        # Find files with low coverage
        low_coverage_files = []
        for file_path, file_data in analysis['files'].items():
            if file_data['coverage'] < 70 and file_data['statements'] > 10:
                low_coverage_files.append((file_path, file_data['coverage']))
        
        if low_coverage_files:
            recommendations.append("Files with low coverage (< 70%):")
            for file_path, coverage in sorted(low_coverage_files, key=lambda x: x[1]):
                recommendations.append(f"  - {file_path}: {coverage:.1f}%")
        
        # Find categories with low coverage
        for category, data in analysis['categories'].items():
            if data['coverage'] < 75:
                recommendations.append(
                    f"Category '{category}' has low coverage: {data['coverage']:.1f}%"
                )
        
        return recommendations
    
    def print_summary(self, analysis: Dict = None):
        """
        Print coverage summary.
        
        Args:
            analysis: Coverage analysis data
        """
        if not analysis:
            analysis = self.generate_detailed_report()
        
        if not analysis:
            return
        
        summary = analysis['summary']
        
        print("\n" + "="*60)
        print("📊 COVERAGE SUMMARY")
        print("="*60)
        print(f"Total Statements: {summary['total_statements']}")
        print(f"Covered: {summary['covered_statements']}")
        print(f"Missing: {summary['missing_statements']}")
        print(f"Coverage: {summary['coverage_percentage']:.2f}%")
        
        print("\n📁 COVERAGE BY CATEGORY:")
        print("-"*40)
        for category, data in analysis['categories'].items():
            print(f"{category:20} {data['coverage']:6.1f}% ({data['files']} files)")
        
        if analysis['recommendations']:
            print("\n💡 RECOMMENDATIONS:")
            print("-"*40)
            for rec in analysis['recommendations']:
                print(f"• {rec}")
        
        print("\n🌐 HTML Report: file://" + str(self.coverage_dir / "index.html"))
    
    def check_coverage_threshold(self, threshold: float = 80.0) -> bool:
        """
        Check if coverage meets threshold.
        
        Args:
            threshold: Minimum coverage percentage
            
        Returns:
            True if threshold is met
        """
        try:
            coverage_json_path = self.project_root / "coverage.json"
            
            if not coverage_json_path.exists():
                print("❌ Coverage data not found")
                return False
            
            with open(coverage_json_path, 'r') as f:
                coverage_data = json.load(f)
            
            current_coverage = coverage_data.get('totals', {}).get('percent_covered', 0.0)
            
            if current_coverage >= threshold:
                print(f"✅ Coverage {current_coverage:.2f}% meets threshold {threshold}%")
                return True
            else:
                print(f"❌ Coverage {current_coverage:.2f}% below threshold {threshold}%")
                return False
                
        except Exception as e:
            print(f"❌ Error checking coverage threshold: {e}")
            return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="QuizAIGen Coverage Analysis")
    parser.add_argument("--test-path", default="tests/", help="Path to tests directory")
    parser.add_argument("--threshold", type=float, default=80.0, help="Coverage threshold")
    parser.add_argument("--no-run", action="store_true", help="Skip running tests")
    parser.add_argument("--report-only", action="store_true", help="Generate report only")
    
    args = parser.parse_args()
    
    analyzer = CoverageAnalyzer()
    
    if not args.no_run and not args.report_only:
        success = analyzer.run_coverage(args.test_path)
        if not success:
            sys.exit(1)
    
    analysis = analyzer.generate_detailed_report()
    analyzer.print_summary(analysis)
    
    if not analyzer.check_coverage_threshold(args.threshold):
        sys.exit(1)


if __name__ == "__main__":
    main()
