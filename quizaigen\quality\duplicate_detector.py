"""
Advanced Duplicate Detection for QuizAIGen

Provides sophisticated duplicate detection using semantic similarity analysis.
Helps identify and remove duplicate or near-duplicate questions.
"""

from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import re
import hashlib
from collections import defaultdict

from ..generators.base import Question
from ..models.base_model import ModelTier
from ..core.exceptions import ProcessingError, LicenseError
from ..utils.logger import LoggerMixin


class SimilarityMethod(Enum):
    """Methods for calculating similarity."""
    EXACT_MATCH = "exact_match"
    FUZZY_MATCH = "fuzzy_match"
    SEMANTIC_SIMILARITY = "semantic_similarity"
    STRUCTURAL_SIMILARITY = "structural_similarity"


@dataclass
class DuplicateMatch:
    """Result of duplicate detection."""
    question1_index: int
    question2_index: int
    similarity_score: float  # 0.0 to 1.0
    method: SimilarityMethod
    confidence: float  # 0.0 to 1.0
    reasoning: str
    
    def to_dict(self) -> Dict:
        """Convert to dictionary format."""
        return {
            'question1_index': self.question1_index,
            'question2_index': self.question2_index,
            'similarity_score': self.similarity_score,
            'method': self.method.value,
            'confidence': self.confidence,
            'reasoning': self.reasoning
        }


@dataclass
class DuplicateGroup:
    """Group of duplicate questions."""
    question_indices: List[int]
    representative_index: int  # Index of the "best" question in the group
    similarity_scores: List[float]  # Pairwise similarity scores
    group_confidence: float
    
    def to_dict(self) -> Dict:
        """Convert to dictionary format."""
        return {
            'question_indices': self.question_indices,
            'representative_index': self.representative_index,
            'similarity_scores': self.similarity_scores,
            'group_confidence': self.group_confidence
        }


class DuplicateDetector(LoggerMixin):
    """
    Advanced duplicate detection system.
    
    Detects duplicates using multiple methods:
    - Exact text matching
    - Fuzzy string matching
    - Semantic similarity (Premium/Enterprise)
    - Structural similarity analysis
    """
    
    def __init__(self, tier: ModelTier = ModelTier.FREE, 
                 similarity_threshold: float = 0.8):
        """
        Initialize duplicate detector.
        
        Args:
            tier: License tier for feature access
            similarity_threshold: Threshold for considering questions as duplicates
        """
        super().__init__()
        self.tier = tier
        self.similarity_threshold = similarity_threshold
        
        # Similarity thresholds for different methods
        self.method_thresholds = {
            SimilarityMethod.EXACT_MATCH: 1.0,
            SimilarityMethod.FUZZY_MATCH: 0.9,
            SimilarityMethod.SEMANTIC_SIMILARITY: 0.8,
            SimilarityMethod.STRUCTURAL_SIMILARITY: 0.85
        }
        
        # Weights for combining different similarity methods
        self.method_weights = {
            SimilarityMethod.EXACT_MATCH: 1.0,
            SimilarityMethod.FUZZY_MATCH: 0.8,
            SimilarityMethod.SEMANTIC_SIMILARITY: 0.9,
            SimilarityMethod.STRUCTURAL_SIMILARITY: 0.6
        }
        
        self.log_info(f"Initialized DuplicateDetector with tier: {tier.value}, "
                     f"threshold: {similarity_threshold}")
    
    def detect_duplicates(self, questions: List[Question]) -> List[DuplicateMatch]:
        """
        Detect duplicate questions in a list.
        
        Args:
            questions: List of questions to analyze
            
        Returns:
            List of duplicate matches found
            
        Raises:
            ProcessingError: If duplicate detection fails
        """
        try:
            self.log_debug(f"Detecting duplicates in {len(questions)} questions...")
            
            duplicates = []
            
            # Compare each pair of questions
            for i in range(len(questions)):
                for j in range(i + 1, len(questions)):
                    match = self._compare_questions(questions[i], questions[j], i, j)
                    if match and match.similarity_score >= self.similarity_threshold:
                        duplicates.append(match)
            
            self.log_info(f"Found {len(duplicates)} duplicate pairs")
            return duplicates
            
        except Exception as e:
            self.log_error(f"Duplicate detection failed: {str(e)}")
            raise ProcessingError(
                stage="duplicate_detection",
                message=f"Failed to detect duplicates: {str(e)}"
            )
    
    def group_duplicates(self, questions: List[Question]) -> List[DuplicateGroup]:
        """
        Group duplicate questions together.
        
        Args:
            questions: List of questions to analyze
            
        Returns:
            List of duplicate groups
        """
        duplicates = self.detect_duplicates(questions)
        
        # Build adjacency list for connected components
        adjacency = defaultdict(set)
        for match in duplicates:
            adjacency[match.question1_index].add(match.question2_index)
            adjacency[match.question2_index].add(match.question1_index)
        
        # Find connected components (groups of duplicates)
        visited = set()
        groups = []
        
        for i in range(len(questions)):
            if i not in visited:
                group = self._find_connected_component(i, adjacency, visited)
                if len(group) > 1:  # Only groups with actual duplicates
                    # Calculate group statistics
                    similarity_scores = []
                    for j in range(len(group)):
                        for k in range(j + 1, len(group)):
                            idx1, idx2 = group[j], group[k]
                            match = next((m for m in duplicates 
                                        if (m.question1_index == idx1 and m.question2_index == idx2) or
                                           (m.question1_index == idx2 and m.question2_index == idx1)), None)
                            if match:
                                similarity_scores.append(match.similarity_score)
                    
                    # Choose representative (first question in group for now)
                    representative_index = group[0]
                    group_confidence = sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0.0
                    
                    duplicate_group = DuplicateGroup(
                        question_indices=group,
                        representative_index=representative_index,
                        similarity_scores=similarity_scores,
                        group_confidence=group_confidence
                    )
                    groups.append(duplicate_group)
        
        self.log_info(f"Found {len(groups)} duplicate groups")
        return groups
    
    def remove_duplicates(self, questions: List[Question]) -> List[Question]:
        """
        Remove duplicate questions, keeping only unique ones.
        
        Args:
            questions: List of questions to deduplicate
            
        Returns:
            List of unique questions
        """
        groups = self.group_duplicates(questions)
        
        # Collect indices to remove (all except representatives)
        indices_to_remove = set()
        for group in groups:
            for idx in group.question_indices:
                if idx != group.representative_index:
                    indices_to_remove.add(idx)
        
        # Keep only non-duplicate questions
        unique_questions = []
        for i, question in enumerate(questions):
            if i not in indices_to_remove:
                # Add duplicate detection metadata
                if question.metadata is None:
                    question.metadata = {}

                # Find if this question was a representative
                representative_group = next((g for g in groups if g.representative_index == i), None)
                if representative_group:
                    question.metadata['duplicate_info'] = {
                        'is_representative': True,
                        'group_size': len(representative_group.question_indices),
                        'group_confidence': representative_group.group_confidence
                    }
                else:
                    question.metadata['duplicate_info'] = {
                        'is_representative': False,
                        'is_unique': True
                    }

                unique_questions.append(question)
        
        self.log_info(f"Removed {len(questions) - len(unique_questions)} duplicate questions")
        return unique_questions
    
    def _compare_questions(self, q1: Question, q2: Question, 
                          idx1: int, idx2: int) -> Optional[DuplicateMatch]:
        """Compare two questions for similarity."""
        similarities = {}
        
        # Exact match
        exact_score = self._exact_similarity(q1.question, q2.question)
        if exact_score > 0:
            similarities[SimilarityMethod.EXACT_MATCH] = exact_score
        
        # Fuzzy match
        fuzzy_score = self._fuzzy_similarity(q1.question, q2.question)
        similarities[SimilarityMethod.FUZZY_MATCH] = fuzzy_score
        
        # Structural similarity
        structural_score = self._structural_similarity(q1, q2)
        similarities[SimilarityMethod.STRUCTURAL_SIMILARITY] = structural_score
        
        # Semantic similarity (Premium/Enterprise only)
        if self.tier in [ModelTier.PREMIUM, ModelTier.ENTERPRISE]:
            semantic_score = self._semantic_similarity(q1.question, q2.question)
            similarities[SimilarityMethod.SEMANTIC_SIMILARITY] = semantic_score
        
        # Find best similarity method
        if not similarities:
            return None
        
        best_method = max(similarities.items(), key=lambda x: x[1])
        method, score = best_method
        
        # Calculate weighted combined score
        combined_score = sum(
            score * self.method_weights.get(method, 1.0) 
            for method, score in similarities.items()
        ) / sum(self.method_weights.get(method, 1.0) for method in similarities.keys())
        
        # Calculate confidence
        confidence = self._calculate_confidence(similarities, score)
        
        # Generate reasoning
        reasoning = self._generate_reasoning(method, score, similarities)
        
        return DuplicateMatch(
            question1_index=idx1,
            question2_index=idx2,
            similarity_score=combined_score,
            method=method,
            confidence=confidence,
            reasoning=reasoning
        )
    
    def _exact_similarity(self, text1: str, text2: str) -> float:
        """Calculate exact text similarity."""
        # Normalize texts
        norm1 = self._normalize_text(text1)
        norm2 = self._normalize_text(text2)
        
        return 1.0 if norm1 == norm2 else 0.0
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison."""
        # Convert to lowercase, remove extra whitespace and punctuation
        normalized = re.sub(r'[^\w\s]', '', text.lower())
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        return normalized
    
    def _fuzzy_similarity(self, text1: str, text2: str) -> float:
        """Calculate fuzzy string similarity using Levenshtein-like approach."""
        norm1 = self._normalize_text(text1)
        norm2 = self._normalize_text(text2)
        
        if not norm1 or not norm2:
            return 0.0
        
        # Simple character-based similarity
        max_len = max(len(norm1), len(norm2))
        if max_len == 0:
            return 1.0
        
        # Count matching characters in order
        matches = 0
        i = j = 0
        while i < len(norm1) and j < len(norm2):
            if norm1[i] == norm2[j]:
                matches += 1
                i += 1
                j += 1
            else:
                i += 1
        
        return matches / max_len

    def _structural_similarity(self, q1: Question, q2: Question) -> float:
        """Calculate structural similarity between questions."""
        similarities = []

        # Question type similarity
        type_sim = 1.0 if q1.type == q2.type else 0.0
        similarities.append(type_sim)

        # Answer structure similarity (for MCQ, etc.)
        if hasattr(q1, 'options') and hasattr(q2, 'options'):
            if q1.options and q2.options:
                # Compare number of options
                len_sim = 1.0 - abs(len(q1.options) - len(q2.options)) / max(len(q1.options), len(q2.options))
                similarities.append(len_sim)

                # Compare option content similarity
                option_similarities = []
                for opt1 in q1.options:
                    best_match = max(
                        self._fuzzy_similarity(opt1, opt2) for opt2 in q2.options
                    ) if q2.options else 0.0
                    option_similarities.append(best_match)

                if option_similarities:
                    avg_option_sim = sum(option_similarities) / len(option_similarities)
                    similarities.append(avg_option_sim)

        # Question length similarity
        len1, len2 = len(q1.question), len(q2.question)
        if max(len1, len2) > 0:
            length_sim = 1.0 - abs(len1 - len2) / max(len1, len2)
            similarities.append(length_sim)

        return sum(similarities) / len(similarities) if similarities else 0.0

    def _semantic_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate semantic similarity using AI models (Premium/Enterprise).

        This would use BERT embeddings for sophisticated similarity analysis.
        For now, provides rule-based approximation.
        """
        if self.tier == ModelTier.FREE:
            raise LicenseError("Semantic similarity requires Premium or Enterprise tier")

        # Rule-based semantic similarity approximation
        # In full implementation, this would use BERT embeddings

        # Extract key concepts from both texts
        concepts1 = self._extract_concepts(text1)
        concepts2 = self._extract_concepts(text2)

        if not concepts1 or not concepts2:
            return 0.0

        # Calculate concept overlap
        common_concepts = concepts1.intersection(concepts2)
        all_concepts = concepts1.union(concepts2)

        if not all_concepts:
            return 0.0

        # Jaccard similarity
        jaccard_sim = len(common_concepts) / len(all_concepts)

        # Boost similarity if key question words match
        question_words = {'what', 'how', 'why', 'when', 'where', 'who', 'which'}
        text1_q_words = set(text1.lower().split()) & question_words
        text2_q_words = set(text2.lower().split()) & question_words

        if text1_q_words and text2_q_words and text1_q_words == text2_q_words:
            jaccard_sim = min(jaccard_sim + 0.2, 1.0)

        return jaccard_sim

    def _extract_concepts(self, text: str) -> Set[str]:
        """Extract key concepts from text."""
        # Simple concept extraction using important words
        words = re.findall(r'\b\w+\b', text.lower())

        # Filter out common words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
        }

        concepts = set()
        for word in words:
            if len(word) > 3 and word not in stop_words:
                concepts.add(word)

        return concepts

    def _find_connected_component(self, start: int, adjacency: Dict[int, Set[int]],
                                visited: Set[int]) -> List[int]:
        """Find connected component using DFS."""
        component = []
        stack = [start]

        while stack:
            node = stack.pop()
            if node not in visited:
                visited.add(node)
                component.append(node)

                # Add unvisited neighbors to stack
                for neighbor in adjacency[node]:
                    if neighbor not in visited:
                        stack.append(neighbor)

        return component

    def _calculate_confidence(self, similarities: Dict[SimilarityMethod, float],
                            best_score: float) -> float:
        """Calculate confidence in the similarity assessment."""
        if not similarities:
            return 0.0

        # Higher confidence if multiple methods agree
        high_scores = [score for score in similarities.values() if score > 0.7]
        method_agreement = len(high_scores) / len(similarities)

        # Higher confidence for higher scores
        score_confidence = best_score

        # Combine factors
        confidence = (method_agreement * 0.6) + (score_confidence * 0.4)

        return min(confidence, 1.0)

    def _generate_reasoning(self, method: SimilarityMethod, score: float,
                          similarities: Dict[SimilarityMethod, float]) -> str:
        """Generate human-readable reasoning for the similarity assessment."""
        reasoning = f"Primary match via {method.value} (score: {score:.2f}). "

        # Add information about other methods
        other_methods = [(m, s) for m, s in similarities.items() if m != method and s > 0.3]
        if other_methods:
            method_info = [f"{m.value}: {s:.2f}" for m, s in other_methods[:2]]
            reasoning += f"Supporting evidence: {', '.join(method_info)}."

        return reasoning

    def get_duplicate_statistics(self, questions: List[Question]) -> Dict[str, any]:
        """
        Get comprehensive duplicate detection statistics.

        Args:
            questions: List of questions to analyze

        Returns:
            Dictionary with duplicate statistics
        """
        duplicates = self.detect_duplicates(questions)
        groups = self.group_duplicates(questions)

        # Calculate statistics
        total_questions = len(questions)
        duplicate_pairs = len(duplicates)
        duplicate_groups = len(groups)
        questions_in_groups = sum(len(group.question_indices) for group in groups)
        unique_questions = total_questions - questions_in_groups + duplicate_groups

        # Method breakdown
        method_counts = {}
        for duplicate in duplicates:
            method = duplicate.method.value
            method_counts[method] = method_counts.get(method, 0) + 1

        # Similarity score distribution
        scores = [d.similarity_score for d in duplicates]
        avg_similarity = sum(scores) / len(scores) if scores else 0.0

        return {
            'total_questions': total_questions,
            'duplicate_pairs': duplicate_pairs,
            'duplicate_groups': duplicate_groups,
            'questions_in_groups': questions_in_groups,
            'unique_questions': unique_questions,
            'duplication_rate': questions_in_groups / total_questions if total_questions > 0 else 0.0,
            'average_similarity': avg_similarity,
            'method_breakdown': method_counts,
            'similarity_threshold': self.similarity_threshold
        }
