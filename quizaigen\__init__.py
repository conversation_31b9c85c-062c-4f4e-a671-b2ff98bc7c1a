"""
QuizAIGen: AI-Powered Question Generation Library

A modular, extensible Python library for generating various types of questions
from text using state-of-the-art transformer models.

Author: QuizAIGen Team
License: Dual License (MIT for Free Tier, Commercial for Premium/Enterprise)
Version: 0.1.0
"""

import os
import sys
import warnings
from typing import Optional

__version__ = "0.1.0"
__author__ = "QuizAIGen Team"
__email__ = "<EMAIL>"
__license__ = "Dual License"

# License validation imports
from .core.license_validator import LicenseValidator, LicenseError
from .models.base_model import ModelTier

# Global license validator instance
_license_validator: Optional[LicenseValidator] = None


def initialize_license(license_key: Optional[str] = None,
                      license_file: Optional[str] = None,
                      validate_on_import: bool = True) -> bool:
    """
    Initialize QuizAIGen with license validation.

    Args:
        license_key: License key string
        license_file: Path to license file
        validate_on_import: Whether to validate license immediately

    Returns:
        bool: True if initialization successful

    Raises:
        LicenseError: If license validation fails
    """
    global _license_validator

    try:
        _license_validator = LicenseValidator()

        if license_key:
            _license_validator.set_license_key(license_key)
        elif license_file:
            _license_validator.load_license_file(license_file)

        if validate_on_import:
            if not _license_validator.validate():
                warnings.warn(
                    "QuizAIGen: Invalid or missing license. "
                    "Premium and Enterprise features will be restricted. "
                    "Contact <EMAIL> for licensing.",
                    UserWarning,
                    stacklevel=2
                )

        return True

    except Exception as e:
        if validate_on_import:
            warnings.warn(
                f"QuizAIGen: License initialization failed: {str(e)}. "
                "Using free tier features only.",
                UserWarning,
                stacklevel=2
            )
        return False


def get_license_info() -> dict:
    """Get current license information."""
    if _license_validator is None:
        return {"status": "not_initialized", "tier": "free"}

    return _license_validator.get_license_info()


def get_current_tier() -> ModelTier:
    """Get current license tier."""
    if _license_validator is None:
        return ModelTier.FREE

    return _license_validator.get_current_tier()


# Auto-initialize on import (can be disabled via environment variable)
if os.getenv('QUIZAIGEN_SKIP_AUTO_INIT', '').lower() not in ('1', 'true', 'yes'):
    initialize_license(validate_on_import=False)

# Core API imports for easy access
from .api.question_generator import QuestionGenerator
from .api.batch_processor import BatchProcessor
from .export.export_manager import ExportManager
from .inputs.text_processor import TextProcessor
from .inputs.document_processor import DocumentProcessor
from .inputs.url_processor import URLProcessor

# Exception imports
from .core.exceptions import (
    QuizAIGenError,
    ProcessingError,
    ValidationError,
    InputError,
    ModelLoadError
)

# Configuration
from .core.config import Config

__all__ = [
    # Main API classes
    'QuestionGenerator',
    'BatchProcessor',
    'ExportManager',
    'TextProcessor',
    'DocumentProcessor',
    'URLProcessor',

    # Configuration
    'Config',

    # License management
    'initialize_license',
    'get_license_info',
    'get_current_tier',
    'LicenseValidator',
    'LicenseError',
    'ModelTier',

    # Exceptions
    'QuizAIGenError',
    'ProcessingError',
    'ValidationError',
    'InputError',
    'ModelLoadError',

    # Version info
    '__version__',
    '__author__',
    '__email__',
    '__license__'
]

# Library-level configuration
DEFAULT_CONFIG = {
    'models': {
        'mcq': {
            'name': 't5-base',
            'cache': True,
            'max_length': 512
        },
        'boolean': {
            'name': 'bert-base-uncased',
            'cache': True,
            'max_length': 512
        },
        'faq': {
            'name': 't5-base',
            'cache': True,
            'max_length': 512
        },
        'fill_blank': {
            'name': 't5-base',
            'cache': True,
            'max_length': 512
        },
        'paraphraser': {
            'name': 't5-base',
            'cache': True,
            'max_length': 512
        },
        'answerer': {
            'name': 't5-base',
            'cache': True,
            'max_length': 512
        }
    },
    'processing': {
        'max_questions': 50,
        'min_quality_score': 0.7,
        'remove_duplicates': True,
        'language': 'en'
    },
    'export': {
        'default_format': 'json',
        'include_metadata': True,
        'pretty_print': True
    }
}

def get_version():
    """Get the current version of QuizAIGen."""
    return __version__

def get_config():
    """Get the default configuration."""
    return DEFAULT_CONFIG.copy()
