"""
QuizAIGen Models Module

This module handles AI model management, loading, and inference.
Provides tiered AI model integration:
- Free Tier: Lightweight models using NLTK/spaCy
- Premium Tier: T5 for fill-blank optimization, BERT for answer validation
- Enterprise Tier: Advanced models, GPU acceleration, custom training
"""

# Always available components (no external dependencies)
from .base_model import BaseModel, ModelConfig, ModelOutput, ModelTier, LightweightModel
from .model_cache import ModelCache

# Try to import AI models with fallback for missing dependencies
try:
    from .model_manager import ModelManager, ModelType
    from .inference_pipeline import InferencePipeline, InferenceRequest, InferenceResult
    from .t5_integration import T5QuestionGenerator
    from .bert_integration import BERTAnswerValidator, AnswerValidationResult
    from .ai_quality_enhancer import AIQualityEnhancer, QualityEnhancementResult

    AI_MODELS_AVAILABLE = True

    __all__ = [
        # Base components (always available)
        'BaseModel',
        'ModelConfig',
        'ModelOutput',
        'ModelTier',
        'LightweightModel',
        'ModelCache',

        # AI components (require transformers)
        'ModelManager',
        'ModelType',
        'InferencePipeline',
        'InferenceRequest',
        'InferenceResult',
        'T5QuestionGenerator',
        'BERTAnswerValidator',
        'AnswerValidationResult',
        'AIQualityEnhancer',
        'QualityEnhancementResult',

        # Availability flag
        'AI_MODELS_AVAILABLE'
    ]

except ImportError as e:
    # Fallback for environments without transformers
    AI_MODELS_AVAILABLE = False

    # Create placeholder classes that raise helpful errors
    class _MissingDependency:
        def __init__(self, *args, **kwargs):
            raise ImportError(
                "AI model features require additional dependencies. "
                "Install with: pip install quizaigen[premium] or pip install transformers torch"
            )

    # Assign placeholders
    ModelManager = _MissingDependency
    ModelType = None
    InferencePipeline = _MissingDependency
    InferenceRequest = None
    InferenceResult = None
    T5QuestionGenerator = _MissingDependency
    BERTAnswerValidator = _MissingDependency
    AnswerValidationResult = None
    AIQualityEnhancer = _MissingDependency
    QualityEnhancementResult = None

    __all__ = [
        # Base components (always available)
        'BaseModel',
        'ModelConfig',
        'ModelOutput',
        'ModelTier',
        'LightweightModel',
        'ModelCache',

        # Placeholder AI components
        'ModelManager',
        'ModelType',
        'InferencePipeline',
        'InferenceRequest',
        'InferenceResult',
        'T5QuestionGenerator',
        'BERTAnswerValidator',
        'AnswerValidationResult',
        'AIQualityEnhancer',
        'QualityEnhancementResult',

        # Availability flag
        'AI_MODELS_AVAILABLE'
    ]
