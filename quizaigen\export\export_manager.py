"""
Export Manager

This module handles exporting questions to various formats.
"""

import json
import csv
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime
import xml.etree.ElementTree as ET

from ..core.config import Config
from ..utils.logger import LoggerMixin
from ..core.exceptions import ValidationError, ProcessingError
from ..utils.validation_utils import validate_file_path, validate_choice
from ..utils.file_utils import write_file, write_json, write_csv, write_xml, ensure_directory


class ExportManager(LoggerMixin):
    """Manager for exporting questions to various formats."""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the export manager.
        
        Args:
            config: Configuration object
        """
        self.config = config or Config()
        self.export_config = self.config.get_export_config()
        
        self.log_info("ExportManager initialized")
    
    def export_questions(self, questions: List[Dict[str, Any]], 
                        output_path: Union[str, Path], 
                        format: str = 'json', **kwargs) -> None:
        """
        Export questions to a file in the specified format.
        
        Args:
            questions: List of question dictionaries
            output_path: Path to output file
            format: Export format ('json', 'csv', 'xml', 'qti', 'moodle')
            **kwargs: Additional format-specific parameters
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If export fails
        """
        # Validate inputs
        if not questions:
            raise ValidationError("questions", questions, "Questions list cannot be empty")
        
        supported_formats = ['json', 'csv', 'xml', 'qti', 'moodle', 'aiken', 'respondus', 'gift']
        format = validate_choice(format, supported_formats, field_name="format")
        
        # Ensure output directory exists
        output_path = Path(output_path)
        ensure_directory(output_path.parent)
        
        try:
            if format == 'json':
                self._export_json(questions, output_path, **kwargs)
            elif format == 'csv':
                self._export_csv(questions, output_path, **kwargs)
            elif format == 'xml':
                self._export_xml(questions, output_path, **kwargs)
            elif format == 'qti':
                self._export_qti(questions, output_path, **kwargs)
            elif format == 'moodle':
                self._export_moodle(questions, output_path, **kwargs)
            elif format == 'aiken':
                self._export_aiken(questions, output_path, **kwargs)
            elif format == 'respondus':
                self._export_respondus(questions, output_path, **kwargs)
            elif format == 'gift':
                self._export_gift(questions, output_path, **kwargs)
            
            self.log_info(f"Successfully exported {len(questions)} questions to {output_path}")
            
        except Exception as e:
            raise ProcessingError(
                stage="export",
                message=f"Failed to export questions to {format}: {str(e)}"
            )
    
    def _export_json(self, questions: List[Dict[str, Any]], 
                    output_path: Path, **kwargs) -> None:
        """
        Export questions to JSON format.
        
        Args:
            questions: List of question dictionaries
            output_path: Path to output file
            **kwargs: Additional parameters
        """
        export_data = {
            'metadata': {
                'export_date': datetime.now().isoformat(),
                'total_questions': len(questions),
                'format_version': '1.0',
                'generator': 'QuizAIGen'
            },
            'questions': questions
        }
        
        indent = kwargs.get('indent', 2)
        write_json(output_path, export_data, indent=indent)
    
    def _export_csv(self, questions: List[Dict[str, Any]], 
                   output_path: Path, **kwargs) -> None:
        """
        Export questions to CSV format.
        
        Args:
            questions: List of question dictionaries
            output_path: Path to output file
            **kwargs: Additional parameters
        """
        # Flatten questions for CSV format
        csv_data = []
        
        for i, question in enumerate(questions, 1):
            row = {
                'id': i,
                'question': question.get('question', ''),
                'type': question.get('type', ''),
                'answer': question.get('answer', ''),
                'difficulty': question.get('difficulty', ''),
                'confidence': question.get('confidence', ''),
                'explanation': question.get('explanation', '')
            }
            
            # Handle options for MCQ questions
            if question.get('type') == 'mcq' and question.get('options'):
                for j, option in enumerate(question['options'][:4]):  # Limit to 4 options
                    row[f'option_{chr(ord("A") + j)}'] = option
            
            # Handle keywords
            if question.get('keywords'):
                row['keywords'] = ', '.join(question['keywords'])
            
            csv_data.append(row)
        
        delimiter = kwargs.get('delimiter', ',')
        write_csv(output_path, csv_data, delimiter=delimiter)
    
    def _export_xml(self, questions: List[Dict[str, Any]], 
                   output_path: Path, **kwargs) -> None:
        """
        Export questions to XML format.
        
        Args:
            questions: List of question dictionaries
            output_path: Path to output file
            **kwargs: Additional parameters
        """
        # Create root element
        root = ET.Element('quiz')
        root.set('version', '1.0')
        root.set('generator', 'QuizAIGen')
        root.set('export_date', datetime.now().isoformat())
        
        # Add metadata
        metadata = ET.SubElement(root, 'metadata')
        ET.SubElement(metadata, 'total_questions').text = str(len(questions))
        
        # Add questions
        questions_elem = ET.SubElement(root, 'questions')
        
        for i, question in enumerate(questions, 1):
            q_elem = ET.SubElement(questions_elem, 'question')
            q_elem.set('id', str(i))
            
            # Add question elements
            ET.SubElement(q_elem, 'text').text = question.get('question', '')
            ET.SubElement(q_elem, 'type').text = question.get('type', '')
            ET.SubElement(q_elem, 'answer').text = str(question.get('answer', ''))
            
            if question.get('difficulty'):
                ET.SubElement(q_elem, 'difficulty').text = question['difficulty']
            
            if question.get('confidence'):
                ET.SubElement(q_elem, 'confidence').text = str(question['confidence'])
            
            if question.get('explanation'):
                ET.SubElement(q_elem, 'explanation').text = question['explanation']
            
            # Add options for MCQ
            if question.get('options'):
                options_elem = ET.SubElement(q_elem, 'options')
                for j, option in enumerate(question['options']):
                    option_elem = ET.SubElement(options_elem, 'option')
                    option_elem.set('id', chr(ord('A') + j))
                    option_elem.text = option
            
            # Add keywords
            if question.get('keywords'):
                keywords_elem = ET.SubElement(q_elem, 'keywords')
                for keyword in question['keywords']:
                    ET.SubElement(keywords_elem, 'keyword').text = keyword
        
        write_xml(output_path, root)
    
    def _export_qti(self, questions: List[Dict[str, Any]], 
                   output_path: Path, **kwargs) -> None:
        """
        Export questions to QTI format.
        
        Args:
            questions: List of question dictionaries
            output_path: Path to output file
            **kwargs: Additional parameters
        """
        # Create QTI XML structure
        root = ET.Element('questestinterop')
        root.set('xmlns', 'http://www.imsglobal.org/xsd/ims_qtiasiv1p2')
        
        assessment = ET.SubElement(root, 'assessment')
        assessment.set('ident', 'QuizAIGen_Assessment')
        assessment.set('title', 'Generated Quiz')
        
        # Add assessment metadata
        qtimetadata = ET.SubElement(assessment, 'qtimetadata')
        qtimetadatafield = ET.SubElement(qtimetadata, 'qtimetadatafield')
        ET.SubElement(qtimetadatafield, 'fieldlabel').text = 'cc_maxattempts'
        ET.SubElement(qtimetadatafield, 'fieldentry').text = '1'
        
        # Add section
        section = ET.SubElement(assessment, 'section')
        section.set('ident', 'root_section')
        
        for i, question in enumerate(questions, 1):
            item = ET.SubElement(section, 'item')
            item.set('ident', f'question_{i}')
            item.set('title', f'Question {i}')
            
            # Add item metadata
            itemmetadata = ET.SubElement(item, 'itemmetadata')
            qtimetadata_item = ET.SubElement(itemmetadata, 'qtimetadata')
            
            # Question type metadata
            qmd_field = ET.SubElement(qtimetadata_item, 'qtimetadatafield')
            ET.SubElement(qmd_field, 'fieldlabel').text = 'question_type'
            ET.SubElement(qmd_field, 'fieldentry').text = question.get('type', 'multiple_choice')
            
            # Add presentation
            presentation = ET.SubElement(item, 'presentation')
            material = ET.SubElement(presentation, 'material')
            mattext = ET.SubElement(material, 'mattext')
            mattext.set('texttype', 'text/html')
            mattext.text = f"<p>{question.get('question', '')}</p>"
            
            # Add response processing based on question type
            if question.get('type') == 'mcq':
                self._add_qti_mcq_response(item, question)
            elif question.get('type') == 'boolean':
                self._add_qti_boolean_response(item, question)
        
        write_xml(output_path, root)
    
    def _add_qti_mcq_response(self, item: ET.Element, question: Dict[str, Any]) -> None:
        """Add QTI response processing for MCQ questions."""
        # Add response declaration
        response_lid = ET.SubElement(item.find('presentation'), 'response_lid')
        response_lid.set('ident', 'response1')
        response_lid.set('rcardinality', 'Single')
        
        render_choice = ET.SubElement(response_lid, 'render_choice')
        
        if question.get('options'):
            for i, option in enumerate(question['options']):
                response_label = ET.SubElement(render_choice, 'response_label')
                response_label.set('ident', chr(ord('A') + i))
                material = ET.SubElement(response_label, 'material')
                mattext = ET.SubElement(material, 'mattext')
                mattext.text = option
        
        # Add response processing
        resprocessing = ET.SubElement(item, 'resprocessing')
        outcomes = ET.SubElement(resprocessing, 'outcomes')
        decvar = ET.SubElement(outcomes, 'decvar')
        decvar.set('maxvalue', '100')
        decvar.set('minvalue', '0')
        decvar.set('varname', 'SCORE')
        decvar.set('vartype', 'Decimal')
    
    def _add_qti_boolean_response(self, item: ET.Element, question: Dict[str, Any]) -> None:
        """Add QTI response processing for Boolean questions."""
        # Add response declaration for True/False
        response_lid = ET.SubElement(item.find('presentation'), 'response_lid')
        response_lid.set('ident', 'response1')
        response_lid.set('rcardinality', 'Single')
        
        render_choice = ET.SubElement(response_lid, 'render_choice')
        
        # Add True/False options
        for value, label in [('true', 'True'), ('false', 'False')]:
            response_label = ET.SubElement(render_choice, 'response_label')
            response_label.set('ident', value)
            material = ET.SubElement(response_label, 'material')
            mattext = ET.SubElement(material, 'mattext')
            mattext.text = label
    
    def _export_moodle(self, questions: List[Dict[str, Any]], 
                      output_path: Path, **kwargs) -> None:
        """
        Export questions to Moodle XML format.
        
        Args:
            questions: List of question dictionaries
            output_path: Path to output file
            **kwargs: Additional parameters
        """
        # Create Moodle XML structure
        root = ET.Element('quiz')
        
        for i, question in enumerate(questions, 1):
            q_elem = ET.SubElement(root, 'question')
            
            if question.get('type') == 'mcq':
                q_elem.set('type', 'multichoice')
            elif question.get('type') == 'boolean':
                q_elem.set('type', 'truefalse')
            else:
                q_elem.set('type', 'shortanswer')
            
            # Add question name
            name = ET.SubElement(q_elem, 'name')
            name_text = ET.SubElement(name, 'text')
            name_text.text = f'Question {i}'
            
            # Add question text
            questiontext = ET.SubElement(q_elem, 'questiontext')
            questiontext.set('format', 'html')
            qt_text = ET.SubElement(questiontext, 'text')
            qt_text.text = f"<p>{question.get('question', '')}</p>"
            
            # Add default grade
            defaultgrade = ET.SubElement(q_elem, 'defaultgrade')
            defaultgrade.text = '1.0000000'
            
            # Add penalty
            penalty = ET.SubElement(q_elem, 'penalty')
            penalty.text = '0.0000000'
            
            # Add answers based on question type
            if question.get('type') == 'mcq' and question.get('options'):
                for j, option in enumerate(question['options']):
                    answer = ET.SubElement(q_elem, 'answer')
                    
                    # Check if this is the correct answer
                    correct_answer = question.get('answer', '')
                    is_correct = (correct_answer == chr(ord('A') + j) or 
                                correct_answer == option)
                    
                    answer.set('fraction', '100' if is_correct else '0')
                    answer.set('format', 'html')
                    
                    answer_text = ET.SubElement(answer, 'text')
                    answer_text.text = f"<p>{option}</p>"
                    
                    feedback = ET.SubElement(answer, 'feedback')
                    feedback.set('format', 'html')
                    feedback_text = ET.SubElement(feedback, 'text')
                    feedback_text.text = '<p></p>'
            
            elif question.get('type') == 'boolean':
                # Add True answer
                true_answer = ET.SubElement(q_elem, 'answer')
                is_true_correct = question.get('answer', '').lower() == 'true'
                true_answer.set('fraction', '100' if is_true_correct else '0')
                true_answer.set('format', 'moodle_auto_format')
                true_text = ET.SubElement(true_answer, 'text')
                true_text.text = 'true'
                
                # Add False answer
                false_answer = ET.SubElement(q_elem, 'answer')
                false_answer.set('fraction', '0' if is_true_correct else '100')
                false_answer.set('format', 'moodle_auto_format')
                false_text = ET.SubElement(false_answer, 'text')
                false_text.text = 'false'
        
        write_xml(output_path, root)

    def _export_aiken(self, questions: List[Dict[str, Any]],
                     output_path: Path, **kwargs) -> None:
        """
        Export questions to AIKEN format.

        Args:
            questions: List of question dictionaries
            output_path: Path to output file
            **kwargs: Additional parameters
        """
        aiken_content = []

        for i, question in enumerate(questions, 1):
            if question.get('type') == 'mcq':
                # AIKEN format: Question text followed by options A-E, then ANSWER: X
                aiken_content.append(f"{question['question']}")

                options = question.get('options', [])
                option_labels = ['A', 'B', 'C', 'D', 'E']

                for j, option in enumerate(options[:5]):  # AIKEN supports up to 5 options
                    aiken_content.append(f"{option_labels[j]}. {option}")

                # Find correct answer
                correct_answer = question.get('answer', '')
                if isinstance(correct_answer, str) and correct_answer in option_labels:
                    aiken_content.append(f"ANSWER: {correct_answer}")
                elif isinstance(correct_answer, int) and 0 <= correct_answer < len(option_labels):
                    aiken_content.append(f"ANSWER: {option_labels[correct_answer]}")
                else:
                    # Default to A if no valid answer found
                    aiken_content.append("ANSWER: A")

                aiken_content.append("")  # Empty line between questions

        # Write to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(aiken_content))

    def _export_respondus(self, questions: List[Dict[str, Any]],
                         output_path: Path, **kwargs) -> None:
        """
        Export questions to Respondus format.

        Args:
            questions: List of question dictionaries
            output_path: Path to output file
            **kwargs: Additional parameters
        """
        respondus_content = []

        for i, question in enumerate(questions, 1):
            if question.get('type') == 'mcq':
                # Respondus format: Question number, question text, options with *correct
                respondus_content.append(f"{i}. {question['question']}")

                options = question.get('options', [])
                correct_answer = question.get('answer', '')

                for j, option in enumerate(options):
                    prefix = "*" if (isinstance(correct_answer, int) and j == correct_answer) or \
                                  (isinstance(correct_answer, str) and correct_answer.upper() in ['A', 'B', 'C', 'D', 'E'] and
                                   j == ord(correct_answer.upper()) - ord('A')) else ""
                    respondus_content.append(f"{prefix}{chr(ord('a') + j)}. {option}")

                respondus_content.append("")  # Empty line between questions

            elif question.get('type') == 'boolean':
                # Boolean questions in Respondus format
                respondus_content.append(f"{i}. {question['question']}")

                correct_answer = question.get('answer', '').lower()
                if correct_answer in ['true', 'yes', '1']:
                    respondus_content.append("*a. True")
                    respondus_content.append("b. False")
                else:
                    respondus_content.append("a. True")
                    respondus_content.append("*b. False")

                respondus_content.append("")

        # Write to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(respondus_content))

    def _export_gift(self, questions: List[Dict[str, Any]],
                    output_path: Path, **kwargs) -> None:
        """
        Export questions to GIFT format.

        Args:
            questions: List of question dictionaries
            output_path: Path to output file
            **kwargs: Additional parameters
        """
        gift_content = []

        for i, question in enumerate(questions, 1):
            if question.get('type') == 'mcq':
                # GIFT format: Question text {=correct ~wrong1 ~wrong2 ~wrong3}
                gift_line = question['question'] + " {"

                options = question.get('options', [])
                correct_answer = question.get('answer', '')

                # Find correct option
                correct_option = ""
                wrong_options = []

                if isinstance(correct_answer, int) and 0 <= correct_answer < len(options):
                    correct_option = options[correct_answer]
                    wrong_options = [opt for j, opt in enumerate(options) if j != correct_answer]
                elif isinstance(correct_answer, str) and correct_answer.upper() in ['A', 'B', 'C', 'D', 'E']:
                    idx = ord(correct_answer.upper()) - ord('A')
                    if 0 <= idx < len(options):
                        correct_option = options[idx]
                        wrong_options = [opt for j, opt in enumerate(options) if j != idx]

                if correct_option:
                    gift_line += f"={correct_option}"
                    for wrong in wrong_options:
                        gift_line += f" ~{wrong}"

                gift_line += "}"
                gift_content.append(gift_line)

            elif question.get('type') == 'boolean':
                # GIFT Boolean format: Question {T} or {F}
                correct_answer = question.get('answer', '').lower()
                answer_symbol = "T" if correct_answer in ['true', 'yes', '1'] else "F"
                gift_content.append(f"{question['question']} {{{answer_symbol}}}")

            elif question.get('type') in ['faq', 'short_answer']:
                # GIFT short answer format: Question {=answer}
                answer = question.get('answer', '')
                if answer:
                    gift_content.append(f"{question['question']} {{={answer}}}")

            gift_content.append("")  # Empty line between questions

        # Write to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(gift_content))

    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported export formats.
        
        Returns:
            List of supported format names
        """
        return ['json', 'csv', 'xml', 'qti', 'moodle', 'aiken', 'respondus', 'gift']
    
    def validate_export_data(self, questions: List[Dict[str, Any]]) -> bool:
        """
        Validate questions data before export.
        
        Args:
            questions: List of question dictionaries
        
        Returns:
            True if data is valid for export
        
        Raises:
            ValidationError: If validation fails
        """
        if not questions:
            raise ValidationError("questions", questions, "Questions list cannot be empty")
        
        for i, question in enumerate(questions):
            if not isinstance(question, dict):
                raise ValidationError(
                    f"questions[{i}]", question, 
                    "Each question must be a dictionary"
                )
            
            if 'question' not in question or not question['question']:
                raise ValidationError(
                    f"questions[{i}].question", question.get('question'), 
                    "Question text is required"
                )
            
            if 'type' not in question:
                raise ValidationError(
                    f"questions[{i}].type", None, 
                    "Question type is required"
                )
        
        return True
