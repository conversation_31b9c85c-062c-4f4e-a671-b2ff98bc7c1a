#!/usr/bin/env python3
"""
Advanced Export Formats Test Suite
Tests all 7 export formats with sample questions
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from quizaigen import QuestionGenerator, ExportManager

def create_sample_questions():
    """Create sample questions for export testing"""
    return [
        {
            "question": "What is the capital of France?",
            "type": "mcq",
            "answer": 0,
            "options": ["Paris", "London", "Berlin", "Madrid"],
            "explanation": "Paris is the capital and largest city of France.",
            "difficulty": "easy",
            "keywords": ["France", "capital", "geography"],
            "source_text": "France is a country in Western Europe with Paris as its capital.",
            "confidence": 0.95,
            "metadata": {"category": "geography", "topic": "capitals"}
        },
        {
            "question": "Python is a programming language.",
            "type": "boolean",
            "answer": "true",
            "explanation": "Python is indeed a high-level programming language.",
            "difficulty": "easy",
            "keywords": ["Python", "programming", "language"],
            "source_text": "Python is a high-level programming language.",
            "confidence": 0.98,
            "metadata": {"category": "technology", "topic": "programming"}
        },
        {
            "question": "The process of photosynthesis converts _____ into glucose.",
            "type": "fill_blank",
            "answer": "carbon dioxide",
            "explanation": "Photosynthesis converts carbon dioxide and water into glucose using sunlight.",
            "difficulty": "medium",
            "keywords": ["photosynthesis", "glucose", "carbon dioxide"],
            "source_text": "Photosynthesis is the process by which plants convert carbon dioxide into glucose.",
            "confidence": 0.92,
            "metadata": {"category": "biology", "topic": "photosynthesis"}
        }
    ]

def test_all_export_formats():
    """Test all 7 export formats"""
    print("🧪 Advanced Export Formats Test Suite")
    print("=" * 60)
    
    # Create sample questions
    questions = create_sample_questions()
    print(f"📝 Created {len(questions)} sample questions")
    
    # Initialize export manager
    exporter = ExportManager()
    print("✅ ExportManager initialized")
    
    # Test all formats
    formats = ['json', 'csv', 'xml', 'qti', 'moodle', 'aiken', 'respondus', 'gift']
    results = {}
    
    print("\n🔄 Testing Export Formats:")
    print("-" * 40)
    
    for fmt in formats:
        try:
            filename = f"sample_export.{fmt}"
            exporter.export_questions(questions, filename, format=fmt)
            
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"✅ {fmt.upper():10} - {size:4d} bytes - SUCCESS")
                results[fmt] = {'status': 'success', 'size': size}
                
                # Show sample content for text-based formats
                if fmt in ['aiken', 'respondus', 'gift']:
                    with open(filename, 'r', encoding='utf-8') as f:
                        content = f.read()[:200]  # First 200 chars
                        print(f"   📄 Sample: {content[:100]}...")
                
                # Clean up
                os.remove(filename)
            else:
                print(f"❌ {fmt.upper():10} - FILE NOT CREATED")
                results[fmt] = {'status': 'failed', 'size': 0}
                
        except Exception as e:
            print(f"❌ {fmt.upper():10} - ERROR: {str(e)[:50]}...")
            results[fmt] = {'status': 'error', 'size': 0}
    
    # Summary
    print("\n📊 Export Test Summary:")
    print("-" * 40)
    successful = sum(1 for r in results.values() if r['status'] == 'success')
    total_size = sum(r['size'] for r in results.values() if r['status'] == 'success')
    
    print(f"✅ Successful exports: {successful}/{len(formats)}")
    print(f"📦 Total export size: {total_size} bytes")
    print(f"🎯 Success rate: {successful/len(formats)*100:.1f}%")
    
    # Format details
    print("\n📋 Format Details:")
    for fmt, result in results.items():
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"  {status_icon} {fmt.upper():10} - {result['size']:4d} bytes")
    
    return successful == len(formats)

if __name__ == "__main__":
    success = test_all_export_formats()
    
    if success:
        print("\n🎉 ALL EXPORT FORMATS WORKING PERFECTLY!")
        print("✨ QuizAIGen Advanced Export System Complete")
    else:
        print("\n⚠️  Some export formats failed")
        sys.exit(1)
