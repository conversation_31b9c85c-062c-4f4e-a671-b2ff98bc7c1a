"""
Language Detection Framework for QuizAIGen

Provides comprehensive language detection capabilities with confidence scoring,
fallback mechanisms, and support for multiple detection libraries.
"""

from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import re
import logging

from ..utils.logger import LoggerMixin
from ..core.exceptions import ProcessingError


class SupportedLanguage(Enum):
    """Supported languages with their ISO codes."""
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    GERMAN = "de"
    ITALIAN = "it"
    PORTUGUESE = "pt"
    DUTCH = "nl"
    RUSSIAN = "ru"
    CHINESE = "zh"
    JAPANESE = "ja"
    KOREAN = "ko"
    ARABIC = "ar"


@dataclass
class LanguageDetectionResult:
    """Result of language detection."""
    language: str
    confidence: float
    detected_by: str
    alternatives: List[Tuple[str, float]]
    is_reliable: bool
    text_length: int
    
    def to_dict(self) -> Dict:
        """Convert to dictionary."""
        return {
            'language': self.language,
            'confidence': self.confidence,
            'detected_by': self.detected_by,
            'alternatives': self.alternatives,
            'is_reliable': self.is_reliable,
            'text_length': self.text_length
        }


class LanguageDetector(LoggerMixin):
    """
    Multi-library language detection system with fallback mechanisms.
    
    Uses multiple detection libraries for improved accuracy:
    1. langdetect (primary)
    2. fasttext (fallback)
    3. rule-based detection (final fallback)
    """
    
    def __init__(self, min_confidence: float = 0.7, min_text_length: int = 10):
        """
        Initialize language detector.
        
        Args:
            min_confidence: Minimum confidence threshold for reliable detection
            min_text_length: Minimum text length for detection
        """
        super().__init__()
        self.min_confidence = min_confidence
        self.min_text_length = min_text_length
        self.supported_languages = {lang.value for lang in SupportedLanguage}
        
        # Initialize detection libraries
        self._init_langdetect()
        self._init_fasttext()
        
        self.log_info("Language detector initialized")
    
    def _init_langdetect(self):
        """Initialize langdetect library."""
        try:
            import langdetect
            from langdetect import detect, detect_langs, DetectorFactory
            
            # Set seed for consistent results
            DetectorFactory.seed = 0
            
            self.langdetect = langdetect
            self.langdetect_available = True
            self.log_info("langdetect library initialized")
        except ImportError:
            self.langdetect = None
            self.langdetect_available = False
            self.log_warning("langdetect library not available")
    
    def _init_fasttext(self):
        """Initialize fasttext library."""
        try:
            import fasttext
            self.fasttext = fasttext
            self.fasttext_available = True
            self.log_info("fasttext library initialized")
        except ImportError:
            self.fasttext = None
            self.fasttext_available = False
            self.log_warning("fasttext library not available")
    
    def detect_language(self, text: str, fallback_language: str = "en") -> LanguageDetectionResult:
        """
        Detect language of the given text.
        
        Args:
            text: Text to analyze
            fallback_language: Language to use if detection fails
            
        Returns:
            LanguageDetectionResult with detection details
        """
        if not text or len(text.strip()) < self.min_text_length:
            self.log_warning(f"Text too short for reliable detection: {len(text)} chars")
            return LanguageDetectionResult(
                language=fallback_language,
                confidence=0.0,
                detected_by="fallback",
                alternatives=[],
                is_reliable=False,
                text_length=len(text)
            )
        
        # Clean text for detection
        cleaned_text = self._clean_text_for_detection(text)
        
        # Try primary detection method
        result = self._detect_with_langdetect(cleaned_text)
        if result and result.confidence >= self.min_confidence:
            return result
        
        # Try fallback detection method
        result = self._detect_with_fasttext(cleaned_text)
        if result and result.confidence >= self.min_confidence:
            return result
        
        # Try rule-based detection
        result = self._detect_with_rules(cleaned_text)
        if result:
            return result
        
        # Final fallback
        self.log_warning(f"All detection methods failed, using fallback: {fallback_language}")
        return LanguageDetectionResult(
            language=fallback_language,
            confidence=0.0,
            detected_by="final_fallback",
            alternatives=[],
            is_reliable=False,
            text_length=len(text)
        )
    
    def _detect_with_langdetect(self, text: str) -> Optional[LanguageDetectionResult]:
        """Detect language using langdetect library."""
        if not self.langdetect_available:
            return None
        
        try:
            # Get detailed detection results
            lang_probs = self.langdetect.detect_langs(text)
            
            if not lang_probs:
                return None
            
            # Get primary detection
            primary = lang_probs[0]
            language = primary.lang
            confidence = primary.prob
            
            # Get alternatives
            alternatives = [(lang.lang, lang.prob) for lang in lang_probs[1:5]]
            
            # Check if language is supported
            if language not in self.supported_languages:
                self.log_warning(f"Detected unsupported language: {language}")
                return None
            
            return LanguageDetectionResult(
                language=language,
                confidence=confidence,
                detected_by="langdetect",
                alternatives=alternatives,
                is_reliable=confidence >= self.min_confidence,
                text_length=len(text)
            )
            
        except Exception as e:
            self.log_warning(f"langdetect detection failed: {str(e)}")
            return None
    
    def _detect_with_fasttext(self, text: str) -> Optional[LanguageDetectionResult]:
        """Detect language using fasttext library."""
        if not self.fasttext_available:
            return None
        
        try:
            # Note: This would require a pre-trained fasttext model
            # For now, return None as placeholder
            self.log_debug("fasttext detection not implemented yet")
            return None
            
        except Exception as e:
            self.log_warning(f"fasttext detection failed: {str(e)}")
            return None
    
    def _detect_with_rules(self, text: str) -> Optional[LanguageDetectionResult]:
        """Rule-based language detection using character patterns."""
        try:
            # Character-based language detection patterns
            patterns = {
                'en': [
                    r'\b(the|and|or|but|in|on|at|to|for|of|with|by)\b',
                    r'\b(is|are|was|were|have|has|had|will|would|could|should)\b'
                ],
                'es': [
                    r'\b(el|la|los|las|un|una|y|o|pero|en|de|con|por|para)\b',
                    r'\b(es|son|era|fueron|tiene|tenía|será|sería)\b',
                    r'[ñáéíóúü]'
                ],
                'fr': [
                    r'\b(le|la|les|un|une|et|ou|mais|dans|de|avec|par|pour)\b',
                    r'\b(est|sont|était|étaient|a|avait|sera|serait)\b',
                    r'[àâäéèêëïîôöùûüÿç]'
                ],
                'de': [
                    r'\b(der|die|das|ein|eine|und|oder|aber|in|an|zu|für|von|mit)\b',
                    r'\b(ist|sind|war|waren|hat|hatte|wird|würde)\b',
                    r'[äöüß]'
                ]
            }
            
            text_lower = text.lower()
            scores = {}
            
            for lang, lang_patterns in patterns.items():
                score = 0
                for pattern in lang_patterns:
                    matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
                    score += matches
                
                # Normalize by text length
                scores[lang] = score / max(len(text.split()), 1)
            
            if not scores:
                return None
            
            # Get best match
            best_lang = max(scores, key=scores.get)
            confidence = min(scores[best_lang], 1.0)
            
            # Sort alternatives
            alternatives = [(lang, score) for lang, score in sorted(scores.items(), 
                          key=lambda x: x[1], reverse=True)[1:4]]
            
            return LanguageDetectionResult(
                language=best_lang,
                confidence=confidence,
                detected_by="rule_based",
                alternatives=alternatives,
                is_reliable=confidence >= 0.3,  # Lower threshold for rule-based
                text_length=len(text)
            )
            
        except Exception as e:
            self.log_warning(f"Rule-based detection failed: {str(e)}")
            return None
    
    def _clean_text_for_detection(self, text: str) -> str:
        """Clean text for better language detection."""
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove numbers and special characters for better detection
        text = re.sub(r'[0-9]+', '', text)
        text = re.sub(r'[^\w\s]', ' ', text)
        
        return text.strip()
    
    def is_language_supported(self, language: str) -> bool:
        """Check if a language is supported."""
        return language in self.supported_languages
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported language codes."""
        return list(self.supported_languages)
    
    def get_language_name(self, language_code: str) -> str:
        """Get human-readable language name from code."""
        language_names = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'nl': 'Dutch',
            'ru': 'Russian',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'ar': 'Arabic'
        }
        return language_names.get(language_code, language_code.upper())
