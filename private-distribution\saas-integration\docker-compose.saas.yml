# SaaS Integration Docker Compose for QuizAIGen
version: '3.8'

services:
  # Main SaaS Application
  quizaigen-saas:
    build:
      context: ./saas-app
      dockerfile: Dockerfile
    container_name: quizaigen-saas
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./config/saas:/app/config
      - ./data/saas:/app/data
      - ./logs/saas:/app/logs
      - saas_media:/app/media
    environment:
      - DATABASE_URL=**********************************************/saas_db
      - REDIS_URL=redis://redis:6379/0
      - QUIZAIGEN_LICENSE_SERVER=http://license-api:8000
      - QUIZAIGEN_PYPI_SERVER=http://pypi-server:8080
      - CELERY_BROKER_URL=redis://redis:6379/1
      - SECRET_KEY=${SAAS_SECRET_KEY}
      - DEBUG=false
      - ALLOWED_HOSTS=localhost,127.0.0.1,yourdomain.com
    depends_on:
      - postgres
      - redis
      - license-api
    networks:
      - quizaigen-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.saas.rule=Host(`yourdomain.com`)"
      - "traefik.http.routers.saas.tls=true"
      - "traefik.http.routers.saas.tls.certresolver=letsencrypt"

  # Celery Worker for Background Tasks
  celery-worker:
    build:
      context: ./saas-app
      dockerfile: Dockerfile
    container_name: quizaigen-celery
    restart: unless-stopped
    volumes:
      - ./config/saas:/app/config
      - ./data/saas:/app/data
      - ./logs/celery:/app/logs
      - saas_media:/app/media
    environment:
      - DATABASE_URL=**********************************************/saas_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - QUIZAIGEN_LICENSE_SERVER=http://license-api:8000
    command: celery -A saas_app worker -l info --concurrency=4
    depends_on:
      - postgres
      - redis
    networks:
      - quizaigen-network

  # Celery Beat for Scheduled Tasks
  celery-beat:
    build:
      context: ./saas-app
      dockerfile: Dockerfile
    container_name: quizaigen-celery-beat
    restart: unless-stopped
    volumes:
      - ./config/saas:/app/config
      - ./data/saas:/app/data
      - ./logs/celery:/app/logs
    environment:
      - DATABASE_URL=**********************************************/saas_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
    command: celery -A saas_app beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    depends_on:
      - postgres
      - redis
    networks:
      - quizaigen-network

  # API Gateway
  api-gateway:
    image: nginx:alpine
    container_name: quizaigen-api-gateway
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/saas.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - quizaigen-saas
      - license-api
      - customer-portal
    networks:
      - quizaigen-network

  # Load Balancer (for scaling)
  traefik:
    image: traefik:v2.10
    container_name: quizaigen-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./config/traefik:/etc/traefik
      - ./data/traefik:/data
    command:
      - --api.dashboard=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/data/acme.json
      - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
    networks:
      - quizaigen-network

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: quizaigen-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus/saas.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - quizaigen-network

  grafana:
    image: grafana/grafana:latest
    container_name: quizaigen-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/saas:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    networks:
      - quizaigen-network

  # Log Aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: quizaigen-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - quizaigen-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: quizaigen-logstash
    restart: unless-stopped
    volumes:
      - ./config/logstash:/usr/share/logstash/pipeline
      - ./logs:/usr/share/logstash/logs
    depends_on:
      - elasticsearch
    networks:
      - quizaigen-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: quizaigen-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - quizaigen-network

  # Backup Service
  backup-service:
    build:
      context: ./backup-service
      dockerfile: Dockerfile
    container_name: quizaigen-backup
    restart: unless-stopped
    volumes:
      - ./data:/backup/data
      - ./config/backup:/app/config
      - backup_storage:/backup/storage
    environment:
      - DATABASE_URL=**********************************************/saas_db
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - S3_BUCKET=${BACKUP_S3_BUCKET}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
    depends_on:
      - postgres
    networks:
      - quizaigen-network

  # Health Check Service
  health-checker:
    build:
      context: ./health-checker
      dockerfile: Dockerfile
    container_name: quizaigen-health
    restart: unless-stopped
    volumes:
      - ./config/health:/app/config
    environment:
      - SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
      - EMAIL_ALERTS_ENABLED=true
      - CHECK_INTERVAL=60  # seconds
    networks:
      - quizaigen-network

networks:
  quizaigen-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  saas_media:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
  backup_storage:
