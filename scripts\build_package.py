#!/usr/bin/env python3
"""
Build script for QuizAIGen package.

This script handles the complete build process including:
- Cleaning previous builds
- Running tests
- Building the package
- Validating the build
"""

import subprocess
import shutil
import sys
from pathlib import Path
import argparse


def run_command(cmd, check=True, capture_output=True):
    """Run a shell command and return the result."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=capture_output, text=True)
    
    if check and result.returncode != 0:
        print(f"❌ Command failed with exit code {result.returncode}")
        if result.stderr:
            print(f"Error: {result.stderr}")
        sys.exit(1)
    
    return result


def clean_build():
    """Clean previous build artifacts."""
    print("🧹 Cleaning previous builds...")
    
    dirs_to_clean = ['dist', 'build', 'quizaigen.egg-info']
    files_to_clean = ['*.egg-info']
    
    for dir_name in dirs_to_clean:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"  ✅ Cleaned {dir_name}/")
    
    # Clean any .egg-info directories
    for egg_info in Path('.').glob('*.egg-info'):
        if egg_info.is_dir():
            shutil.rmtree(egg_info)
            print(f"  ✅ Cleaned {egg_info}")
    
    print("  ✅ Build cleanup completed")


def run_tests():
    """Run the test suite."""
    print("🧪 Running tests...")
    
    try:
        result = run_command(["python", "-m", "pytest", "tests/", "-v", "--tb=short"])
        print("  ✅ All tests passed")
        return True
    except SystemExit:
        print("  ❌ Tests failed")
        return False


def run_quality_checks():
    """Run code quality checks."""
    print("🔍 Running quality checks...")
    
    checks = [
        (["python", "-m", "black", "--check", "quizaigen/", "tests/"], "Code formatting (black)"),
        (["python", "-m", "isort", "--check-only", "quizaigen/", "tests/"], "Import sorting (isort)"),
        (["python", "-m", "flake8", "quizaigen/", "tests/"], "Code linting (flake8)"),
    ]
    
    all_passed = True
    
    for cmd, description in checks:
        try:
            run_command(cmd)
            print(f"  ✅ {description} passed")
        except SystemExit:
            print(f"  ❌ {description} failed")
            all_passed = False
    
    return all_passed


def build_package():
    """Build the package."""
    print("🔨 Building package...")
    
    try:
        result = run_command(["python", "-m", "build"])
        print("  ✅ Package built successfully")
        
        # List generated files
        dist_dir = Path("dist")
        if dist_dir.exists():
            print("  📦 Generated files:")
            for file in dist_dir.glob("*"):
                size_mb = file.stat().st_size / (1024 * 1024)
                print(f"     {file.name} ({size_mb:.1f} MB)")
        
        return True
    except SystemExit:
        print("  ❌ Package build failed")
        return False


def validate_package():
    """Validate the built package."""
    print("✅ Validating package...")
    
    try:
        # Check package with twine
        result = run_command(["python", "-m", "twine", "check", "dist/*"])
        print("  ✅ Package validation passed")
        
        # Run package validator if available
        validator_path = Path("scripts/package_validator.py")
        if validator_path.exists():
            result = run_command(["python", str(validator_path)])
            print("  ✅ Custom validation passed")
        
        return True
    except SystemExit:
        print("  ❌ Package validation failed")
        return False


def test_installation():
    """Test package installation in a virtual environment."""
    print("🧪 Testing package installation...")
    
    import tempfile
    import venv
    
    with tempfile.TemporaryDirectory() as temp_dir:
        venv_path = Path(temp_dir) / "test_env"
        
        try:
            # Create virtual environment
            print("  Creating test environment...")
            venv.create(venv_path, with_pip=True)
            
            # Get paths for the virtual environment
            if sys.platform == "win32":
                python_exe = venv_path / "Scripts" / "python.exe"
                pip_exe = venv_path / "Scripts" / "pip.exe"
            else:
                python_exe = venv_path / "bin" / "python"
                pip_exe = venv_path / "bin" / "pip"
            
            # Install the package
            print("  Installing package in test environment...")
            wheel_files = list(Path("dist").glob("*.whl"))
            if not wheel_files:
                print("  ❌ No wheel file found")
                return False
            
            wheel_file = wheel_files[0]
            run_command([str(pip_exe), "install", str(wheel_file)])
            
            # Test import
            print("  Testing package import...")
            test_script = """
import quizaigen
print(f"QuizAIGen version: {quizaigen.__version__}")
print("Import successful!")
"""
            
            result = run_command([str(python_exe), "-c", test_script])
            print("  ✅ Package installation test passed")
            return True
            
        except Exception as e:
            print(f"  ❌ Installation test failed: {e}")
            return False


def main():
    """Main build function."""
    parser = argparse.ArgumentParser(description="Build QuizAIGen package")
    parser.add_argument("--skip-tests", action="store_true", help="Skip running tests")
    parser.add_argument("--skip-quality", action="store_true", help="Skip quality checks")
    parser.add_argument("--skip-validation", action="store_true", help="Skip package validation")
    parser.add_argument("--skip-install-test", action="store_true", help="Skip installation test")
    parser.add_argument("--clean-only", action="store_true", help="Only clean build artifacts")
    
    args = parser.parse_args()
    
    print("QuizAIGen Package Builder")
    print("=" * 40)
    
    # Always clean first
    clean_build()
    
    if args.clean_only:
        print("\n✅ Clean completed")
        return
    
    success = True
    
    # Run tests
    if not args.skip_tests:
        if not run_tests():
            print("\n❌ Build failed: Tests did not pass")
            success = False
    
    # Run quality checks
    if not args.skip_quality and success:
        if not run_quality_checks():
            print("\n⚠️  Quality checks failed, but continuing build...")
            # Don't fail the build for quality issues
    
    # Build package
    if success:
        if not build_package():
            success = False
    
    # Validate package
    if not args.skip_validation and success:
        if not validate_package():
            success = False
    
    # Test installation
    if not args.skip_install_test and success:
        if not test_installation():
            print("\n⚠️  Installation test failed, but package was built")
    
    # Summary
    print("\n" + "=" * 40)
    if success:
        print("✅ Build completed successfully!")
        print("\nNext steps:")
        print("  1. Review generated files in dist/")
        print("  2. Test with: python scripts/pypi_publisher.py --test-pypi")
        print("  3. Publish with: python scripts/pypi_publisher.py --publish")
    else:
        print("❌ Build failed!")
        print("Please fix the issues and try again.")
        sys.exit(1)


if __name__ == "__main__":
    main()