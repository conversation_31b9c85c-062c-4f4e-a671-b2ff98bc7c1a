"""
Test suite for Multi-Language Support in QuizAIGen

Tests language detection, multilingual templates, NLP models,
and question generation in multiple languages.
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock

from quizaigen.core.language_detector import LanguageDetector, LanguageDetectionResult, SupportedLanguage
from quizaigen.templates.multilingual_templates import MultilingualTemplates, QuestionType, QuestionTemplate
from quizaigen.models.multilingual_nlp import MultilingualNLPManager, MultilingualModelConfig


class TestLanguageDetector(unittest.TestCase):
    """Test language detection functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.detector = LanguageDetector(min_confidence=0.7, min_text_length=10)
    
    def test_detector_initialization(self):
        """Test language detector initialization."""
        self.assertEqual(self.detector.min_confidence, 0.7)
        self.assertEqual(self.detector.min_text_length, 10)
        self.assertIsInstance(self.detector.supported_languages, set)
        self.assertIn('en', self.detector.supported_languages)
        self.assertIn('es', self.detector.supported_languages)
    
    def test_detect_english_text(self):
        """Test detection of English text."""
        text = "Python is a high-level programming language created by <PERSON> van <PERSON>um in 1991."
        result = self.detector.detect_language(text)
        
        self.assertIsInstance(result, LanguageDetectionResult)
        self.assertEqual(result.language, 'en')
        self.assertGreater(result.text_length, 0)
        self.assertIn(result.detected_by, ['langdetect', 'rule_based', 'fallback'])
    
    def test_detect_spanish_text(self):
        """Test detection of Spanish text."""
        text = "Python es un lenguaje de programación de alto nivel creado por Guido van Rossum en 1991."
        result = self.detector.detect_language(text)
        
        self.assertIsInstance(result, LanguageDetectionResult)
        # Should detect Spanish or fallback to English
        self.assertIn(result.language, ['es', 'en'])
        self.assertGreater(result.text_length, 0)
    
    def test_detect_french_text(self):
        """Test detection of French text."""
        text = "Python est un langage de programmation de haut niveau créé par Guido van Rossum en 1991."
        result = self.detector.detect_language(text)
        
        self.assertIsInstance(result, LanguageDetectionResult)
        # Should detect French or fallback to English
        self.assertIn(result.language, ['fr', 'en'])
        self.assertGreater(result.text_length, 0)
    
    def test_detect_german_text(self):
        """Test detection of German text."""
        text = "Python ist eine hochrangige Programmiersprache, die 1991 von Guido van Rossum erstellt wurde."
        result = self.detector.detect_language(text)
        
        self.assertIsInstance(result, LanguageDetectionResult)
        # Should detect German or fallback to English
        self.assertIn(result.language, ['de', 'en'])
        self.assertGreater(result.text_length, 0)
    
    def test_short_text_fallback(self):
        """Test fallback for short text."""
        text = "Hi"
        result = self.detector.detect_language(text, fallback_language='es')
        
        self.assertEqual(result.language, 'es')
        self.assertEqual(result.confidence, 0.0)
        self.assertEqual(result.detected_by, 'fallback')
        self.assertFalse(result.is_reliable)
    
    def test_empty_text_fallback(self):
        """Test fallback for empty text."""
        text = ""
        result = self.detector.detect_language(text)
        
        self.assertEqual(result.language, 'en')
        self.assertEqual(result.confidence, 0.0)
        self.assertEqual(result.detected_by, 'fallback')
        self.assertFalse(result.is_reliable)
    
    def test_supported_languages(self):
        """Test supported language methods."""
        self.assertTrue(self.detector.is_language_supported('en'))
        self.assertTrue(self.detector.is_language_supported('es'))
        self.assertFalse(self.detector.is_language_supported('xyz'))
        
        supported = self.detector.get_supported_languages()
        self.assertIsInstance(supported, list)
        self.assertIn('en', supported)
        self.assertIn('es', supported)
    
    def test_language_names(self):
        """Test language name retrieval."""
        self.assertEqual(self.detector.get_language_name('en'), 'English')
        self.assertEqual(self.detector.get_language_name('es'), 'Spanish')
        self.assertEqual(self.detector.get_language_name('xyz'), 'XYZ')


class TestMultilingualTemplates(unittest.TestCase):
    """Test multilingual template functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.templates = MultilingualTemplates()
    
    def test_template_initialization(self):
        """Test template system initialization."""
        self.assertIsInstance(self.templates.templates, dict)
        self.assertIn('en', self.templates.templates)
        self.assertIn('es', self.templates.templates)
        self.assertIn('fr', self.templates.templates)
        self.assertIn('de', self.templates.templates)
    
    def test_english_templates(self):
        """Test English template retrieval."""
        mcq_templates = self.templates.get_templates('en', QuestionType.MCQ.value)
        self.assertIsInstance(mcq_templates, list)
        self.assertGreater(len(mcq_templates), 0)
        
        template = mcq_templates[0]
        self.assertIsInstance(template, QuestionTemplate)
        self.assertEqual(template.language, 'en')
        self.assertEqual(template.question_type, 'mcq')
    
    def test_spanish_templates(self):
        """Test Spanish template retrieval."""
        faq_templates = self.templates.get_templates('es', QuestionType.FAQ.value)
        self.assertIsInstance(faq_templates, list)
        self.assertGreater(len(faq_templates), 0)
        
        template = faq_templates[0]
        self.assertIsInstance(template, QuestionTemplate)
        self.assertEqual(template.language, 'es')
        self.assertEqual(template.question_type, 'faq')
        self.assertIn('¿', template.pattern)  # Spanish question mark
    
    def test_french_templates(self):
        """Test French template retrieval."""
        boolean_templates = self.templates.get_templates('fr', QuestionType.BOOLEAN.value)
        self.assertIsInstance(boolean_templates, list)
        self.assertGreater(len(boolean_templates), 0)
        
        template = boolean_templates[0]
        self.assertIsInstance(template, QuestionTemplate)
        self.assertEqual(template.language, 'fr')
        self.assertEqual(template.question_type, 'boolean')
    
    def test_german_templates(self):
        """Test German template retrieval."""
        fill_blank_templates = self.templates.get_templates('de', QuestionType.FILL_BLANK.value)
        self.assertIsInstance(fill_blank_templates, list)
        self.assertGreater(len(fill_blank_templates), 0)
        
        template = fill_blank_templates[0]
        self.assertIsInstance(template, QuestionTemplate)
        self.assertEqual(template.language, 'de')
        self.assertEqual(template.question_type, 'fill_blank')
    
    def test_template_formatting(self):
        """Test template formatting functionality."""
        template = QuestionTemplate(
            pattern="What is {subject}?",
            placeholders=["subject"],
            question_type="mcq",
            language="en"
        )
        
        formatted = template.format(subject="Python")
        self.assertEqual(formatted, "What is Python?")
    
    def test_random_template_selection(self):
        """Test random template selection."""
        template = self.templates.get_random_template('en', QuestionType.MCQ.value)
        self.assertIsInstance(template, QuestionTemplate)
        self.assertEqual(template.language, 'en')
        self.assertEqual(template.question_type, 'mcq')
    
    def test_unsupported_language_fallback(self):
        """Test fallback for unsupported language."""
        templates = self.templates.get_templates('xyz', QuestionType.MCQ.value)
        # Should fallback to English templates
        self.assertIsInstance(templates, list)
        if templates:  # If English templates are available
            self.assertEqual(templates[0].language, 'en')
    
    def test_unsupported_question_type(self):
        """Test handling of unsupported question type."""
        templates = self.templates.get_templates('en', 'unsupported_type')
        self.assertEqual(templates, [])
        
        template = self.templates.get_random_template('en', 'unsupported_type')
        self.assertIsNone(template)


class TestMultilingualNLPManager(unittest.TestCase):
    """Test multilingual NLP model management."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.nlp_manager = MultilingualNLPManager()
    
    def test_manager_initialization(self):
        """Test NLP manager initialization."""
        self.assertIsInstance(self.nlp_manager.model_configs, dict)
        self.assertIn('en', self.nlp_manager.model_configs)
        self.assertIn('es', self.nlp_manager.model_configs)
        self.assertIn('fr', self.nlp_manager.model_configs)
        self.assertIn('de', self.nlp_manager.model_configs)
    
    def test_model_configuration(self):
        """Test model configuration structure."""
        en_config = self.nlp_manager.model_configs['en']
        self.assertIsInstance(en_config, MultilingualModelConfig)
        self.assertEqual(en_config.language, 'en')
        self.assertIsInstance(en_config.spacy_model, str)
        self.assertIsInstance(en_config.transformers_model, str)
    
    def test_supported_languages(self):
        """Test supported language methods."""
        supported = self.nlp_manager.get_supported_languages()
        self.assertIsInstance(supported, list)
        self.assertIn('en', supported)
        self.assertIn('es', supported)
        self.assertIn('fr', supported)
        self.assertIn('de', supported)
    
    def test_model_availability_check(self):
        """Test model availability checking."""
        # This test depends on which models are actually installed
        available = self.nlp_manager.get_available_languages()
        self.assertIsInstance(available, list)
        
        # At least English should be available in most setups
        if 'en' in available:
            self.assertTrue(self.nlp_manager.is_model_available('en'))
    
    def test_model_info_retrieval(self):
        """Test model information retrieval."""
        info = self.nlp_manager.get_model_info('en')
        self.assertIsInstance(info, dict)
        self.assertIn('language', info)
        self.assertIn('spacy_model', info)
        self.assertIn('transformers_model', info)
        self.assertIn('available', info)
        self.assertIn('loaded', info)
        
        all_info = self.nlp_manager.get_all_model_info()
        self.assertIsInstance(all_info, dict)
        self.assertIn('en', all_info)
    
    @patch('spacy.load')
    def test_model_loading_success(self, mock_spacy_load):
        """Test successful model loading."""
        mock_nlp = Mock()
        mock_spacy_load.return_value = mock_nlp
        
        # Set model as available
        self.nlp_manager.model_configs['en'].available = True
        
        result = self.nlp_manager.load_language_model('en')
        self.assertTrue(result)
        self.assertTrue(self.nlp_manager.is_model_loaded('en'))
    
    def test_model_loading_unsupported_language(self):
        """Test model loading for unsupported language."""
        result = self.nlp_manager.load_language_model('xyz')
        self.assertFalse(result)
    
    @patch('spacy.load')
    def test_text_processing(self, mock_spacy_load):
        """Test text processing functionality."""
        # Mock spaCy document
        mock_doc = Mock()
        mock_doc.ents = []
        mock_doc.sents = [Mock(text="Test sentence.")]
        mock_doc.noun_chunks = []
        
        # Mock spaCy model
        mock_nlp = Mock()
        mock_nlp.return_value = mock_doc
        mock_spacy_load.return_value = mock_nlp
        
        # Set model as available and load it
        self.nlp_manager.model_configs['en'].available = True
        self.nlp_manager.load_language_model('en')
        
        # Test text processing
        result = self.nlp_manager.process_text("Test text", 'en')
        self.assertIsNotNone(result)
        
        # Test sentence extraction
        sentences = self.nlp_manager.get_sentences("Test text", 'en')
        self.assertIsInstance(sentences, list)
        
        # Test entity extraction
        entities = self.nlp_manager.extract_entities("Test text", 'en')
        self.assertIsInstance(entities, list)
        
        # Test noun phrase extraction
        noun_phrases = self.nlp_manager.get_noun_phrases("Test text", 'en')
        self.assertIsInstance(noun_phrases, list)
    
    def test_model_unloading(self):
        """Test model unloading functionality."""
        # This test assumes no models are loaded initially
        result = self.nlp_manager.unload_language_model('en')
        self.assertTrue(result)  # Should succeed even if not loaded
        
        # Test clearing all models
        self.nlp_manager.clear_all_models()
        loaded = self.nlp_manager.get_loaded_languages()
        self.assertEqual(len(loaded), 0)


class TestMultilingualIntegration(unittest.TestCase):
    """Test integration between multilingual components."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.detector = LanguageDetector()
        self.templates = MultilingualTemplates()
        self.nlp_manager = MultilingualNLPManager()
    
    def test_language_detection_and_template_selection(self):
        """Test integration between language detection and template selection."""
        # Test with English text
        text = "Python is a programming language."
        detection_result = self.detector.detect_language(text)
        
        templates = self.templates.get_templates(
            detection_result.language, 
            QuestionType.MCQ.value
        )
        self.assertIsInstance(templates, list)
        
        if templates:
            template = templates[0]
            self.assertIsInstance(template, QuestionTemplate)
    
    def test_multilingual_workflow(self):
        """Test complete multilingual workflow."""
        test_texts = {
            'en': "Python is a high-level programming language.",
            'es': "Python es un lenguaje de programación de alto nivel.",
            'fr': "Python est un langage de programmation de haut niveau.",
            'de': "Python ist eine hochrangige Programmiersprache."
        }
        
        for expected_lang, text in test_texts.items():
            # Detect language
            detection_result = self.detector.detect_language(text)
            detected_lang = detection_result.language
            
            # Get templates for detected language
            templates = self.templates.get_templates(detected_lang, QuestionType.FAQ.value)
            self.assertIsInstance(templates, list)
            
            # Check if NLP model is available
            model_available = self.nlp_manager.is_model_available(detected_lang)
            self.assertIsInstance(model_available, bool)


if __name__ == '__main__':
    unittest.main()
